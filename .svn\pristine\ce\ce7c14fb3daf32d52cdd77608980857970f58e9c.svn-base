import dash_bootstrap_components as dbc
import dash_uploader as du
import numpy as np
import pandas as pd
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import Input, Output, State, callback, dash, dcc, html
from dash_tabulator import DashTabulator

from common import read_sql
from config import UPLOAD_FOLDER_ROOT

dash.register_page(__name__, title="交叉料表")

uploader = du.Upload(
    id="dash-uploader",
    text="点击上传YPAN SAP BOM",
    filetypes=["xls", "xlsx"],
    # default_style={"border-color": "rgba(26, 188, 156)", "min-height": "100%"},
)

content = [
    html.Br(),
    uploader,
    html.Br(),
    html.Div(id="critical-part-output"),
    dcc.Download(id="critical-part-download"),
]

layout = dbc.Container(
    [
        dbc.Tabs(
            [
                dbc.Tab(content, label="交叉料表", tab_id="tab-1"),
            ],
            id="tab-num",
            active_tab="tab-1",
            style={"color": "#16a085"},
        )
    ],
    fluid=True,
    className="ml-3 pr-5",
)


# ---------回调函数----------------
@callback(
    Output("critical-part-output", "children"),
    Input("dash-uploader", "isCompleted"),
    State("dash-uploader", "fileNames"),
    State("dash-uploader", "upload_id"),
)
def upload_file(iscompleted, filenames, upload_id):
    if not iscompleted or not filenames:
        raise PreventUpdate

    file = UPLOAD_FOLDER_ROOT / upload_id / filenames[0]

    df = pd.read_excel(
        file,
        usecols=[
            "PARENT LEVEL P/N",
            "PART NO",
            "DESCRIPTION",
            "VENDOR NAME",
            "MFG PART",
            "DESIGN NO",
            "%",
        ],
        dtype=str,
        keep_default_na=False,
    )

    df = df.rename(
        columns={
            "PARENT LEVEL P/N": "PARENT",
            "PART NO": "Delta Part Number",
            "DESCRIPTION": "Component Description",
            "VENDOR NAME": "Supplier Name",
            "MFG PART": "Supplier Part Number",
            "DESIGN NO": "Design Number",
        }
    )
    df["Quantity"] = ""
    c1 = df["Design Number"] != ""
    c2 = df["Delta Part Number"].str.startswith(("0", "1", "2"))
    c3 = df["%"].isin(["", "100"])
    df = df.loc[c1 & c2 & c3]

    # conn = pool.connection()
    sql = "select * from ssp_ext.critical_part"
    df1 = read_sql(sql)
    params = df["Delta Part Number"].unique().tolist()
    sql = "select deltapn,des from ssp_csg.mat_info where deltapn in %s"
    dfm = read_sql(sql, params=[params])
    # conn.close()
    dfm = dfm.drop_duplicates("deltapn")
    df = df.merge(dfm, left_on="Delta Part Number", right_on="deltapn", how="left")
    df["Component Description"] = np.where(
        df["des"].notna(), df["des"], df["Component Description"]
    )

    df["smd_dip"] = np.where(
        df["Component Description"].str.contains("smd", case=False, na=False),
        "SMD",
        "HI",
    )

    df["remark"] = df["PARENT"] + "/" + df["smd_dip"]
    df = df.drop_duplicates(["Design Number", "remark"])

    dfx = []
    for i in df1.itertuples():
        c1 = df["Delta Part Number"].str.startswith(i.match_deltapn)
        c2 = df["Component Description"].str.contains(i.match_des, na=False, case=False)
        dfi = df.loc[c1 & c2]
        if not dfi.empty:
            if i.match_des:
                dfc = dfi["Component Description"].str.extract(i.match_des)
                dfc.columns = ["value", "unit"]
                dfc["unit"] = (
                    dfc["unit"].replace("", 1).replace("m", 0.001).replace("M", 1000)
                )
                dfc["value"] = dfc["value"].astype(int)
                c3 = dfc.eval(f"value*unit{i.cond_des}")
                dfi = dfi.loc[c3]

            dfi["Delta PN"] = i.deltapn
            dfi["Description"] = i.des
            dfi["Type"] = i.type
            dfx.append(dfi)
    dfx = pd.concat(dfx)

    dfx = dfx.melt(
        id_vars=["Delta PN", "Description", "Type", "Design Number", "remark"],
        value_vars=[
            "Delta Part Number",
            "Supplier Name",
            "Supplier Part Number",
            "Component Description",
            "Quantity",
        ],
        var_name="var",
        value_name="value",
    )
    # dfx = dfx.set_index(["Delta PN", "Description", "Type", "Design Number", "remark"])
    # dfx = dfx.melt(
    #     id_vars=["Delta PN", "Description", "Type", "Design Number", "remark"],
    #     value_vars=[
    #         "Delta Part Number",
    #         "Supplier Name",
    #         "Supplier Part Number",
    #         "Component Description",
    #         "Quantity",
    #     ],
    #     var_name="var",
    #     value_name="value",
    #     ignore_index=False,
    # )

    dfx["var"] = (
        dfx["var"]
        .astype("category")
        .cat.set_categories(
            [
                "Delta Part Number",
                "Supplier Name",
                "Supplier Part Number",
                "Component Description",
                "Quantity",
            ]
        )
    )
    dfx = dfx.sort_values(["Design Number", "var"])

    col = [
        "Delta PN",
        "Description",
        "Type",
        "Design Number",
        "var",
        "value",
        "remark",
    ]
    dfx = dfx.reindex(columns=col)
    table = DashTabulator(
        id="critical-part-table",
        theme="tabulator_site",
        options={
            "selectable": 1,
            "height": "350px",
            # "groupBy": ["Delta PN", "Description", "Type", "Design Number"],
        },
        data=dfx.to_dict(orient="records"),
        columns=[{"title": i, "field": i} for i in dfx.columns],
        # downloadButtonType={
        #     "css": "btn btn-sm btn-outline-primary",
        #     "text": "Export",
        #     "type": "xlsx",
        # },
    )
    output = [dbc.Button("Export", id="critical-part-btn", size="sm"), table]
    return output


@callback(
    Output("critical-part-download", "data"),
    Input("critical-part-btn", "n_clicks"),
    State("critical-part-table", "data"),
)
def download_file(n_clicks, data):
    if not n_clicks:
        raise PreventUpdate

    df = pd.DataFrame(data)
    df[["a", "b", "c"]] = ""

    df = pd.concat([df, df["remark"].str.split("/", expand=True)], axis=1)
    df[1] = df[1].astype("category").cat.set_categories(["SMD", "HI"])
    df = df.sort_values(by=[0, 1])
    df = df.set_index(
        ["Delta PN", "Description", "a", "Type", "b", "Design Number", "remark", "c"]
    )
    return dcc.send_data_frame(df.to_excel, "critical_part.xlsx")
