# -*- coding: utf-8 -*-
import os
import shutil
import time
import warnings
from datetime import datetime, timedelta
from pathlib import Path

import numpy as np
import pandas as pd
import polars as pl
import psycopg2
import requests
from exchangelib import (
    Account,
    Configuration,
    Credentials,
    FileAttachment,
    HTMLBody,
    Mailbox,
    Message,
)
from huey import CancelExecution, RedisExpireHuey, crontab
from huey.signals import SIGNAL_ERROR

from common import (
    df_add_mat_category,
    df_insert,
    df_to_html,
    read_sql,
    safety_stock_handler,
    pur_etd,
)
from config import BOM_DIR, SSP_DIR, cache, pg_conn, pool
from server import server

# from huey.contrib.mini import MiniHuey
# huey3 = MiniHuey()
# huey3.start()


MAIL_USER = os.getenv("MAIL_USER")
MAIL_PASSWORD = os.getenv("MAIL_PASSWORD")
creds = Credentials(username=MAIL_USER, password=MAIL_PASSWORD)
config = Configuration(server="mail.deltaww.com.cn", credentials=creds)
REDIS_HOST = os.environ.get("REDIS_HOST")
huey = RedisExpireHuey(host=REDIS_HOST, utc=False)
huey_label = RedisExpireHuey(name="label", host=REDIS_HOST, utc=False)
huey_ai = RedisExpireHuey(name="ai", host=REDIS_HOST, utc=False)
pd.options.mode.chained_assignment = None

if os.getenv("MYSQL_TCP_PORT") == "3306":
    huey.immediate = False
else:
    huey.immediate = int(os.getenv("HUEY_IMMEDIATE"))


# * -------------打印标签------------------
@huey_label.task()
def bg_label_print(data: str):
    pass


def execute_once_per_half_hour(func):
    last_execution_time = 0

    def wrapper(*args, **kwargs):
        nonlocal last_execution_time

        # 获取当前时间
        current_time = time.time()

        # 如果距离上次执行时间超过半小时，则执行函数
        if current_time - last_execution_time > 30 * 60:
            last_execution_time = current_time
            return func(*args, **kwargs)

    return wrapper


# *-------------发邮件------------------
@huey.task(retries=3)
def bg_mail(to: str, subject: str, body: str = "", cc: str = "", attach: list = None):
    """[summary]
    Parameters
    ----------
    to : str
        [收件人]
    subject : str
        [主题]]
    body : str, optional
        [正文], by default ''
    cc : str, optional
        [抄送], by default ''
    attach : List[str], optional
        [附件,路径列表], by default None
    """
    account = Account(
        primary_smtp_address=f"{MAIL_USER}@deltaww.com",
        credentials=creds,
        autodiscover=False,
        config=config,
    )
    m = Message(
        account=account,
        subject=subject,
        body=HTMLBody(body),
        to_recipients=[Mailbox(email_address=i) for i in to.split(";")],
        cc_recipients=[i for i in cc.split(";")] if cc else cc,
    )
    if attach:
        for i in attach:
            f = FileAttachment(
                name=i.name, content=i.read_bytes()
            )  # , content='qq'.encode('utf-8')
            m.attach(f)
    m.send()


@huey.task()
def bg_access_record(user: dict, interface: str, action: str, keywords: str = None):
    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = (
                "insert into ssp.access_record(dept,owner,interface,action,date,area,keywords)\
                values(%s,%s,%s,%s,%s,%s,%s)"
            )
            params = [
                user.get("dept"),
                user.get("nt_name"),
                interface,
                action,
                datetime.now(),
                user.get("area"),
                keywords,
            ]
            cu.execute(sql, params)
            conn.commit()


@huey.task()
def bg_tools_short(id: str, area: str, mailto: str):
    sql = "select id,品名,描述,厂商,数量,安全库存,单次采购量,区域,部门专用 from ssp_asset.tool_list where id=%s"
    params = [id]
    df1 = read_sql(sql, params=params)

    sql = "SELECT 料号ID as id,需求数量,已到数量 FROM ssp_asset.tool_short where 料号ID=%s and 申请状态=%s"
    params = [id, "Ongoing"]
    df2 = read_sql(sql, params=params)
    df2 = df2.groupby("id", as_index=False).agg({"需求数量": sum, "已到数量": sum})
    df = df1.merge(df2, on="id", how="left")
    df = df.fillna(0)

    if ((df["数量"] + df["需求数量"] - df["已到数量"]) < df["安全库存"]).any():
        with pool.connection() as conn:
            with conn.cursor() as cu:
                sql = "insert into ssp_asset.tool_short\
                    (料号ID,需求数量,已到数量,申请状态,申请时间,区域)\
                    values(%s,%s,%s,%s,%s,%s)"
                params = [id, df["单次采购量"][0], 0, "Ongoing", datetime.now(), area]
                cu.execute(sql, params)
                conn.commit()
                subject = "工具缺料补货通知"
                # 品名	描述	厂商	区域	部门专用	安全库存	单次采购量
                cols = [
                    "品名",
                    "描述",
                    "厂商",
                    "区域",
                    "部门专用",
                    "安全库存",
                    "单次采购量",
                ]
                body = df[cols].to_html()
                bg_mail(mailto, subject, body)


@huey.periodic_task(crontab(hour="1", minute="0"))
# @server.route("/often", methods=["GET"])
def task_often_material():
    """定期生成常用料清单"""
    today = datetime.now().strftime("%Y%m%d")
    sql = "Select Dept,Category,Common,Sub_Group,DeltaPN,DES,MFGNAME,MFGPN,Industry_Grade,Status,DeleteRemark,BlockRemark from ssp_ce.a_oftenMaterial"
    df = read_sql(sql)
    depts = df["Dept"].unique().tolist()
    depts.remove("LGT")
    sql = (
        "select distinct ce_database_name,tp_download_order from ssp_ce.a_db_head_list"
    )
    order = read_sql(sql)
    order = order.sort_values(by="tp_download_order")
    order = order["ce_database_name"].str.upper()

    sql = 'select concat_ws("_",dept_group,dept_name) as dept,dept_group from ssp.dept'
    df_dept = read_sql(sql)
    df_dept = df_dept.loc[df_dept["dept_group"].isin(depts)]

    for d in df_dept.itertuples():
        df1 = df.loc[df["Dept"] == d.dept_group]
        df1["Common"] = df1["Common"].astype("category").cat.set_categories(["Y", "NO"])
        df1 = df1.sort_values(by=["Sub_Group", "Common"])
        fp = SSP_DIR / "program" / "EXTERNAL" / f"{d.dept}"
        for i in fp.glob("*常用料*.xlsx"):
            try:
                os.remove(i)
            except Exception:
                pass
        with pd.ExcelWriter(fp / f"{d.dept}常用料{today}.xlsx") as writer:
            df1.to_excel(
                writer, sheet_name="OftenMaterial", index=False, freeze_panes=(1, 1)
            )
            df1["Category"] = df1["Category"].str.upper()
            cats = df1["Category"].unique()
            cats = order.loc[order.isin(cats)]
            cats = cats.unique()

            df1 = df1.loc[~df1["Status"].str.lower().isin(["delete", "block", ""])]
            for cat in cats:
                sql = f"select * from ssp_ce.{cat}"
                df2 = read_sql(sql)
                df_cat = df1.loc[
                    df1["Category"] == cat,
                    ["Sub_Group", "Common", "DeltaPN", "DES", "MFGNAME", "MFGPN"],
                ]
                df3 = df_cat.merge(
                    df2, left_on="DeltaPN", right_on="Delta_PN", how="left"
                )
                df3["Description"] = df3["DES"]
                df3["MFG_NAME"] = df3["MFGNAME"]
                df3["MFG_PN"] = df3["MFGPN"]
                if not df3.empty:
                    df3 = df3.drop(
                        ["ID", "Delta_PN", "DES", "MFGNAME", "MFGPN"], axis=1
                    )
                    df3["Common"] = (
                        df3["Common"].astype("category").cat.set_categories(["Y", "NO"])
                    )
                    df3 = df3.sort_values(by=["Sub_Group", "Common"])
                    df3.to_excel(
                        writer, sheet_name=cat, index=False, freeze_panes=(1, 1)
                    )
        shutil.copy(
            fp / f"{d.dept}常用料{today}.xlsx",
            SSP_DIR / "program" / "ce" / f"常用料{d.dept}.xlsx",
        )
    # conn.close()


@huey.periodic_task(crontab(hour="1", minute="0", day_of_week="1-5"))
# @server.route("/task/check_list", methods=["GET"])
def panasonic_check_list():
    fp = SSP_DIR / "program" / "EXTERNAL" / "LGT_LGT" / "PanasonicCheckList"
    dfs = []
    for i in fp.glob("*"):
        if i.stem != "CheckList汇总":
            if "MLCC" in i.stem or "贴片电阻" in i.stem:
                dfi = pd.read_excel(i, dtype=str).iloc[:2, 5:].T.dropna()
                dfi.columns = ["mfgpn", "mfgname"]
                dfi["mfgpn"] = np.where(
                    dfi["mfgname"] == "TAIYO YUDEN",
                    "%" + dfi["mfgpn"] + "%",
                    dfi["mfgpn"] + "%",
                )
                params = dfi.values.tolist()
                cond = " or ".join(["(mfgpn like %s and mfgname=%s)"] * len(params))
                sql = f"select deltapn,des,mfgname,mfgpn from ssp_csg.mat_info where {cond}"
                params = [j for i in params for j in i]
                df = read_sql(sql, params=params)
            else:
                params = pd.read_excel(i, dtype=str).iloc[0, 5:].dropna().tolist()
                sql = "select deltapn,des,mfgname,mfgpn from ssp_csg.mat_info where deltapn in %s"
                df = read_sql(sql, params=(params,))
            df.insert(0, "type", i.stem)
            dfs.append(df)

    # conn.close()
    dfa = pd.concat(dfs)
    dfa["modified_date"] = datetime.now()
    dfa.to_excel(fp / "CheckList汇总.xlsx", index=False)


@huey.periodic_task(crontab(hour="9", minute="0", day_of_week="1-5"))
def task_meeting_due_day_alert_mail():
    now = datetime.now().date()
    day3 = datetime.now() + pd.offsets.Day(3)
    sql = "Select recorded_by,subject,category,action_item_description,action_item_update,\
        owner,due_day,approver,status from ssp_ext.meeting where due_day<%s and status!=%s"
    df = read_sql(sql, params=[day3, "close"])

    if not df.empty:
        for i in df.itertuples():
            df = pd.DataFrame([i])
            body = df_to_html(
                df,
                href="http://sup.deltaww.com/apps/meeting",
                link_text="进入会议记录系统",
            )
            if i.status == "processing":
                to = f"{i.approver}@deltaww.com"
            else:
                to = f"{i.owner}@deltaww.com"
            prefix = "【任务逾期】" if now > i.due_day else "【任务提醒】"
            subject = f"{prefix}{i.subject}"
            bg_mail(to, subject, body)


# @huey.periodic_task(crontab(hour="9", minute="0", day="*/2", day_of_week="1-5"))
# @server.route("/spec_mail_will_overdue", methods=["GET"])
def task_spec_will_overdue_mail():
    sql = (
        "SELECT c.id,c.dept,c.doc_type,model,input_date,request_date,d.status,f.due_day,\
        g.owner as dept_owner,ee,me,spec,c.owner as pm \
        FROM ssp_spec.task c left JOIN (SELECT a.task_id,a.status FROM ssp_spec.modification \
        a LEFT JOIN ssp_spec.modification b on a.task_id = b.task_id \
        AND a.modified_date < b.modified_date WHERE b.modified_date is NULL) d \
        on c.id=d.task_id left join ssp_spec.due_day f on c.dept=f.dept and c.doc_type=f.doc_type\
        left join ssp_spec.duty g on c.dept=g.dept"
    )
    df = read_sql(sql)
    # conn.close()
    if df.empty:
        return "没有需要邮件提醒的即将逾期任务,请添加"

    df = df.drop_duplicates("id")
    now = datetime.now().date()
    df["due_date"] = (
        df["due_day"].fillna(1).astype(int).apply(lambda x: pd.offsets.BDay(x))
    )
    df["due_date"] = (df["input_date"] + df["due_date"]).dt.date
    df = df.loc[(df["due_date"] - pd.offsets.BDay(1)).dt.date == now]
    if df.empty:
        return "没有需要邮件提醒的即将逾期任务,请添加"

    df["status"] = df["status"].fillna("Open")
    df_ee = df.loc[df["status"] == "Submitted_EE"]  # 发给EE
    df_me = df.loc[df["status"] == "Submitted_ME"]  # 发给ME
    df_open = df.loc[df["status"] == "Open"]  # 发给Spec Dept owner
    df_spec = df.loc[
        ~df["status"].isin(["Open", "Submitted_ME", "Submitted_EE"])
    ]  # 发给SPEC

    if not df_ee.empty:
        for i in df_ee["ee"].unique():
            dfi = df_ee.loc[df_ee["ee"] == i]
            dfi = dfi.reindex(
                columns=["doc_type", "model", "status", "input_date", "request_date"]
            )
            dfi.columns = ["文件类型", "机种名称", "状态", "创建日期", "需求日期"]
            body = df_to_html(dfi, title=f"Dear {i}:")
            to = f"{i}@deltaww.com;<EMAIL>;<EMAIL>"
            subject = "SSP-即将逾期，请立即确认签核"
            bg_mail(to, subject, body)

    if not df_me.empty:
        for i in df_me["me"].unique():
            dfi = df_me.loc[df_me["me"] == i]
            dfi = dfi.reindex(
                columns=["doc_type", "model", "status", "input_date", "request_date"]
            )
            dfi.columns = ["文件类型", "机种名称", "状态", "创建日期", "需求日期"]
            body = df_to_html(dfi, title=f"Dear {i}:")
            to = f"{i}@deltaww.com;<EMAIL>;<EMAIL>"
            subject = "SSP-即将逾期，请立即确认签核"
            bg_mail(to, subject, body)

    if not df_open.empty:
        for i in df_open["dept_owner"].unique():
            dfi = df_open.loc[df_open["dept_owner"] == i]
            dfi = dfi.reindex(
                columns=["doc_type", "model", "status", "input_date", "request_date"]
            )
            dfi.columns = ["文件类型", "机种名称", "状态", "创建日期", "需求日期"]
            body = df_to_html(dfi, title=f"Dear {i}:")
            to = f"{i}@deltaww.com;<EMAIL>;<EMAIL>"
            subject = "SSP-即将逾期，请立即确认签核"
            bg_mail(to, subject, body)

    if not df_spec.empty:
        for i in df_spec["spec"].unique():
            dfi = df_spec.loc[df_spec["spec"] == i]
            dfi = dfi.reindex(
                columns=["doc_type", "model", "status", "input_date", "request_date"]
            )
            dfi.columns = ["文件类型", "机种名称", "状态", "创建日期", "需求日期"]
            body = df_to_html(dfi, title=f"Dear {i}:")
            to = f"{i}@deltaww.com;<EMAIL>;<EMAIL>"
            subject = "SSP-即将逾期，请立即确认签核"
            bg_mail(to, subject, body)

    return "请查收邮箱确认即将逾期"


# @huey.periodic_task(crontab(hour="9", minute="0", day="*/2", day_of_week="1-5"))
# @server.route("/spec_mail_overdue", methods=["GET"])
def task_spec_overdue_mail():
    """[早上9点，已逾期delay通知<br>以下发给PM]
    Returns
    -------
    [type]
        [description]
    """
    sql = (
        "SELECT c.id,c.dept,c.doc_type,model,input_date,request_date,d.status,f.due_day,\
        g.owner as dept_owner,ee,me,spec,c.owner as pm \
        FROM ssp_spec.task c left JOIN (SELECT a.task_id,a.status FROM ssp_spec.modification \
        a LEFT JOIN ssp_spec.modification b on a.task_id = b.task_id \
        AND a.modified_date < b.modified_date WHERE b.modified_date is NULL) d \
        on c.id=d.task_id left join ssp_spec.due_day f on c.dept=f.dept and c.doc_type=f.doc_type\
        left join ssp_spec.duty g on c.dept=g.dept"
    )
    df = read_sql(sql)
    if df.empty:
        return "没有需要邮件提醒的已逾期任务,请添加"

    df = df.drop_duplicates("id")
    now = datetime.now().date()
    df["due_date"] = (
        df["due_day"].fillna(1).astype(int).apply(lambda x: pd.offsets.BDay(x))
    )
    df["due_date"] = (df["input_date"] + df["due_date"]).dt.date
    df = df.loc[df["due_date"] < now]
    if df.empty:
        return "没有需要邮件提醒的已逾期任务,请添加"
    df = df.reindex(
        columns=[
            "doc_type",
            "model",
            "status",
            "input_date",
            "request_date",
            "spec",
            "pm",
        ]
    )
    df["input_date"] = df["input_date"].dt.date
    df.columns = ["文件类型", "机种名称", "状态", "创建日期", "需求日期", "规格", "PM"]
    for i in df["PM"].unique():
        dfi = df.loc[df["PM"] == i]
        body = df_to_html(dfi, title=f"Dear {i}:")
        to = f"{i}@deltaww.com;<EMAIL>;<EMAIL>"
        subject = "SSP-逾期通知,请知晓"
        bg_mail(to, subject, body)
    return "请查收邮箱确认已逾期"


# @huey.periodic_task(crontab(hour="9", minute="0", day="*/2", day_of_week="1-5"))
# @server.route("/spec_mail_running_date", methods=["GET"])
def task_spec_running_date():
    """[早上9点,running_date为当天的,发提醒通知给owner]

    Returns
    -------
    [type]
        [description]
    """
    sql = (
        "SELECT Status,Owner,DocType,Model,ECNNo,Running_Date,Input_Date,Release_Date,Followup_Date \
        from ssp_spec.ecn_sm where date(running_date)=curdate()"
    )
    df = read_sql(sql)

    if df.empty:
        return "没有需要邮件提醒的running_date任务,请添加"

    for i in df["Owner"].unique():
        dfi = df.loc[df["Owner"] == i]
        body = df_to_html(dfi, title=f"Dear {i}:")
        to = f"{i}@deltaww.com;<EMAIL>;<EMAIL>"
        subject = "SSP-RunningDate 提醒，请注意"
        bg_mail(to, subject, body)

    return "请查收邮箱确认running_date"


@huey.periodic_task(crontab(hour="1", minute="0", day="1"))
# @server.route("/delete_db_backup", methods=["GET"])
def delete_db_backup():
    """[一个月删除一次数据库备份]"""
    fp = Path(os.getenv("DB_BACKUP_DIR"))
    last_month = datetime.now() - timedelta(days=180)
    del_list = [
        i
        for i in fp.rglob("*")
        if datetime.utcfromtimestamp(i.stat().st_mtime) < last_month
    ]
    if del_list:
        for i in del_list:
            i.unlink()


@huey.task()
def task_pur_update(df: pd.DataFrame, owner: str):
    columns = ["item_pur", "up_type", "remark1", "remark2", "up_date", "owner"]
    df = df.reindex(columns=columns)
    df["up_date"] = datetime.now()
    df["owner"] = owner
    params = df.fillna("").values.tolist()
    sql = "insert into ssp.pur_update(item_pur,up_type,remark1,remark2,up_date,owner) \
        values(%s,%s,%s,%s,%s,%s)"

    with pool.connection() as conn:
        with conn.cursor() as cu:
            cu.executemany(sql, params)
            conn.commit()


@huey.periodic_task(crontab(hour="1", minute="0"))
def update_pur_when_prt_close():
    """挂在王波、李伟明名下的材料，样制结束后没有提供的请直接close,采购备注：”样制结束，材料缺料作业“"""
    sql = (
        "update pur SET pur_status='closed',pur_remark=CONCAT_WS(',',pur_remark,now(),'样制结束,材料缺料作业') \
        WHERE pur IN ('weiming.li','bo.sm.wang') AND pur_status NOT IN ('closed','cancel') \
            AND prtno IN (SELECT prtno FROM prt WHERE smstatus IN ('close','cancel'))"
    )
    with pool.connection() as conn:
        with conn.cursor() as cu:
            cu.execute(sql)
            conn.commit()
    return "更新完成"


@huey.periodic_task(crontab(hour="2", minute="0"))
# @server.route("/test", methods=["GET"])
def check_287_miss():
    """
    检查出库记录287磁组材料id标记是否有遗漏
    """
    sql = "SELECT id FROM stockout \
        WHERE deltapn LIKE %s \
        AND deltapn IN (SELECT deltapn FROM ssp_ext.mag)  \
        AND bom_id IS NOT null \
        AND mag_stock_list IS NULL"
    params = ["287%"]
    with pool.connection() as conn:
        with conn.cursor() as cu:
            cu.execute(sql, params)
            res = cu.fetchone()
            if res:
                raise Exception("287磁组材料id标记有遗漏")


@huey.periodic_task(crontab(day="1,15", hour="2", minute="0"))
@server.route("/task_order_safety_stock", methods=["GET"])
def task_order_safety_stock():
    sql = "select area,checkcode,dept,prtno,dept_id from ssp.stockout \
    where stockoutdate>=date_sub(curdate(),interval 1 week) \
    and type in %s and dept_id not in %s"
    params = [["sm", "debug"], [10, 14, 15, 13, 16, 31]]
    stockout = read_sql(sql, params=params)
    if stockout.empty:
        return

    stockout = stockout.groupby(["area", "checkcode"], as_index=False).last()
    check_code_list = stockout["checkcode"].unique().tolist()
    params = ["processing", check_code_list]
    sql = "select * from ssp.safetystock where status=%s and checkcode in %s"
    ss = read_sql(sql, params=params)

    ss = safety_stock_handler(ss)
    order = ss[ss["order_qty"] > 0]

    prtno_list = stockout["prtno"].unique().tolist()
    sql = "select prtno,proj,ee as rd from prt where prtno in %s"
    prt = read_sql(sql, params=[prtno_list])
    order = order.merge(stockout, on=["area", "checkcode"], how="inner").merge(
        prt, on="prtno", how="left"
    )
    if order.empty:
        return "本次没有需要安全库存的订单"

    now = datetime.now()
    order = order.reset_index(drop=True)
    order["item_pur"] = int(f"{datetime.now():%y%m%d%H%M%S%f}")
    order["item_pur"] = order["item_pur"] + range(order.shape[0])
    order["rd"] = np.where(order["rd"].isna(), order["prtno"], order["rd"])
    order["prtno"] = np.where(
        order["prtno"].str.startswith(("HZ", "SH")),
        order["prtno"],
        order["area"] + "SS",
    )
    order["application"] = "Stock"
    order["start_date"] = now.strftime("%Y-%m-%d %H:%M:%S")
    order["req_date"] = (now + timedelta(days=30)).strftime("%Y-%m-%d %H:%M:%S")
    order["r_qty"] = order["order_qty"]
    order["pur_status"] = "transferred"
    order["dest_area"] = order["area"]
    order = order.rename(columns={"order_qty": "qty", "id": "safetystock_id"})
    order = df_add_mat_category(order)
    order = order.rename(columns={"cat1": "mat_catelogue", "cat2": "mat_group"})

    order.to_excel(
        SSP_DIR / "3. Document backup" / "SafetyStock" / f"{now:%Y%m%d%H%M%S}.xlsx"
    )
    df_insert("ssp.pur", order)
    return f"{order}"


@huey.periodic_task(crontab(day_of_week=5, hour="3", minute="0"))
@server.route("/task_add_safety_stock", methods=["GET"])
def task_add_safety_stock():
    """
    增加状态为”TBD”的安全库存数据，满足“部门包含SDC,HDC，DPEC，且半年使用次数>=3次”且来源为“Auto”的
    1年内无出入库记录的自动从安全库存Cancel，并在“特殊说明”备注“1年内未使用”
    """
    now = datetime.now()
    half_year = (now - timedelta(days=180)).date()
    sql = "SELECT a.area,a.checkcode,a.deltapn,c.des,c.mfgname,c.mfgpn \
    from ssp.stockout a \
    left JOIN (select id,area,checkcode from safetystock)b \
    ON a.Area=b.area AND a.checkcode=b.checkcode \
    left JOIN (select deltapn,des,mfgname,mfgpn from ssp_csg.mat_info)c \
    ON a.deltapn=c.deltapn\
    WHERE stockoutdate>=%s AND \
    dept_id IN (SELECT id FROM ssp.dept WHERE category IN (%s,%s) OR id=%s) \
    AND b.id IS null group BY a.area,a.checkcode HAVING COUNT(*)>=%s"
    params = [half_year, "HDC", "SDC", 12, 3]
    dfh = read_sql(sql, params=params)
    c1 = dfh["area"] == "HZ"
    c2 = dfh["des"].str.contains("SMD")
    c3 = dfh["deltapn"].str.match("^\d")
    dfh = dfh.loc[~(c1 & c2 & c3)]

    if not dfh.empty:
        dfh["status"] = "TBD"
        dfh["addtype"] = "Auto"
        params = dfh.values.tolist()
        ph1 = ",".join(dfh.columns)
        ph2 = ",".join(["%s"] * dfh.columns.size)
        sql = f"insert into ssp.safetystock({ph1}) values({ph2})"
        with pool.connection() as conn:
            with conn.cursor() as cu:
                cu.executemany(sql, params)
                conn.commit()

    # 1年内无出入库记录的自动从安全库存Cancel，并在“特殊说明”备注“1年内未使用”
    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = "update safetystock a LEFT JOIN \
                (SELECT DISTINCT checkcode FROM stockout \
                WHERE stockoutdate>=DATE_SUB(CURDATE(), INTERVAL 1 YEAR) \
                union SELECT DISTINCT checkcode FROM stockin \
                WHERE StockInDate>=DATE_SUB(CURDATE(), INTERVAL 1 YEAR))b \
                ON a.checkcode=b.checkcode \
                set memo=%s,STATUS=%s,canceldate=%s \
                WHERE a.status=%s and b.checkcode is null \
                and a.adddate<DATE_SUB(CURDATE(), INTERVAL 1 YEAR)"
            params = [f"一年内未使用{now}", "Cancel", now, "Processing"]
            cu.execute(sql, params)
            conn.commit()

            sql = "update safetystock a \
                JOIN (SELECT max(stockoutdate) AS stockoutdate,area,checkcode \
                FROM stockout WHERE stockoutdate>=DATE_SUB(CURDATE(), INTERVAL 1 week) \
                AND dept_id IN (SELECT id FROM dept WHERE purchasing_permit='Y') \
                GROUP BY AREA,checkcode) b \
                ON a.checkcode=b.checkcode AND a.area=b.area \
                set STATUS='Processing',memo=CONCAT_WS(',',memo,'再次使用',now()) \
                WHERE STATUS='cancel' AND memo LIKE '%一年内未使用%'"
            cu.execute(sql)
            conn.commit()

            sql = "update stock a JOIN \
                (SELECT area,checkcode,GROUP_CONCAT(ifnull(limituse,'')) AS limituse \
                from safetystock WHERE STATUS='processing' \
                GROUP BY AREA,checkcode HAVING limituse!='')b \
                ON a.Area=b.area AND a.checkcode=b.checkcode \
                SET a.limituse=b.limituse WHERE a.LimitUse!=b.limituse"
            cu.execute(sql)
            conn.commit()

    return "部门包含SDC,HDC，DPEC，且半年使用次数>=3次"


@huey.task()
def task_import_uuid_to_my300(df: pd.DataFrame):
    df.columns = df.columns.str.lower()
    df["deltapn"] = df["checkcode"]
    df["deltapn"] = df["deltapn"].fillna("NA")

    df = df.reindex(columns=["id", "deltapn", "prtno", "qty", "owner"])

    df["prtno"] = np.where(df["prtno"].isnull(), df["deltapn"], df["prtno"])
    df["prtno"] = df["prtno"].str.replace("\W+", ",", regex=True)
    df["prtno"] = df["prtno"].str.replace("[\u4e00-\u9fa5]", "", regex=True)
    df["prtno"] = df["prtno"].str.strip()

    df["deltapn"] = df["deltapn"].str.replace("'", "")
    df["deltapn"] = df["deltapn"].str.replace("[\u4e00-\u9fa5]", "", regex=True)
    df["deltapn"] = df["deltapn"].str.strip()
    df["deltapn"] = np.where(
        df["deltapn"].str.startswith(("E", "M", "R")),
        df["prtno"].str.cat(df["deltapn"], sep="-"),
        df["deltapn"],
    )

    df["qty"] = pd.to_numeric(df["qty"], errors="coerce").fillna(9999).astype(int)

    try:
        conn = psycopg2.connect(**pg_conn)
        cu = conn.cursor()
        sql = "select name,magazinetype,deftapeangle,defsteplength \
            from mydbcompview_10.component"

        with warnings.catch_warnings():
            warnings.simplefilter("ignore", UserWarning)
            comp = pd.read_sql(sql, conn)

        comp = comp.drop_duplicates(["name"])
        comp["deftapeangle"] = (comp["deftapeangle"] / 1000).astype("object")
        comp["defsteplength"] = comp["defsteplength"].astype("object")

        df = df.merge(comp, left_on="deltapn", right_on="name", how="left")
        df["created"] = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
        df["aux1"] = df["prtno"]
        df["aux2"] = df["owner"]
        df["magazinetype"] = df["magazinetype"].fillna("UNKNOWN")
        df["deftapeangle"] = df["deftapeangle"].fillna("-90")
        df["defsteplength"] = df["defsteplength"].fillna("2000")
        df = df.reindex(
            columns=[
                "id",
                "deltapn",
                "prtno",
                "magazinetype",
                "deftapeangle",
                "defsteplength",
                "created",
                "qty",
                "aux1",
                "aux2",
            ]
        )

        sql = "INSERT INTO mydbcarrview_10.carrier_magname \
            (carrierid, componentname, batchid, carriertype,angle,step,\
                created,quantity,aux1,aux2)\
            VALUES(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)"
        params = df.values.tolist()

        cu.executemany(sql, params)
        conn.commit()
        cu.close()
        conn.close()
    except Exception as e:
        to = "<EMAIL>"
        subject = "MY300导入失败"
        body = str(e)
        bg_mail(to, subject, body + df.to_html())


@huey.task()
def task_bom_result(
    df: pd.DataFrame,
    start_date: datetime,
    source: str,
    prtno: str,
    prt_id: str,
    bom_id: str,
):
    if source == "change":
        sql = "select deltapn,checkcode,des,mfgname,mfgpn,designno,source,bomtype \
            from ssp.bom_change where bom_id=%s"
    else:
        sql = "select deltapn,checkcode,des,mfgname,mfgpn,designno,source,bomtype \
            from ssp.bom_initial where bom_id=%s"

    df1 = read_sql(sql, params=[bom_id])
    df1.dropna(axis=1, how="all", inplace=True)

    sql = "select deltapn,des,mfgname,mfgpn,qty,start_date,mat_remark,\
        pur_status from ssp.pur where prt_id=%s and application=%s"
    df2 = read_sql(sql, params=[prt_id, "project"])
    df2 = df2.loc[df2["start_date"] == start_date]

    sql = "select deltapn,checkcode,des,mfgname,mfgpn,designno,stockno,packaging,\
        up_date from ssp.smbom where prt_id=%s"
    df3 = read_sql(sql, params=[prt_id])

    if not df2.empty:
        designno = df3.groupby("deltapn", as_index=False).agg(
            {"designno": lambda x: ",".join(set(x))}
        )
        df2 = df2.merge(designno, on="deltapn", how="left")

    xls = [
        {"sheet_name": "原始BOM", "data": df1},
        {"sheet_name": "缺料清单", "data": df2},
        {"sheet_name": "样制BOM", "data": df3},
    ]

    folder = BOM_DIR / prtno
    if not folder.exists():
        folder.mkdir()
    df.to_excel(
        folder / f"{prtno}_计算过程{datetime.now():%y%m%d%H%M%S}.xlsx", index=False
    )

    xls_file = folder / f"{prtno}_扣库结果{datetime.now():%y%m%d%H%M%S}.xlsx"
    with pd.ExcelWriter(xls_file, engine="xlsxwriter") as writer:
        for i in xls:
            dfi = i["data"]
            dfi.to_excel(writer, sheet_name=i["sheet_name"], index=False)
            worksheet = writer.sheets[i["sheet_name"]]  # pull worksheet object
            if not dfi.empty:
                for i, col in enumerate(dfi.columns):
                    width = max(dfi[col].apply(lambda x: len(str(x))).max(), len(col))
                    worksheet.set_column(i, i, width)
        writer.save()
    return xls_file


@huey.pre_execute("execute_once_per_half_hour")
def pre_execute_hook(task):
    if task.name == "task_express_delivery":
        if any(i.name == "task_express_delivery" for i in huey.scheduled()):
            raise CancelExecution("只需执行1次")


@huey.task()
def task_express_delivery(nt_name, express_date):
    to = [nt_name, "weiming.li", "lili.fang", "cc.zhu", "FANWEI.XIE"]
    to = ";".join(f"{i}@deltaww.com" for i in to)
    subject = "【快递寄出通知】"
    sql = "select prtno,dept,proj,pcbpn,me,ee,pm,qty,express_qty,express_date \
        from ssp.prt where express_date>=%s"
    df = read_sql(sql, params=[express_date])
    df = df.rename(
        columns={
            "express_date": "寄出时间",
            "express_qty": "今日寄出数量",
            "qty": "总数量",
        }
    )
    bg_mail(to, subject, df.to_html())


@huey.task()
def task_put_on_shelf(bin: str, color: str = "Red"):
    json = {
        "Bin": bin,
        "LightColor": color,
        "AskOrder": "AskBlinkLamp",
        "PDAID": "PDA001",
        "PSID": "48112966",
        "Plant": None,
    }
    try:
        res = requests.post(os.getenv("SHELF_URL"), json=json, timeout=1)
    except Exception as e:  # noqa: F841
        return {"RetCode": -1}
    if res.status_code == 502:
        return {"RetCode": res.status_code}
    return res.json()


@huey.task()
def task_take_off_shelf(binlist: list[str], color: str = "Yellow"):
    """
    LightColor 灯色 (Red/Green/Yellow/Gray)Gray为灭灯指令
    """
    json = {
        "BinList": binlist,
        "LightColor": color,
        "AskOrder": "AskTakeOnOffLamp",
        "PDAID": "PC001",
        "PSID": "48112966",
        "Plant": None,
    }
    try:
        res = requests.post(os.getenv("SHELF_URL"), json=json, timeout=1)
    except Exception as e:  # noqa: F841
        return {"RetCode": -1}
    if res.status_code == 502:
        return {"RetCode": res.status_code}
    return res.json()


@huey_ai.task()
def task_ai_sql(question: str):
    pass


@huey.periodic_task(crontab(hour="3", minute="0"))
def task_pur_etd():
    pur = pur_etd()
    pur1 = pur.filter(pl.col("es_gt_pcb"))
    rate = 1 - (pur1.height / pur.height)
    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = "insert into ssp.pur_etd_rate(rate) values(%s)"
            params = [rate]
            cu.execute(sql, params)
            conn.commit()


# @huey.periodic_task(crontab(hour="3", minute="0"))
def task_delete_my300_carrier():
    sql = "select prtno from prt where smstatus in %s"
    df = read_sql(sql, params=[("close", "cancel")])
    prtno = df["prtno"].tolist()
    conn = psycopg2.connect(**pg_conn)
    params = ",".join(f"'{i}'" for i in prtno)
    sql = f"delete from mydbcarrview_10.carrier_magname where aux1 in ({params})"
    cu = conn.cursor()
    cu.execute(sql)
    conn.commit()
    conn.close()


@huey.periodic_task(crontab(hour="8-17", minute="0"))
def task_download_prt():
    p = SSP_DIR / "3. Document backup" / "SampleMaking" / "prt.xlsx"
    sql = "select *from ssp.prt where appdate>='2023-01-01'"
    df = read_sql(sql)
    df.to_excel(p, index=False)


@huey.periodic_task(crontab(minute="*/10"))
def rename_failed_label():
    label_dir = SSP_DIR / "3. Document backup" / "label"
    for i in label_dir.rglob("*.failed"):
        i.rename(i.with_suffix(".csv"))


# @huey.periodic_task(crontab(hour="3", minute="0"))
# def duckdb_cache():
#     dk.sql("CREATE OR REPLACE TABLE _stock AS from my.stock")
#     dk.sql("CREATE OR REPLACE TABLE _stockin AS FROM my.stockin")
#     dk.sql("CREATE OR REPLACE TABLE _stockout AS FROM my.stockout")


# @huey.task()
# def path_task(path, name, *args, **kwargs):
#     spec = importlib.util.spec_from_file_location(path, path)
#     foo = importlib.util.module_from_spec(spec)
#     return getattr(foo, name)(*args, **kwargs)  # Call the function.


# ----------signal-------------------
@huey.signal(SIGNAL_ERROR)
def task_error_handler(signal, task, exc=None):
    to = "<EMAIL>"
    subject = f"【SSP TASK ERROR】{task.name}"
    body = exc
    bg_mail(to, subject, body)


@server.route("/cache/refresh", methods=["GET"])
def cache_refresh():
    cache.evict("user")
    return "refresh ok"


# @server.before_first_request
# @server.route("/cache/refresh", methods=["GET"])
# def duckdb_cache():
#     sql = "select nt_name,dept_id,area,onno,role_group,dept from ssp.user where termdate is null"
#     df = read_sql(sql)
#     df["nt_name"] = df["nt_name"].str.title()
#     dk.sql("CREATE OR REPLACE TABLE user AS SELECT * FROM df")
#     dcon.create_table("user", df)
#     return ""


# @huey3.periodic_task(crontab(minute="*"))
# @server.before_first_request
# def task_register_duck():
#     sql = "select * from ce.auto_motive"
#     auto_motive = read_sql(sql)
#     sql = "select * from ce.flammability"
#     flammability = read_sql(sql)
#     sql = "select * from ce.life_time"
#     life_time = read_sql(sql)
#     sql = "select * from ce.operation_temp"
#     operation_temp = read_sql(sql)
#     sql = "select application,deltapn,des,mfgname,mfgpn,prtno,pur_remark,dept,pur,mat_remark,rd \
#         from ssp.pur where pur_status in %s and start_date>=date_sub(now(),interval 1 year)"
#     params = [["closed", "cancel"]]
#     pur_closed = read_sql(sql, params=params)
#     dk.sql("CREATE OR REPLACE TABLE auto_motive AS SELECT * FROM auto_motive")
#     dk.sql("CREATE OR REPLACE TABLE flammability AS SELECT * FROM flammability")
#     dk.sql("CREATE OR REPLACE TABLE life_time AS SELECT * FROM life_time")
#     dk.sql("CREATE OR REPLACE TABLE operation_temp AS SELECT * FROM operation_temp")
#     dk.sql("CREATE OR REPLACE TABLE pur_closed AS SELECT * FROM pur_closed")
#     print("task_register_duck")


# @huey3.periodic_task(crontab(minute="*"))
# def test():
#     print("test")
