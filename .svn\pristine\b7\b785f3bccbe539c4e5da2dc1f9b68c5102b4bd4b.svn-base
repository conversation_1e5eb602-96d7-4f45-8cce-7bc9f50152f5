# -*- coding: utf-8 -*-
from datetime import datetime
from urllib.parse import parse_qsl, urlsplit

import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
import feffery_antd_components as fac
import feffery_utils_components.alias as fuc
import pandas as pd
from dash import ctx, html
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import ALL, Input, Output, State, callback, no_update

from common import ce_cats, ce_mat_category, df_to_html, get_db, id_factory, read_sql
from components.notice import notice
from config import IMG_TYPE, UPLOAD_FOLDER_ROOT, rule
from tasks import bg_mail

id = id_factory(__name__)

ce_owner = rule.get_decision("ce_owner.json")


def form_part_1(task):
    db = get_db()
    cats = ce_cats()
    sql = "select nt_name,dept_id,role_group from ssp.user where termdate is null"
    user = read_sql(sql)
    user["nt_name"] = user["nt_name"].str.title()
    select_data = user["nt_name"].tolist()
    applicant = task.get("applicant").title()
    role_group = user.loc[user["nt_name"] == applicant.title(), "role_group"].iloc[0]
    if role_group == "CE":
        applicant_disabled = False
    else:
        applicant_disabled = True

    fa = db.find_one("ce.fa", {"task_id": task.get("id")}) or {}
    cc = task.get("cc")
    if not cc:
        cc = []
    else:
        cc = cc.split(",")

    form_part_1 = [
        dmc.Center(dmc.Text("元件失效分析申请单", weight=700)),
        dmc.Divider(),
        dmc.Grid(
            [
                dmc.Col(
                    dmc.Select(
                        label="申请人(Applicant)",
                        withAsterisk=True,
                        size="xs",
                        id=id("applicant"),
                        value=applicant,
                        data=select_data,
                        disabled=applicant_disabled,
                        searchable=True,
                    ),
                    span=3,
                ),
                dmc.Col(
                    dmc.MultiSelect(
                        label="抄送(Cc)",
                        size="xs",
                        data=select_data + cc,
                        id=id("cc"),
                        value=cc,
                        searchable=True,
                    ),
                    span=9,
                ),
                dmc.Col(
                    dmc.TextInput(
                        label="主题(Purpose)",
                        withAsterisk=True,
                        size="xs",
                        id={"type": id("form-input-1"), "index": 0},
                        value=fa.get("purpose"),
                    ),
                    span=12,
                ),
                dmc.Col(
                    dmc.Select(
                        label="发生地点(Place of Occurrence)",
                        placeholder="Select one",
                        id={"type": id("form-input-1"), "index": 1},
                        data=[
                            {"value": "debug", "label": "Debug"},
                            {"value": "qe_test", "label": "QE_Test"},
                            {"value": "customer", "label": "Customer"},
                            {"value": "plant", "label": "Plant"},
                            {"value": "others", "label": "Others"},
                        ],
                        size="xs",
                        withAsterisk=True,
                        value=fa.get("place"),
                    ),
                    span=4,
                ),
                dmc.Col(
                    dmc.Select(
                        label="地区(Region)",
                        placeholder="Select one",
                        id={"type": id("form-input-1"), "index": 2},
                        data=[
                            {"value": "thailand", "label": "Thailand"},
                            {"value": "wuhu", "label": "Wuhu"},
                            {"value": "wujiang", "label": "WuJiang"},
                            {"value": "dongguan", "label": "Dongguan"},
                        ],
                        size="xs",
                        value=fa.get("region"),
                    ),
                    span=4,
                ),
                dmc.Col(
                    dmc.TextInput(
                        label="客户(Customer)",
                        id={"type": id("form-input-1"), "index": 3},
                        size="xs",
                        withAsterisk=True,
                        value=fa.get("customer"),
                    ),
                    span=4,
                ),
                dmc.Col(
                    dmc.TextInput(
                        label="使用机种(Model)",
                        id={"type": id("form-input-1"), "index": 4},
                        size="xs",
                        withAsterisk=True,
                        value=fa.get("model"),
                    ),
                    span=3,
                ),
                dmc.Col(
                    dmc.Select(
                        label="机种阶段(Model Stage)",
                        placeholder="Select one",
                        id={"type": id("form-input-1"), "index": 5},
                        data=[
                            {"value": "evt", "label": "EVT"},
                            {"value": "dvt", "label": "DVT"},
                            {"value": "pvt", "label": "PVT"},
                            {"value": "mp", "label": "MP"},
                        ],
                        size="xs",
                        value=fa.get("stage"),
                    ),
                    span=2,
                ),
                dmc.Col(
                    dmc.NumberInput(
                        label="不良数(Defectives)",
                        id={"type": id("form-input-1"), "index": 6},
                        size="xs",
                        withAsterisk=True,
                        min=1,
                        value=fa.get("defectives"),
                    ),
                    span=2,
                ),
                dmc.Col(
                    dmc.TextInput(
                        label="不良率(Failure Rates)%",
                        id={"type": id("form-input-1"), "index": 7},
                        size="xs",
                        withAsterisk=True,
                        value=fa.get("failure_rates"),
                        # rightSection=DashIconify(icon="material-symbols:percent"),
                        placeholder="eg: 5.8%",
                    ),
                    span=2,
                ),
                dmc.Col(
                    dmc.Select(
                        label="是否有其他材料失效?(other failure?)",
                        id={"type": id("form-input-1"), "index": 8},
                        size="xs",
                        value=fa.get("other_failure"),
                        data=["Yes", "No"],
                    ),
                    span=3,
                ),
                dmc.Col(
                    dmc.TextInput(
                        label="其他失效材料描述【内容需要描述：DeltaPN/厂商/厂商料号，失效现象简单描述】",
                        id={"type": id("form-input-1"), "index": 10},
                        size="xs",
                        value=fa.get("other_failure_description"),
                        style={"display": "none"},
                        withAsterisk=True,
                    ),
                    span=12,
                ),
                dmc.Col(
                    dmc.Textarea(
                        label="问题描述(Failure Description)",
                        id={"type": id("form-input-1"), "index": 9},
                        autosize=True,
                        size="xs",
                        withAsterisk=True,
                        value=fa.get("failure_description"),
                    ),
                    span=12,
                ),
                dmc.Col(
                    dmc.Group(
                        [
                            dmc.TextInput(
                                label="位置号",
                                size="xs",
                                withAsterisk=True,
                                id=id("designno"),
                                value=fa.get("designno"),
                            ),
                            dmc.TextInput(
                                label="料号",
                                size="xs",
                                withAsterisk=True,
                                id=id("deltapn"),
                                value=fa.get("deltapn"),
                                debounce=1000,
                            ),
                            dmc.TextInput(
                                label="描述",
                                size="xs",
                                id=id("des"),
                                value=fa.get("des"),
                            ),
                            dmc.TextInput(
                                label="厂商",
                                size="xs",
                                id=id("mfgname"),
                                value=fa.get("mfgname"),
                            ),
                            dmc.TextInput(
                                label="厂商料号",
                                size="xs",
                                id=id("mfgpn"),
                                value=fa.get("mfgpn"),
                                debounce=1000,
                            ),
                            dmc.Select(
                                label="材料类别1",
                                placeholder="Select one",
                                size="xs",
                                withAsterisk=True,
                                id=id("cat1"),
                                value=fa.get("cat1"),
                                data=cats["cat1"].unique(),
                            ),
                            dmc.Select(
                                label="材料类别2",
                                placeholder="Select one",
                                size="xs",
                                withAsterisk=True,
                                id=id("cat2"),
                                value=fa.get("cat2"),
                                data=cats["cat2"].unique(),
                            ),
                            dmc.Select(
                                label="材料类别3",
                                placeholder="Select one",
                                size="xs",
                                withAsterisk=True,
                                id=id("cat3"),
                                value=fa.get("cat3"),
                                data=cats["cat3"].unique(),
                            ),
                        ],
                        spacing=0,
                        align="end",
                        grow=True,
                    ),
                    span=12,
                ),
            ]
        ),
    ]
    return dmc.Stack(form_part_1)


def form_part_2(task):
    db = get_db()
    fa = db.find_one("ce.fa", {"task_id": task.get("id")}) or {}
    form_part_2 = [
        dmc.Paper(
            [
                html.Div(
                    [
                        dmc.Text("输入输出信息", size="xs", weight=700),
                        dmc.Text("output/input information", color="dimmed", size="xs"),
                    ],
                    # style={"background-color": "#f1f5f8"},
                ),
                dmc.Divider(),
                dmc.Group(
                    [
                        dmc.TextInput(
                            label="輸入電壓(input voltage AC/DC)",
                            # style={"width": 225},
                            size="xs",
                            id={"type": id("form-input-2"), "index": 0},
                            withAsterisk=True,
                            value=fa.get("input_voltage"),
                        ),
                        dmc.TextInput(
                            label="輸出電壓(output voltage)",
                            # style={"width": 225},
                            size="xs",
                            id={"type": id("form-input-2"), "index": 1},
                            withAsterisk=True,
                            value=fa.get("output_voltage"),
                        ),
                        dmc.TextInput(
                            label="輸出電流/功率(Output current/power)",
                            # style={"width": 225},
                            size="xs",
                            id={"type": id("form-input-2"), "index": 2},
                            withAsterisk=True,
                            value=fa.get("output_power"),
                        ),
                        dmc.TextInput(
                            label="工作頻率(operating frequency)",
                            # style={"width": 225},
                            size="xs",
                            id={"type": id("form-input-2"), "index": 3},
                            withAsterisk=True,
                            value=fa.get("operating_frequency"),
                        ),
                    ],
                    grow=True,
                ),
                dmc.Space(h=10),
            ],
            withBorder=True,
            shadow="xs",
            p="xs",
            style={"background-color": "#f1f5f8"},
            # style={"width": "800px"},
        ),
        dmc.Paper(
            [
                html.Div(
                    [
                        dmc.Text("失效材料所在功能", size="xs", weight=700),
                        dmc.Text(
                            "Function of the failed material", color="dimmed", size="xs"
                        ),
                    ],
                    # style={"background-color": "azure"},
                ),
                dmc.Divider(),
                dmc.Group(
                    [
                        dmc.Select(
                            label="位置固定/隨機(Fixed/random position)",
                            placeholder="Select one",
                            data=[
                                {
                                    "value": "fixed",
                                    "label": "Fixed",
                                },
                                {
                                    "value": "random",
                                    "label": "Random",
                                },
                            ],
                            # style={"width": 200},
                            size="xs",
                            withAsterisk=True,
                            id={"type": id("form-input-2"), "index": 4},
                            value=fa.get("position"),
                        ),
                        dmc.TextInput(
                            label="元件應用功能(Component application function)",
                            # style={"width": 225},
                            size="xs",
                            id={"type": id("form-input-2"), "index": 5},
                            withAsterisk=True,
                            value=fa.get("component_function"),
                        ),
                    ],
                    grow=True,
                ),
                dmc.Space(h=10),
            ],
            withBorder=True,
            shadow="xs",
            p="xs",
            style={"background-color": "#f1f5f8"},
        ),
        dmc.Paper(
            [
                html.Div(
                    [
                        dmc.Text(
                            "失效條件(元件電壓/電流/Ramp time/测试时间\
                                 /溫度/濕度/震動/上電失效or測試多久失效等)",
                            size="xs",
                            weight=700,
                        ),
                        dmc.Text(
                            "Failure condition (component voltage/current /Ramp time\
                                /Test time/ temperature/humidity/vibration/power-on \
                                failure or test duration failure, etc.)",
                            color="dimmed",
                            size="xs",
                        ),
                    ],
                    # style={"background-color": "azure"},
                ),
                dmc.Divider(),
                dmc.Textarea(
                    label="失效前測試項目及條件(Failure test items and conditions)",
                    # style={"width": 225},
                    size="xs",
                    id={"type": id("form-input-2"), "index": 6},
                    withAsterisk=True,
                    autosize=True,
                    value=fa.get("failure_condition"),
                ),
                dmc.Space(h=10),
            ],
            withBorder=True,
            shadow="xs",
            p="xs",
            style={"background-color": "#f1f5f8"},
        ),
        dmc.Paper(
            [
                html.Div(
                    [
                        dmc.Text(
                            "外觀無burn mark或無法工作時,各pin阻抗對比",
                            size="xs",
                            weight=700,
                        ),
                        dmc.Text(
                            "Comparison of pin impedance when there is no burn \
                                mark in appearance or cannot work",
                            color="dimmed",
                            size="xs",
                        ),
                    ],
                    # style={"background-color": "azure"},
                ),
                dmc.Divider(),
                dmc.Group(
                    [
                        dmc.TextInput(
                            label="失效樣品(Failure sample)",
                            size="xs",
                            id={"type": id("form-input-2"), "index": 7},
                            withAsterisk=True,
                            value=fa.get("failure_sample"),
                        ),
                        dmc.TextInput(
                            label="正常樣品(Normal sample)",
                            size="xs",
                            id={"type": id("form-input-2"), "index": 8},
                            withAsterisk=True,
                            value=fa.get("normal_sample"),
                        ),
                    ],
                    grow=True,
                    # style={"backgroundColor": "white"},
                ),
                dmc.Space(h=10),
            ],
            withBorder=True,
            shadow="xs",
            p="xs",
            style={"background-color": "#f1f5f8"},
        ),
        dmc.Paper(
            [
                html.Div(
                    [
                        dmc.Text("失效相關波形", size="xs", weight=700),
                        dmc.Text(
                            "Failure dependent waveform", color="dimmed", size="xs"
                        ),
                    ],
                    # style={"background-color": "azure"},
                ),
                dmc.Divider(),
                dmc.Group(
                    [
                        dmc.TextInput(
                            label="失效波形(Failure waveform)",
                            size="xs",
                            id={"type": id("form-input-2"), "index": 9},
                            withAsterisk=True,
                            value=fa.get("failure_waveform"),
                        ),
                        dmc.TextInput(
                            label="正常波形(Normal waveform)",
                            size="xs",
                            id={"type": id("form-input-2"), "index": 10},
                            withAsterisk=True,
                            value=fa.get("normal_waveform"),
                        ),
                    ],
                    grow=True,
                    # style={"backgroundColor": "white"},
                ),
                dmc.Space(h=10),
            ],
            withBorder=True,
            shadow="xs",
            p="xs",
            style={"background-color": "#f1f5f8"},
        ),
        dmc.Paper(
            [
                html.Div(
                    [
                        dmc.Text(
                            "交叉驗證結果(失效產品上失效材料A,正常產品上正常材料B)",
                            size="xs",
                            weight=700,
                        ),
                        dmc.Text(
                            "Cross validation results (Failed material A on failed \
                                product, normal material B on normal product)",
                            color="dimmed",
                            size="xs",
                        ),
                    ],
                    # style={"background-color": "azure"},
                ),
                dmc.Divider(),
                dmc.Group(
                    [
                        dmc.TextInput(
                            label="A->B測試結果(A->B test result)",
                            size="xs",
                            id={"type": id("form-input-2"), "index": 11},
                            withAsterisk=True,
                            value=fa.get("a_b_test"),
                        ),
                        dmc.TextInput(
                            label="B->A測試結果(B->A test result)",
                            size="xs",
                            id={"type": id("form-input-2"), "index": 12},
                            withAsterisk=True,
                            value=fa.get("b_a_test"),
                        ),
                    ],
                    grow=True,
                    # style={"backgroundColor": "white"},
                ),
                dmc.Space(h=10),
            ],
            withBorder=True,
            shadow="xs",
            p="xs",
            style={"background-color": "#f1f5f8"},
        ),
    ]
    return dmc.Stack(form_part_2)


def form_part_3(task):
    task_id = task.get("id")
    marking_picture = f"marking_picture_{task_id}"
    failure_waveform_pic = f"failure_waveform_pic_{task_id}"
    normal_waveform_pic = f"normal_waveform_pic_{task_id}"
    peripheral_circuit = f"peripheral_circuit_{task_id}"
    attachment = f"fa_rd_attachment_{task_id}"
    status = task.get("status")
    if status == "reject":
        submit_child = "重新提交"
        save_display = "none"
    else:
        submit_child = "提交申请"
        save_display = "block"

    fl1, fl2, fl3, fl4, fl5 = [
        [
            {
                "name": j.name,
                "url": f"/upload/{i}/{j.name}",
                "status": "done",
            }
            for j in (UPLOAD_FOLDER_ROOT / i).glob("*")
        ]
        for i in [
            marking_picture,
            failure_waveform_pic,
            normal_waveform_pic,
            peripheral_circuit,
            attachment,
        ]
    ]

    form_part_3 = [
        fac.AntdPictureUpload(
            apiUrl="/upload/",
            buttonContent="上传材料Marking照片",
            id=id("marking-picture"),
            lastUploadTaskRecord={},
            uploadId=marking_picture,
            fileTypes=IMG_TYPE,
            defaultFileList=fl1,
        ),
        dbc.Collapse(
            fac.AntdPictureUpload(
                apiUrl="/upload/",
                buttonContent="上传失效波形照片",
                id=id("failure-waveform-picture"),
                lastUploadTaskRecord={},
                uploadId=failure_waveform_pic,
                fileTypes=IMG_TYPE,
                defaultFileList=fl2,
            ),
            is_open=False,
            id=id("failure-waveform-collapse"),
        ),
        dbc.Collapse(
            fac.AntdPictureUpload(
                apiUrl="/upload/",
                buttonContent="上传正常波形照片",
                id=id("normal-waveform-picture"),
                lastUploadTaskRecord={},
                uploadId=normal_waveform_pic,
                fileTypes=IMG_TYPE,
                defaultFileList=fl3,
            ),
            is_open=False,
            id=id("normal-waveform-collapse"),
        ),
        dbc.Collapse(
            fac.AntdPictureUpload(
                apiUrl="/upload/",
                buttonContent="上传周边电路照片",
                id=id("circuit-picture"),
                lastUploadTaskRecord={},
                uploadId=peripheral_circuit,
                fileTypes=IMG_TYPE,
                defaultFileList=fl4,
            ),
            is_open=False,
            id=id("circuit-picture-collapse"),
        ),
        fac.AntdDraggerUpload(
            apiUrl="/upload/",
            text="上传附件",
            id=id("attachment"),
            lastUploadTaskRecord={},
            # style={"width": "204px"},
            uploadId=attachment,
            defaultFileList=fl5,
        ),
        dmc.Space(h=10),
        dmc.Group(
            [
                dmc.Button(
                    "暂存表单", id=id("save"), color="green", display=save_display
                ),
                dmc.Button(submit_child, id=id("submit")),
                dmc.Button("取消申请", id=id("cancel"), color="red"),
            ],
            spacing=100,
            position="apart",
        ),
    ]
    return dmc.Stack(form_part_3)


def rd_form(task, nt_name):
    return dmc.Stack(
        [
            form_part_1(task, nt_name),
            dbc.Collapse(form_part_2(task), id=id("other-info")),
            form_part_3(task, nt_name),
            dmc.Space(h=10),
        ],
    )


def layout(tid=None, **kwargs):
    if not tid:
        raise PreventUpdate

    db = get_db()
    task = db.find_one("ce.task", {"id": tid})
    output = dmc.Container(
        dmc.Stack(
            [
                form_part_1(task),
                dbc.Collapse(form_part_2(task), id=id("other-info")),
                form_part_3(task),
                dmc.Space(h=10),
                fuc.ListenUnload(id=id("unload")),
            ],
        )
    )
    return output


# !----------------callback----------------

fields1 = [
    {"en": "purpose", "cn": "主题", "req": 1},
    {"en": "place", "cn": "发生地点", "req": 1},
    {"en": "region", "cn": "地区", "req": 0},
    {"en": "customer", "cn": "客户", "req": 1},
    {"en": "model", "cn": "使用机种", "req": 1},
    {"en": "stage", "cn": "机种阶段", "req": 0},
    {"en": "defectives", "cn": "不良数", "req": 1},
    {"en": "failure_rates", "cn": "不良率", "req": 1},
    {"en": "other_failure", "cn": "其他材料失效", "req": 0},
    {"en": "other_failure_description", "cn": "其他失效材料描述", "req": 0},
    {"en": "failure_description", "cn": "问题描述", "req": 1},
]

fields2 = [
    {"en": "input_voltage", "cn": "输入电压", "req": 1},
    {"en": "output_voltage", "cn": "输出电压", "req": 1},
    {"en": "output_power", "cn": "输出电路/功率", "req": 1},
    {"en": "operating_frequency", "cn": "工作频率", "req": 1},
    {"en": "position", "cn": "位置固定/随机", "req": 1},
    {"en": "component_function", "cn": "元件应用功能", "req": 1},
    {"en": "failure_condition", "cn": "失效前测试项目及条件", "req": 1},
    {"en": "failure_sample", "cn": "失效样品", "req": 1},
    {"en": "normal_sample", "cn": "正常样品", "req": 1},
    {"en": "failure_waveform", "cn": "失效波形", "req": 1},
    {"en": "normal_waveform", "cn": "正常波形", "req": 1},
    {"en": "a_b_test", "cn": "A->B测试结果", "req": 1},
    {"en": "b_a_test", "cn": "B->A测试结果", "req": 1},
]

fields3 = (
    {"en": "designno", "cn": "位置号", "req": 1},
    {"en": "deltapn", "cn": "料号", "req": 1},
    {"en": "des", "cn": "描述", "req": 0},
    {"en": "mfgname", "cn": "厂商", "req": 0},
    {"en": "mfgpn", "cn": "厂商料号", "req": 0},
    {"en": "cat1", "cn": "材料类别1", "req": 1},
    {"en": "cat2", "cn": "材料类别2", "req": 1},
    {"en": "cat3", "cn": "材料类别3", "req": 0},
)


# @callback(
#     Output("url-cb", "search"),
#     Input("url-cb", "pathname"),
#     State("user", "data"),
# )
# def redirect(pathname, user):
#     if pathname != "/ce/fa/rd":
#         raise PreventUpdate
#     nt_name = user.get("nt_name")
#     dept = user.get("dept")
#     dept_id = user.get("dept_id")

#     cond = {
#         "applicant": nt_name,
#         "status": "temp",
#         "type": "失效分析",
#         "sub_type": "失效分析",
#         "dept_id": dept_id,
#         "dept": dept,
#     }
#     task = db.find_one("ce.task", cond)
#     if task:
#         task_id = task.get("id")
#     else:
#         task_id = db.insert("ce.task", cond)
#         db.insert("ce.fa", {"task_id": task_id})
#     return f"?tid={task_id}"


@callback(
    Output("url", "search"),
    Input("url", "pathname"),
    State("url", "search"),
    State("user", "data"),
)
def redirect_task_id(pathname, search, user):
    if search:
        raise PreventUpdate
    nt_name = user.get("nt_name")
    dept = user.get("dept")
    dept_id = user.get("dept_id")
    if pathname not in ("/ce/fa/rd", "/ce/pn/new/rd", "/ce/pn/temp/rd"):
        raise PreventUpdate

    db = get_db()
    if pathname == "/ce/fa/rd":
        cond = {
            "applicant": nt_name,
            "status": "temp",
            "type": "失效分析",
            "sub_type": "失效分析",
            "dept_id": dept_id,
            "dept": dept,
        }
        task = db.find_one("ce.task", cond)
        if task:
            task_id = task.get("id")
        else:
            task_id = db.insert("ce.task", cond)
            db.insert("ce.fa", {"task_id": task_id})
        return f"?tid={task_id}"

    elif pathname == "/ce/pn/new/rd":
        cond = {
            "applicant": nt_name,
            "status": "temp",
            "type": "料号申请",
            "sub_type": "全新料号",
            "dept_id": dept_id,
            "dept": dept,
        }
        task = db.find_one("ce.task", cond)
        if task:
            task_id = task.get("id")
        else:
            task_id = db.insert("ce.task", cond)
            db.insert("ce.pn_new", {"task_id": task_id})
        return f"?tid={task_id}"

    elif pathname == "/ce/pn/temp/rd":
        cond = {
            "applicant": nt_name,
            "status": "temp",
            "type": "料号申请",
            "sub_type": "临时料号",
            "dept_id": dept_id,
            "dept": dept,
        }
        task = db.find_one("ce.task", cond)
        if task:
            task_id = task.get("id")
        else:
            task_id = db.insert("ce.task", cond)
            db.insert("ce.pn_temp", {"task_id": task_id})
        return f"?tid={task_id}"
    else:
        raise PreventUpdate


# @callback(
#     Input(id("unload"), "unloaded"),
#     State("url-cb", "search"),
# )
# def unload_page(unloaded, search):
#     if not unloaded:
#         raise PreventUpdate

#     url = dict(parse_qsl(urlsplit(search).query))
#     id = url.get("tid")
#     task = db.find_one("ce.task", {"id": id})
#     status = task.get("status")

#     if status == "temp":
#         if not task.get("gmt_update"):
#             db.delete("ce.task", {"id": id})


@callback(
    Output(id("cat2"), "data"),
    Output(id("cat2"), "value"),
    Output(id("cat3"), "value"),
    Input(id("cat1"), "value"),
)
def initial_cat2_data(cat1):
    if not cat1:
        raise PreventUpdate

    sql = "select distinct category_2 as cat2 \
        from ssp_ce.a_mat_catalogue where category_1=%s"
    df = read_sql(sql, params=[cat1])
    data = df["cat2"].tolist()
    return data, no_update, no_update


@callback(
    Output(id("cat3"), "data"),
    Output(id("cat3"), "value"),
    Input(id("cat2"), "value"),
    State(id("cat1"), "value"),
)
def initial_cat3_data(cat2, cat1):
    if not cat2:
        raise PreventUpdate
    sql = "select distinct category_3 as cat3 \
                  from ssp_ce.a_mat_catalogue \
                    where category_1=%s and category_2=%s"
    df = read_sql(sql, params=[cat1, cat2])
    data = df["cat3"].tolist()
    data = [i for i in data if i]
    return data, no_update


@callback(
    Output(id("mfgname"), "value"),
    Output(id("des"), "value"),
    Output(id("deltapn"), "value"),
    Output(id("mfgpn"), "value"),
    Output(id("cat1"), "value"),
    Output(id("cat2"), "value"),
    Output(id("cat3"), "value"),
    Input(id("deltapn"), "value"),
    Input(id("mfgpn"), "value"),
)
def complete_material_info(deltapn, mfgpn):
    if ctx.triggered_id == id("deltapn"):
        sql = "select distinct deltapn,des,mfgname,mfgpn \
                    from ssp_csg.mat_info where deltapn=%s limit 1"
        df = read_sql(sql, params=[deltapn.strip()])
        df = ce_mat_category(df)
        if df.empty:
            raise PreventUpdate
        df = df.iloc[0]
        return (
            df["mfgname"],
            df["des"],
            no_update,
            df["mfgpn"],
            df["cat1"],
            df["cat2"],
            df["cat3"],
        )
    else:
        sql = "select distinct deltapn,des,mfgname,mfgpn \
                    from ssp_csg.mat_info where mfgpn=%s limit 1"
        df = read_sql(sql, params=[mfgpn.strip()])
        df = ce_mat_category(df)
        if df.empty:
            raise PreventUpdate
        df = df.iloc[0]
        return (
            df["mfgname"],
            df["des"],
            df["deltapn"],
            no_update,
            df["cat1"],
            df["cat2"],
            df["cat3"],
        )


@callback(
    Output(id("other-info"), "is_open"),
    Output(id("failure-waveform-collapse"), "is_open"),
    Output(id("normal-waveform-collapse"), "is_open"),
    Output(id("circuit-picture-collapse"), "is_open"),
    Input(id("cat1"), "value"),
    State(id("cat1"), "value"),
    prevent_initial_call=False,
)
def open_other_info(id, value):
    if value in ("Active", "Passive"):
        return True, True, True, True
    else:
        return False, False, False, False


@callback(
    Output("global-notice", "children"),
    Output(id("save"), "disabled"),
    Output(id("submit"), "disabled"),
    Output(id("cancel"), "disabled"),
    Input(id("save"), "n_clicks"),
    State("url", "search"),
    State({"type": id("form-input-1"), "index": ALL}, "value"),
    State({"type": id("form-input-2"), "index": ALL}, "value"),
    State(id("applicant"), "value"),
    State(id("cc"), "value"),
    State(id("designno"), "value"),
    State(id("deltapn"), "value"),
    State(id("des"), "value"),
    State(id("mfgname"), "value"),
    State(id("mfgpn"), "value"),
    State(id("cat1"), "value"),
    State(id("cat2"), "value"),
    State(id("cat3"), "value"),
)
def temp_save(
    n_clicks,
    search,
    input1,
    input2,
    applicant,
    cc,
    designno,
    deltapn,
    des,
    mfgname,
    mfgpn,
    cat1,
    cat2,
    cat3,
):
    if not n_clicks:
        raise PreventUpdate

    url = dict(parse_qsl(urlsplit(search).query))
    tid = url.get("tid")
    data1 = {j["en"]: input1[i] for i, j in enumerate(fields1) if input1[i]}
    data2 = {j["en"]: input2[i] for i, j in enumerate(fields2) if input2[i]}
    cc = ",".join(cc)
    data3 = {
        "applicant": applicant,
        "ce": cc,
        "designno": designno,
        "des": des,
        "deltapn": deltapn,
        "mfgname": mfgname,
        "mfgpn": mfgpn,
        "cat1": cat1,
        "cat2": cat2,
        "cat3": cat3,
    }

    data = data1 | data2 | data3
    task = {"id": tid} | {
        "applicant": applicant,
        "cc": cc,
        "deltapn": deltapn,
        "mfgname": mfgname,
        "mfgpn": mfgpn,
        "cat1": cat1,
        "cat2": cat2,
        "cat3": cat3,
    }

    db = get_db()
    fa = db.find_one("ce.fa", {"task_id": tid})
    fa = {i: data.get(i) or j for i, j in fa.items()}

    db.update("ce.task", task)
    db.update("ce.fa", fa)
    return notice("暂存成功"), True, True, True


@callback(
    Output("global-notice", "children"),
    Output(id("save"), "disabled"),
    Output(id("submit"), "disabled"),
    Output(id("cancel"), "disabled"),
    Input(id("cancel"), "n_clicks"),
    State("url", "search"),
)
def cancel_task(n_clicks, search):
    if not n_clicks:
        raise PreventUpdate

    url = dict(parse_qsl(urlsplit(search).query))
    tid = url.get("tid")

    db = get_db()
    db.update("ce.task", {"status": "cancel", "id": tid})
    return notice("取消成功"), True, True, True


@callback(
    Output("global-notice", "children"),
    Output(id("submit"), "disabled"),
    Output(id("save"), "disabled"),
    Output(id("cancel"), "disabled"),
    Input(id("submit"), "n_clicks"),
    State({"type": id("form-input-1"), "index": ALL}, "value"),
    State({"type": id("form-input-2"), "index": ALL}, "value"),
    State(id("applicant"), "value"),
    State("url", "search"),
    State(id("cc"), "value"),
    State(id("designno"), "value"),
    State(id("deltapn"), "value"),
    State(id("des"), "value"),
    State(id("mfgname"), "value"),
    State(id("mfgpn"), "value"),
    State(id("cat1"), "value"),
    State(id("cat2"), "value"),
    State(id("cat3"), "value"),
)
def rd_submit(n_clicks, input1, input2, applicant, search, cc, *input3):
    if not n_clicks:
        raise PreventUpdate

    for i, j in enumerate(fields1):
        if j["req"] and not input1[i]:
            return notice(f"{j['cn']}不能为空", "error"), False, False, False

    for i, j in enumerate(fields3):
        if j["req"] and not input3[i]:
            return notice(f"{j['cn']}不能为空", "error"), False, False, False

    if input3[5] in ("Active", "Passive"):
        for i, j in enumerate(fields2):
            if j["req"] and not input2[i]:
                return notice(f"{j['cn']}不能为空", "error"), False, False, False

    url = dict(parse_qsl(urlsplit(search).query))
    task_id = url.get("tid")

    marking_folder = UPLOAD_FOLDER_ROOT / f"marking_picture_{task_id}"
    if not marking_folder.exists():
        return notice("材料Marking照片不能为空", "error"), False, False, False

    data1 = {j["en"]: input1[i] for i, j in enumerate(fields1) if input1[i]}
    data2 = {j["en"]: input2[i] for i, j in enumerate(fields2) if input2[i]}
    data3 = {j["en"]: input3[i].strip() for i, j in enumerate(fields3) if input3[i]}

    defectives = data1.get("defectives") or 0
    place = data1.get("place")
    if (place == "customer") or (defectives > 5):
        urgent = "紧急"
    else:
        urgent = "一般"

    cat1 = data3.get("cat1")
    cat2 = data3.get("cat2")
    cat3 = data3.get("cat3")

    db = get_db()
    user = db.find_one("ssp.user", {"nt_name": applicant})
    dept = user.get("dept")

    # *------不按材料类型指派的CE------*
    cc: set = set(cc) | set(["Ying.Gao", "Yang.Zhao", "Xin.Shu"])
    ce = "ru.xue"
    ce1 = "ru.xue"
    cat_dict = {"category_1": cat1, "category_2": cat2, "category_3": cat3}
    cat_dict = {i: j for i, j in cat_dict.items() if j}
    cats = db.find_one("ssp_ce.a_mat_catalogue", cat_dict)
    if cats:
        ce1 = cats.get("owner_ce").title()

    result = ce_owner.evaluate({"dept": dept, "cat1": cat1, "cat2": cat2}).get("result")
    if result:
        ce = result.get("ce").title()
        cc.add(ce1)
    else:
        ce = ce1

    # data1["cc"] = ",".join(cc)
    # *------图片及附件------*

    data4 = {"marking_picture": f"marking_picture_{task_id}"}
    if (UPLOAD_FOLDER_ROOT / f"peripheral_circuit_{task_id}").exists():
        data4["peripheral_circuit"] = f"peripheral_circuit_{task_id}"
    if (UPLOAD_FOLDER_ROOT / f"failure_waveform_pic_{task_id}").exists():
        data4["failure_waveform_pic"] = f"failure_waveform_pic_{task_id}"
    if (UPLOAD_FOLDER_ROOT / f"normal_waveform_pic_{task_id}").exists():
        data4["normal_waveform_pic"] = f"normal_waveform_pic_{task_id}"
    if (UPLOAD_FOLDER_ROOT / f"fa_rd_attachment_{task_id}").exists():
        data4["attachment"] = f"fa_rd_attachment_{task_id}"

    task = db.find_one("ce.task", {"id": task_id})
    fa = db.find_one("ce.fa", {"task_id": task_id})
    data = fa | data1 | data2 | data3 | data4
    sub_type = "失效分析"

    task = task | {
        "type": "失效分析",
        "ce": ce,
        "status": "open",
        "urgent": urgent,
        "dept": dept,
        "deltapn": data3.get("deltapn"),
        "mfgname": data3.get("mfgname"),
        "mfgpn": data3.get("mfgpn"),
        "cat1": cat1,
        "cat2": cat2,
        "cat3": cat3,
        "cc": ",".join(cc),
        "start_date": datetime.now(),
        "applicant": applicant,
    }

    db.update("ce.task", task)
    db.update("ce.fa", data)

    # *---------邮件通知开始-----------*
    to = {applicant, ce} | cc
    to = ";".join(f"{i}@deltaww.com" for i in to)
    subject = f"【失效分析】{sub_type}"
    df = pd.DataFrame([task])
    columns = [
        "type",
        "dept",
        "applicant",
        "deltapn",
        "mfgname",
        "mfgpn",
        "start_date",
        "ce",
    ]
    df = df.reindex(columns=columns)
    df["start_date"] = df["start_date"].dt.date
    content = df_to_html(
        df,
        f"您的{sub_type}已提交,{ce}将会处理,请知悉！",
        href="http://sup.deltaww.com/info/rd?page=ongoing",
        link_text="工作进展可至个人中心查询",
    )
    bg_mail(to, subject, content)
    # *---------邮件通知结束-----------*

    return notice("提交成功"), True, True, True


@callback(
    Output("global-notice", "children"),
    Output(id("reload"), "reload"),
    Input({"type": id("temp-task"), "index": ALL}, "hide"),
)
def delete_temp_task(hide):
    if not any(hide):
        raise PreventUpdate

    task_id = ctx.triggered_id.get("index")

    db = get_db()
    db.delete("ce.task", {"id": task_id})
    return notice("删除暂存表单成功"), True


@callback(
    Output({"type": id("form-input-1"), "index": 10}, "style"),
    Input({"type": id("form-input-1"), "index": 8}, "value"),
    prevent_initial_call=False,
)
def display_other_failure_description(value):
    if value == "Yes":
        return {"display": "block"}
    else:
        return {"display": "none"}
