from datetime import datetime
import pandas as pd
from config import cfg
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    Input,
    Output,
    State,
    callback,
    dash,
    no_update,
)
from db.ssp import User, Dept
from pony.orm import db_session
from tasks import bg_mail
from common import get_nt_name
import dash_mantine_components as dmc
from components import notice

dash.register_page(__name__, title="Register")


def layout(user, **query):
    _chname = ""
    _onno = ""
    _area = ""
    _dept = ""
    _role = ""
    submit_txt = "Register"
    dept_data = []
    role_data = []

    if user:
        _chname = user["name"]
        _onno = int(user["onno"])
        _area = user["area"]
        _dept = user["dept"]
        _role = user["role_group"]
        submit_txt = "Update"
        if _area == "SH":
            dept_data = cfg.sh_dept
        elif _area == "HZ":
            dept_data = cfg.hz_dept
        elif _area == "WH":
            dept_data = cfg.wh_dept
        elif _area == "CQ":
            dept_data = cfg.cq_dept
        elif _area == "NJ":
            dept_data = cfg.nj_dept
        else:
            dept_data = cfg.depts

        if _dept == "SUP_SUP":
            role_data = ["CE", "SPEC", "CROSS", "PUR", "SM"]
        else:
            role_data = ["PM", "EE", "ME", "LAYOUT", "MAG", "SOFTWARE"]

    title = dmc.Center(dmc.Title("Delta SUP"))
    name = dmc.TextInput(
        label="NT Account:",
        value=get_nt_name(),
        disabled=True,
        id="input-name",
        withAsterisk=True,
    )
    chname = dmc.TextInput(
        label="Name:",
        id="input-chname",
        value=_chname,
        withAsterisk=True,
    )
    onno = dmc.NumberInput(
        label="Employee ID:",
        id="input-onno",
        value=_onno,
        withAsterisk=True,
        hideControls=True,
    )
    area = dmc.Select(
        label="Region:",
        data=[
            {"label": "Shanghai,China", "value": "SH"},
            {"label": "Hangzhou,China", "value": "HZ"},
            {"label": "Wuhan,China", "value": "WH"},
            {"label": "Chongqing,China", "value": "CQ"},
            {"label": "Nanjing,China", "value": "NJ"},
            {"label": "Soest,Germany", "value": "DE"},
            {"label": "Thailand", "value": "TH"},
        ],
        id="input-area",
        value=_area,
        withAsterisk=True,
    )
    dept = dmc.Select(
        label="Department:",
        id="input-dept",
        value=_dept,
        data=dept_data,
        withAsterisk=True,
    )
    role = dmc.Select(
        label="Job Role:",
        id="input-role",
        value=_role,
        data=role_data,
        withAsterisk=True,
    )
    submit = dmc.Button(submit_txt, color="green", id="login-submit")

    layout = dmc.Center(
        dmc.Stack(
            [title, name, chname, onno, area, dept, role, dmc.Space(h=5), submit],
            style={"width": 450},
            spacing=0,
        ),
    )
    return layout


@callback(
    Output("input-dept", "data"),
    Input("input-area", "value"),
)
def initial_dept(area):
    """根据区域赋值部门下拉选项"""
    if area == "SH":
        data = cfg.sh_dept
    elif area == "HZ":
        data = cfg.hz_dept
    elif area == "WH":
        data = cfg.wh_dept
    elif area == "CQ":
        data = cfg.cq_dept
    elif area == "NJ":
        data = cfg.nj_dept
    elif area == "DE":
        data = cfg.hz_dept
    elif area == "TH":
        data = cfg.hz_dept
    else:
        data = []
    return data


@callback(
    Output("input-dept", "value"),
    Output("input-role", "value"),
    Output("input-dept", "disabled"),
    Output("input-role", "disabled"),
    Input("input-area", "value"),
)
def foreign_dept(area):
    """海外指定部门和角色"""
    if not area:
        raise PreventUpdate

    if area in ["DE", "TH"]:
        return "DES_CDBU", "EE", True, True
    else:
        return None, None, False, False


@callback(
    Output("input-role", "data"),
    Input("input-dept", "value"),
)
def initial_options(dept):
    """根据部门显示role_group"""
    if not dept:
        raise PreventUpdate
    if dept == "SUP_SUP":
        data = ["CE", "SPEC", "CROSS", "PUR", "SM"]
    else:
        data = ["PM", "EE", "ME", "LAYOUT", "MAG", "SOFTWARE"]
    return data


@callback(
    Output("global-notice", "children"),
    Output("url", "href"),
    Input("login-submit", "n_clicks"),
    State("input-area", "value"),
    State("input-dept", "value"),
    State("input-role", "value"),
    State("input-name", "value"),
    State("input-onno", "value"),
    State("input-chname", "value"),
)
@db_session
def login_submit(n_clicks, area, dept, role_group, name, onno, chname):
    if not n_clicks:
        raise PreventUpdate
    if not all([area, dept, role_group, name, onno]):
        return notice(
            "Registration failed. Please complete all fields.", "error"
        ), no_update
    user = User.get(nt_name=name)
    dept_group, dept_name = dept.split("_")
    _dept = Dept.get(dept_group=dept_group, dept_name=dept_name)

    if user:
        user.area = area
        user.dept = dept
        user.role_group = role_group
        user.name = chname
        user.onno = onno
        user.u_date = datetime.now()
        user.dept_id = _dept.id
        return notice("个人资料更新成功", "success"), "/"
    else:
        user = User(
            nt_name=name,
            area=area,
            dept=dept,
            role_group=role_group,
            name=chname,
            onno=onno,
            email=f"{name}@deltaww.com",
            u_date=datetime.now(),
            dept_id=_dept.id,
        )
        body = pd.DataFrame([user.to_dict()]).to_html()
        bg_mail(
            "<EMAIL>;<EMAIL>", "新用户注册通知", body
        )
        return notice("Registration successful.", "success"), "/"
