# -*- coding: utf-8 -*-
import os
import traceback

import dash_mantine_components as dmc
import dash_uploader as du
import feffery_antd_components.alias as fac
import pandas as pd
from dash import set_props
from dash_extensions.enrich import (
    DashProxy,
    Input,
    MultiplexerTransform,
    NoOutputTransform,
    ServersideOutputTransform,
    State,
    dash,
    dcc,
    html,
)
from flask import request, send_from_directory

from components.navbar import navbar
from config import SSP_DIR, UPLOAD_FOLDER_ROOT
from server import server
from tasks import bg_mail


def global_callback_error_handler(err):
    tb = traceback.format_exc()
    port = os.getenv("MYSQL_TCP_PORT")
    if port == "3307":
        set_props(
            "msg",
            {"children": dmc.Modal(dmc.Code(tb, block=True), opened=True, size=800)},
        )
    else:
        tb = tb.replace("\n", "<p>")
        bg_mail("<EMAIL>", "【SSP ERROR】", tb)


app = DashProxy(
    __name__,
    transforms=[
        MultiplexerTransform(),
        ServersideOutputTransform(),
        NoOutputTransform(),
    ],
    server=server,
    use_pages=True,
    prevent_initial_callbacks=True,
    suppress_callback_exceptions=True,
    serve_locally=True,
    title="Delta SSP",
    compress=True,
    routing_callback_inputs={
        "user": State("user", "data"),
        "hash": Input("url", "hash"),
    },
    # on_error=global_callback_error_handler,
    # external_stylesheets=[dbc.themes.YETI],
)

# app.index_string =
"""<!DOCTYPE html>
<html>
<head>
<title>SSP</title>
<link rel="manifest" href="./assets/manifest.json" />
{%metas%}
{%favicon%}
{%css%}
</head>
<script type="module">
   import 'https://cdn.jsdelivr.net/npm/@pwabuilder/pwaupdate';
   const el = document.createElement('pwa-update');
   document.body.appendChild(el);
</script>
<body>
<script>
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', ()=> {
      navigator
      .serviceWorker
      .register('./assets/sw01.js')
      .then(()=>console.log("Ready."))
      .catch(()=>console.log("Err..."));
    });
  }
</script>
{%app_entry%}
<footer>
{%config%}
{%scripts%}
{%renderer%}
</footer>
</body>
</html>
"""
# 不要的DARKLY,QUARTZ,BOOTSTRAP,CYBORG,LUX,GRID,VAPOR,SUPERHERO
# MORPH,MINTY,SKETCHY,ZEPHYR,LITERA,SOLAR,PULSE,SIMPLEX
# 可选的CERULEAN,FLATLY,COSMO,YETI,JOURNAL,SPACELAB,UNITED
# server = app.server
server.config["SECRET_KEY"] = os.getenv("SECRET_KEY")
du.configure_upload(app, UPLOAD_FOLDER_ROOT, use_upload_id=True)

app.layout = html.Div(
    [
        html.Div(navbar, style={"height": "8vh"}),
        html.Div(dash.page_container, style={"height": "92vh"}),
        html.Div(id="global-notice"),
        fac.Fragment(id="msg"),
        dcc.Store(id="user", storage_type="local"),
        dcc.Location(id="url", refresh=True),
        dcc.Download(id="download"),
    ],
    style={
        "background-color": "#f1f5f8",
        "height": "100vh",
        "width": "100vw",
        "display": "-webkit-flex",
        "flex-direction": "column",
        "overflow": "auto",
    },
)


@server.route("/mail", methods=["GET", "POST"])
def mail():
    to = request.args.get("to")
    subject = request.args.get("subject")
    body = request.args.get("body")
    data = request.get_data()
    if data:
        body = pd.read_json(data).to_html()
    bg_mail(to, subject, body)
    return "mail ok"


@server.route("/upload/", methods=["POST"])
def upload():
    """
    构建文件上传服务
    :return:
    """

    # 获取上传id参数，用于指向保存路径
    uploadId = request.values.get("uploadId")
    # breakpoint()
    # 获取上传的文件名称
    filename = request.files["file"].filename
    # 基于上传id，若本地不存在则会自动创建目录
    try:
        (UPLOAD_FOLDER_ROOT / uploadId).mkdir()
    except FileExistsError:
        pass

    # 流式写出文件到指定目录
    with open(UPLOAD_FOLDER_ROOT / uploadId / filename, "wb") as f:
        # 流式写出大型文件，这里的10代表10MB
        for chunk in iter(lambda: request.files["file"].read(1024 * 1024 * 10), b""):
            f.write(chunk)

    return {"filename": filename}


# @server.route("/upload/bom/", methods=["POST"])
# def upload_bom():
#     """
#     构建文件上传服务
#     :return:
#     """

#     # 获取上传id参数，用于指向保存路径
#     uploadId = request.values.get("uploadId")
#     # breakpoint()
#     # 获取上传的文件名称
#     filename = request.files["file"].filename
#     # 基于上传id，若本地不存在则会自动创建目录
#     try:
#         (SSP_DIR / "3. Document backup" / "BOM_Record" / uploadId).mkdir()
#     except FileExistsError:
#         pass

#     # 流式写出文件到指定目录
#     with open(
#         SSP_DIR / "3. Document backup" / "BOM_Record" / uploadId / filename, "wb"
#     ) as f:
#         # 流式写出大型文件，这里的10代表10MB
#         for chunk in iter(lambda: request.files["file"].read(1024 * 1024 * 10), b""):
#             f.write(chunk)

#     return {"filename": filename}


@server.route("/upload/<path:folder>", methods=["POST"])
def upload_to_folder(folder):
    """
    构建文件上传服务
    :return:
    """
    # 获取上传id参数，用于指向保存路径
    uploadId = request.values.get("uploadId")
    # breakpoint()
    # 获取上传的文件名称
    filename = request.files["file"].filename
    if "bom" in folder:
        fp = SSP_DIR / "3. Document backup" / "BOM_Record"
    else:
        fp = UPLOAD_FOLDER_ROOT / folder
    # 基于上传id，若本地不存在则会自动创建目录
    (fp / uploadId).mkdir(parents=True, exist_ok=True)

    # 流式写出文件到指定目录
    with open(fp / uploadId / filename, "wb") as f:
        # 流式写出大型文件，这里的10代表10MB
        for chunk in iter(lambda: request.files["file"].read(1024 * 1024 * 10), b""):
            f.write(chunk)

    return {"filename": filename}


@server.route("/upload/<path:folder>/<path:filename>")
def download_file(folder, filename):
    return send_from_directory(
        UPLOAD_FOLDER_ROOT / folder, filename, as_attachment=True
    )


# @server.route("/ce/<int:task_id>", methods=["POST"])
# def upload_ce(task_id):
#     """
#     构建文件上传服务
#     :return:
#     """
#     # 获取上传id参数，用于指向保存路径
#     uploadId = request.values.get("uploadId")
#     # breakpoint()
#     # 获取上传的文件名称
#     filename = request.files["file"].filename

#     # 基于上传id，若本地不存在则会自动创建目录
#     path = SSP_DIR / "3. Document backup" / "CE" / f"{task_id}" / uploadId
#     path.mkdir(parents=True, exist_ok=True)
#     # 流式写出文件到指定目录
#     with open(path / filename, "wb") as f:
#         # 流式写出大型文件，这里的10代表10MB
#         for chunk in iter(lambda: request.files["file"].read(1024 * 1024 * 10), b""):
#             f.write(chunk)

#     return {"filename": filename}


# @server.route("/ce/<path:task_id>/<path:folder>/<path:filename>")
# def download_ce(task_id, folder, filename):
#     path = SSP_DIR / "3. Document backup" / "CE" / f"{task_id}" / folder
#     return send_from_directory(path, filename, as_attachment=True)


if __name__ == "__main__":
    app.run(debug=True, host="127.0.0.1", port=8080, use_reloader=False)
