# -*- coding: utf-8 -*-
import dash_mantine_components as dmc
import feffery_antd_components as fac
from dash import dash_table, dcc, html

from common import id_factory, get_db
from components import CeCancelAIO, CeRejectAIO
from components.ce import get_attachment
# from dbtool import db

id = id_factory(__name__)


def task_information(task):
    db = get_db()
    dept = db.find_one("ssp.dept", {"id": task.get("dept_id")})
    settings = db.find_one("ce.settings", {"dept_id": task.get("dept_id")})
    return fac.AntdDescriptions(
        [
            fac.AntdDescriptionItem(task.get("dept"), label="部门"),
            fac.AntdDescriptionItem(task.get("applicant"), label="申请人"),
            fac.AntdDescriptionItem(task.get("type"), label="类型"),
            fac.AntdDescriptionItem(task.get("status"), label="状态"),
            fac.AntdDescriptionItem(dept.get("product_code"), label="产品代码"),
            fac.AntdDescriptionItem(settings.get("project_name"), label="项目名称"),
        ],
        labelStyle={"fontWeight": "bold"},
    )


def checkbox(pn):
    return dmc.CheckboxGroup(
        children=[
            dmc.Checkbox(label="MSL", value="MSL", size="xs", mb=10),
            dmc.Checkbox(label="ESD", value="ESD", size="xs", mb=10),
            dmc.Checkbox(label="WS", value="WS", size="xs", mb=10),
            dmc.Checkbox(label="HF", value="HF", size="xs", mb=10),
            dmc.Checkbox(label="LF", value="LF", size="xs", mb=10),
        ],
        id=id("admit_check"),
        label="承认资料确认",
        orientation="horizontal",
        withAsterisk=True,
        offset="md",
        size="xs",
        mb=5,
        value=pn.get("admit_check").split(","),  # 从数据库中读取
    )


columns = [
    {"name": "厂商", "id": "mfgname", "presentation": "input"},
    {"name": "厂商型号", "id": "mfgpn", "presentation": "input"},
    {"name": "厂商代码", "id": "mfg_code", "presentation": "input"},
    {"name": "新台达料号", "id": "new_deltapn", "presentation": "input"},
    {"name": "新描述", "id": "new_des", "presentation": "input"},
    {"name": "新厂商", "id": "new_mfgname", "presentation": "input"},
    {"name": "新厂商型号", "id": "new_mfgpn", "presentation": "input"},
    {"name": "CE备注", "id": "ce_remark", "presentation": "input"},
    # {"name": "承认资料上传（附件）", "id": "admit_attachment", "presentation": "input"},
]


def table(data):
    return dash_table.DataTable(
        data=data,
        columns=columns,
        editable=True,
        # row_deletable=True,
        is_focused=True,
        id=id("table"),
        style_cell={
            "whiteSpace": "normal",
            "height": "auto",
            "textAlign": "left",
            "font-family": "Helvetica",
            "font-size": "10px",
        },
        css=[{"selector": ".dash-spreadsheet-menu-item", "rule": "display:none"}],
    )


title = dmc.Center(dmc.Text("临时料号申请单", weight=700))


def form_part2(pn):
    form_part2 = html.Div(
        [
            dmc.Group(
                [
                    dmc.TextInput(
                        label="机种名",
                        description="(Model Number)",
                        size="xs",
                        # withAsterisk=True,
                        id=id("model"),
                        persistence_type="local",
                        persistence=True,
                        value=pn.get("model"),
                    ),
                    dmc.TextInput(
                        label="机种功率",
                        description="(Watt)",
                        size="xs",
                        id=id("watt"),
                        persistence_type="local",
                        persistence=True,
                        value=pn.get("watt"),
                    ),
                    dmc.TextInput(
                        label="机种应用領域",
                        description="(Application)",
                        size="xs",
                        id=id("application"),
                        persistence_type="local",
                        persistence=True,
                        value=pn.get("application"),
                    ),
                    dmc.TextInput(
                        label="项目阶段",
                        description="(Stage Of Project)",
                        size="xs",
                        # withAsterisk=True,
                        id=id("stage"),
                        persistence_type="local",
                        persistence=True,
                        value=pn.get("stage"),
                    ),
                ],
                spacing=0,
                align="end",
                grow=True,
            ),
            dmc.Group(
                [
                    dmc.TextInput(
                        label="量产计划",
                        description="(MP Schedule)",
                        size="xs",
                        # withAsterisk=True,
                        id=id("mp"),
                        persistence_type="local",
                        persistence=True,
                        value=pn.get("mp"),
                    ),
                    dmc.TextInput(
                        label="生产地点",
                        description="(Production location)",
                        size="xs",
                        id=id("location"),
                        persistence_type="local",
                        persistence=True,
                        value=pn.get("location"),
                    ),
                    dmc.TextInput(
                        label="年需求量",
                        description="(Forecast(pcs/Year))",
                        size="xs",
                        id=id("forecast"),
                        persistence_type="local",
                        persistence=True,
                        value=pn.get("forecast"),
                    ),
                    dmc.TextInput(
                        label="單機用量",
                        description="(pcs/set)",
                        size="xs",
                        # withAsterisk=True,
                        id=id("pcs"),
                        persistence_type="local",
                        persistence=True,
                        value=pn.get("pcs"),
                    ),
                    dmc.Select(
                        label="代工还是原设计",
                        description="(OEM or ODM)",
                        placeholder="Select one",
                        id=id("form-input-1"),
                        data=[
                            {"value": "oem", "label": "OEM"},
                            {"value": "odm", "label": "ODM"},
                        ],
                        size="xs",
                        persistence_type="local",
                        persistence=True,
                        value=pn.get("oem_odm"),
                    ),
                ],
                spacing=0,
                align="end",
                grow=True,
            ),
        ]
    )
    return form_part2


def form_part3(pn):
    form_part3 = html.Div(
        [
            dmc.Group(
                [
                    dmc.TextInput(
                        label="位置号",
                        size="xs",
                        # withAsterisk=True,
                        id=id("designno"),
                        persistence_type="local",
                        persistence=True,
                        value=pn.get("designno"),
                    ),
                    dmc.Select(
                        label="材料类别1",
                        placeholder="Select one",
                        size="xs",
                        # withAsterisk=True,
                        id=id("cat1"),
                        data=[pn.get("cat1")],
                        persistence_type="local",
                        persistence=True,
                        value=pn.get("cat1"),
                    ),
                    dmc.Select(
                        label="材料类别2",
                        placeholder="Select one",
                        size="xs",
                        # withAsterisk=True,
                        id=id("cat2"),
                        persistence_type="local",
                        persistence=True,
                        data=[pn.get("cat2")],
                        value=pn.get("cat2"),
                    ),
                    dmc.Select(
                        label="材料类别3",
                        placeholder="Select one",
                        size="xs",
                        # withAsterisk=True,
                        id=id("cat3"),
                        persistence_type="local",
                        persistence=True,
                        data=[pn.get("cat3")],
                        value=pn.get("cat3"),
                    ),
                ],
                spacing=0,
                align="end",
                grow=True,
            ),
        ]
    )
    return form_part3


def form_part4(pn):
    form_part4 = dmc.Stack(
        [
            # dmc.Paper(
            #     [table, checkbox],
            #     withBorder=True,
            #     p="xs",
            #     style={"background-color": "#f1f5f8"},
            # ),
            dmc.Group(
                [
                    dmc.TextInput(
                        label="使用位置",
                        size="xs",
                        persistence_type="local",
                        persistence=True,
                        value=pn.get("use_position"),
                    ),
                    dmc.Select(
                        label="選用此料的原因",  # 勾选不同，生成TextInput不同。主料和替代料
                        placeholder="Select one",
                        size="xs",
                        data=["main source", "single source"],
                        persistence_type="local",
                        persistence=True,
                        value=pn.get("reason"),
                    ),
                    dmc.Select(
                        label="是否有做过该材料的测试？",
                        placeholder="Select one",
                        size="xs",
                        data=["是", "否"],
                        persistence_type="local",
                        persistence=True,
                        value=pn.get("tested"),
                    ),
                    dmc.Select(
                        label="是否有做过其他材料的benchmarking？",
                        placeholder="Select one",
                        size="xs",
                        data=["是", "否"],
                        persistence_type="local",
                        persistence=True,
                        value=pn.get("benchmarking"),
                    ),
                    dmc.Select(
                        label="是否是客戶指定？",
                        placeholder="Select one",
                        size="xs",
                        data=["是", "否"],
                        persistence_type="local",
                        persistence=True,
                        value=pn.get("customer_specified"),
                    ),
                ],
                spacing=0,
                align="end",
                grow=True,
            ),
            dmc.TextInput(
                label="主料为哪颗",
                size="xs",
                # withAsterisk=True,
                id=id("main_source"),
                persistence_type="local",
                persistence=True,
                value=pn.get("main_source"),
            ),
            dmc.TextInput(
                label="替代料为哪颗",
                size="xs",
                # withAsterisk=True,
                id=id("second_source"),
                persistence_type="local",
                persistence=True,
                value=pn.get("second_source"),
            ),
            dmc.Group(
                [
                    dmc.TextInput(
                        label="应用电路及功能",
                        size="xs",
                        id=id("application_circuit"),
                        persistence_type="local",
                        persistence=True,
                        value=pn.get("application_circuit"),
                    ),
                    dmc.TextInput(
                        label="机种输入电压/电流",
                        size="xs",
                        id=id("input_vi"),
                        persistence_type="local",
                        persistence=True,
                        value=pn.get("input_vi"),
                    ),
                    dmc.TextInput(
                        label="机种输出电压/电流",
                        size="xs",
                        id=id("output_vi"),
                        persistence_type="local",
                        persistence=True,
                        value=pn.get("output_vi"),
                    ),
                    dmc.TextInput(
                        label="零件的电压/电流",
                        size="xs",
                        id=id("material_vi"),
                        persistence_type="local",
                        persistence=True,
                        value=pn.get("material_vi"),
                    ),
                ],
                spacing=0,
                align="end",
                grow=True,
            ),
            # dmc.Anchor(
            #     "规格书",
            #     href=f"/upload/{pn.get('attachment')}",
            #     target="_blank",
            #     variant="link",
            #     weight=700,
            #     size=10,
            #     style={"display": "block" if pn.get("attachment") else "none"},
            # ),
            # dmc.Anchor(
            #     "联络单附件",
            #     href=f"/upload/{pn.get('contact_attachment')}",
            #     target="_blank",
            #     variant="link",
            #     weight=700,
            #     size=10,
            #     style={"display": "block" if pn.get("contact_attachment") else "none"},
            # ),
            # dmc.Anchor(
            #     "车规宣告函",
            #     href=f"/upload/{pn.get('auto_attachment')}",
            #     target="_blank",
            #     variant="link",
            #     weight=700,
            #     size=10,
            #     style={"display": "block" if pn.get("auto_attachment") else "none"},
            # ),
            # dmc.Anchor(
            #     "Approve的邮件/文件",
            #     href=f"/upload/{pn.get('approve_attachment')}",
            #     target="_blank",
            #     variant="link",
            #     weight=700,
            #     size=10,
            #     style={"display": "block" if pn.get("approve_attachment") else "none"},
            # ),
        ]
    )
    return form_part4


# admit_attachment = fac.AntdDraggerUpload(
#     apiUrl="/upload/",
#     text="承认资料附件",
#     id=id("admit_attachment"),
#     lastUploadTaskRecord={},
#     # style={"width": "130px"},
# )

download = dmc.Group(
    [
        dmc.Button(
            "下载",
            variant="subtle",
            color="orange",
            size="xs",
            id=id("download-btn"),
        ),
        dcc.Download(id=id("download")),
    ],
    position="right",
)


def layout(**query):
    task_id = query.get("task")
    db = get_db()
    task = db.find_one("ce.task", {"id": task_id})
    pn = db.execute("select * from ce.pn_temp where task_id = %s", (task_id,))
    layout = dmc.Container(
        dmc.Stack(
            [
                download,
                title,
                dmc.Divider(),
                task_information(task),
                dmc.Paper(
                    [
                        html.Div(
                            [
                                dmc.Text("机种信息填写", size="xs", weight=700),
                                dmc.Text(
                                    "model information information",
                                    color="dimmed",
                                    size="xs",
                                ),
                            ],
                        ),
                        dmc.Divider(),
                        form_part2(pn[0]),
                        dmc.Space(h=10),
                    ],
                    withBorder=True,
                    shadow="xs",
                    p="xs",
                    style={"background-color": "#f1f5f8"},
                ),
                dmc.Paper(
                    [
                        html.Div(
                            [
                                dmc.Text("料号申请信息", size="xs", weight=700),
                                dmc.Text(
                                    "deltapn application information",
                                    color="dimmed",
                                    size="xs",
                                ),
                            ],
                        ),
                        dmc.Divider(),
                        dmc.Space(h=5),
                        dmc.Paper(
                            [
                                dmc.Paper(
                                    form_part3(pn[0]),
                                    withBorder=True,
                                    p="xs",
                                    shadow="xs",
                                    style={"background-color": "#f1f5f8"},
                                ),
                                dmc.Space(h=10),
                                dmc.Paper(
                                    form_part4(pn[0]),
                                    withBorder=True,
                                    shadow="xs",
                                    p="xs",
                                    style={"background-color": "#f1f5f8"},
                                ),
                                dmc.Space(h=10),
                            ],
                            withBorder=True,
                            shadow="xs",
                            p="xs",
                            style={"background-color": "#f1f5f8"},
                        ),
                    ],
                    withBorder=True,
                    shadow="xs",
                    p="xs",
                    style={"background-color": "#f1f5f8"},
                ),
                get_attachment("规格书:", pn[0].get("attachment")),
                get_attachment("联络单附件:", pn[0].get("contact_attachment")),
                get_attachment("车规宣告函:", pn[0].get("auto_attachment")),
                get_attachment("Approve的邮件/文件:", pn[0].get("approve_attachment")),
                get_attachment("承认资料:", pn[0]["admit_attachment"]),
                table(pn),
                checkbox(pn[0]),
                fac.AntdDraggerUpload(
                    apiUrl="/upload/",
                    text="承认资料附件",
                    id=id("admit_attachment"),
                    lastUploadTaskRecord={},
                    uploadId=pn[0]["admit_attachment"] or f"{task_id}_admit_attachment",
                ),
                dmc.Group(
                    [
                        dmc.Button("提交", id=id("submit")),
                        CeRejectAIO(__name__),
                        CeCancelAIO(__name__),
                    ],
                    grow=True,
                ),
            ]
        )
    )
    return layout
