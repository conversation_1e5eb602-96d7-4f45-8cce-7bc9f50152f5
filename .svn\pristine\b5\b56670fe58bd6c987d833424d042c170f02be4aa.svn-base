# -*- coding: utf-8 -*-
from datetime import datetime

import pandas as pd
from dash import ctx
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import Input, Output, State, callback, dash

from common import (
    add_mat_category,
    material_information_complete,
    read_sql,
    df_to_html,
    get_db,
)
from components.notice import notice

# from dbtool import db
from tasks import bg_mail

from . import layout

id = layout.id
layout = layout.layout
dash.register_page(__name__, path="/ce/pn/sw/rd", title="IC+软体组合件申请-RD")

fields = [
    {"en": "deltapn", "cn": "台达料号", "req": 1},
    {"en": "des", "cn": "描述", "req": 1},
    {"en": "mfgname", "cn": "厂商", "req": 1},
    {"en": "mfgpn", "cn": "厂商料号", "req": 1},
    {"en": "cat1", "cn": "材料类型1", "req": 1},
    {"en": "cat2", "cn": "材料类型2", "req": 1},
    {"en": "cat3", "cn": "材料类型3", "req": 1},
    {"en": "application_attachment", "cn": "申请表附件", "req": 1},
    {"en": "controlling_attachment", "cn": "台达软体管控表单附件", "req": 1},
]


@callback(
    Output(id("cat2"), "data"),
    Input(id("cat1"), "value"),
)
def initial_cat2_data(cat1):
    if not cat1:
        raise PreventUpdate
    sql = "select distinct category_2 as cat2 \
                  from ssp_ce.a_mat_catalogue where category_1=%s"
    df = read_sql(sql, params=[cat1])
    data = df["cat2"].tolist()
    return data


@callback(
    Output(id("cat3"), "data"),
    Input(id("cat2"), "value"),
    State(id("cat1"), "value"),
)
def initial_cat3_data(cat2, cat1):
    if not cat2:
        raise PreventUpdate
    sql = "select distinct category_3 as cat3 \
                  from ssp_ce.a_mat_catalogue \
                    where category_1=%s and category_2=%s"
    df = read_sql(sql, params=[cat1, cat2])
    data = df["cat3"].tolist()
    data = [i for i in data if i]
    return data


@callback(
    Output(id("deltapn"), "value"),
    Output(id("des"), "value"),
    Output(id("mfgname"), "value"),
    Output(id("mfgpn"), "value"),
    Output(id("cat1"), "value"),
    Output(id("cat2"), "value"),
    Output(id("cat3"), "value"),
    Input(id("deltapn"), "value"),
    Input(id("mfgpn"), "value"),
    State(id("des"), "value"),
    State(id("mfgname"), "value"),
)
def complete_material_info(deltapn, mfgpn, des, mfgname):
    if not any([deltapn, mfgpn]):
        raise PreventUpdate
    df = pd.DataFrame(
        [{"deltapn": deltapn, "des": des, "mfgname": mfgname, "mfgpn": mfgpn}]
    )

    if ctx.triggered_id == id("deltapn"):
        df = material_information_complete(df, "deltapn")

    elif ctx.triggered_id == id("mfgpn"):
        df = material_information_complete(df, "mfgpn")
    df = add_mat_category(df)
    df = df.drop("ce", axis=1)

    return df.iloc[0].tolist()


@callback(
    Output("global-notice", "children"),
    Output(id("rd-submit"), "disabled"),
    Input(id("rd-submit"), "n_clicks"),
    State(id("deltapn"), "value"),
    State(id("des"), "value"),
    State(id("mfgname"), "value"),
    State(id("mfgpn"), "value"),
    State(id("cat1"), "value"),
    State(id("cat2"), "value"),
    State(id("cat3"), "value"),
    State(id("application_attachment"), "lastUploadTaskRecord"),
    State(id("controlling_attachment"), "lastUploadTaskRecord"),
    State(id("applicant"), "value"),
    State(id("title"), "children"),
)
def save_data_to_database(
    n_clicks,
    deltapn,
    des,
    mfgname,
    mfgpn,
    cat1,
    cat2,
    cat3,
    attachment1,
    attachment2,
    applicant,
    title,
):
    if not n_clicks:
        raise PreventUpdate

    if not deltapn or not cat1 or not cat2 or not attachment1 or not attachment2:
        return notice("请填写必填项", "warning"), False

    db = get_db()
    user = db.find_one("ssp.user", {"nt_name": applicant})
    dept = user.get("dept")
    dept_id = user.get("dept_id")
    cc = ["Ru.Xue", "Ying.Gao"]
    if dept_id in (4, 5, 22):
        cc += ["Yuhan.Wang"]

    ce = "Huanhuan.Yu"
    sub_type = title[:-3]

    df = pd.DataFrame(
        {
            "deltapn": deltapn,
            "des": des,
            "mfgname": mfgname,
            "mfgpn": mfgpn,
            "cat1": cat1,
            "cat2": cat2,
            "cat3": cat3,
        },
        index=[0],
    )
    df = df.fillna("")
    df["type"] = "料号申请"
    df["sub_type"] = sub_type
    df["applicant"] = applicant
    df["ce"] = ce
    df["status"] = "open"
    df["urgent"] = "一般"
    df["dept"] = dept
    df["dept_id"] = dept_id
    df["model_id"] = 0
    df["cc"] = ",".join(cc)
    df["start_date"] = datetime.now()

    for i in df.itertuples(index=False):
        data = i._asdict()
        task_id = db.insert("ce.task", data)

        data1 = {
            "task_id": task_id,
            "deltapn": i.deltapn,
            "des": i.des,
            "mfgname": i.mfgname,
            "mfgpn": i.mfgpn,
            "cat1": i.cat1,
            "cat2": i.cat2,
            "cat3": i.cat3,
            "application_attachment": f'{attachment1["taskId"]}/{attachment1["fileName"]}',
            "controlling_attachment": f'{attachment2["taskId"]}/{attachment2["fileName"]}',
        }
        db.insert("ce.pn_sw", data1)

        # *---------邮件通知开始-----------*
        to = [applicant, ce] + cc
        to = ";".join(f"{i}@deltaww.com" for i in to)
        subject = f"【料号申请】{sub_type}"
        df = pd.DataFrame([data])
        columns = [
            "type",
            "dept",
            "applicant",
            "deltapn",
            "mfgname",
            "mfgpn",
            "start_date",
            "ce",
        ]
        df = df.reindex(columns=columns)
        df["start_date"] = df["start_date"].dt.date
        content = df_to_html(
            df,
            f"您的{sub_type}已提交,{ce}将会处理,请知悉！",
            href="http://sup.deltaww.com/info/rd?page=ongoing",
            link_text="工作进展可至个人中心查询",
        )
        bg_mail(to, subject, content)
        # *---------邮件通知结束-----------*

    return notice("提交成功"), True
