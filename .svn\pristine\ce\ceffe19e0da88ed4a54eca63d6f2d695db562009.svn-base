from datetime import datetime

import dash_bootstrap_components as dbc
import feffery_antd_components as fac
import pandas as pd
from config import UPLOAD_FOLDER_ROOT
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    MATCH,
    Input,
    Output,
    State,
    callback,
    callback_context,
    dash,
    dcc,
    html,
)
from dash_extensions.javascript import Namespace, assign
from dash_tabulator import DashTabulator
from db.ssp import User
from db.ssp_ext import Meeting, Meeting_category, Meeting_subject
from pony.orm import db_session, select
from tasks import bg_mail, df_to_html
import dash_mantine_components as dmc
from common import get_ssp_user

dash.register_page(__name__, title="会议", redirect_from=["/apps/meeting"])

ns = Namespace("myNamespace", "tabulator")
table_theme = "tabulator_site"

# rowFormatter = assign("function(){window.alert(123)}")
cellClick = assign('function(e, cell){window.alert("Printing row data for: ")}')

editCheck = assign(
    'function(cell){var data = cell.getRow().getData();return data.status==undefined || data.status=== "open";}'
)

searchFunc = assign(
    """
    function(term, values){
        var matches = [];
        window.alert(123);
        values.forEach(function(item){
            if(item.value === term){
                matches.push(item);
            }
        });

        return matches;
    }
    """
)

table_columns = [i.name for i in Meeting._attrs_]
create_table_columns = [
    {
        "title": i.title(),
        "field": i,
        "editor": "input",
        "formatter": "datetime" if i == "due_day" else "plaintext",
        "formatterParams": {"outputFormat": "YYYY/MM/DD"},
        "width": "60px" if i == "action_item_description" else "10px",
        "editable": editCheck,
    }
    for i in table_columns
    if i
    not in (
        "id",
        "status",
        "gmt_create",
        "recorded_by",
        "subject",
        "meeting_attendees",
        "action_item_update",
        "actual_close_day",
        "attendee",
        "approver_comment",
        "dept_id",
    )
]

porcess_table_columns = [
    {
        "title": i.title(),
        "field": i,
        "formatter": "datetime" if i == "due_day" else "plaintext",
        "formatterParams": {"outputFormat": "YYYY/MM/DD"},
    }
    for i in table_columns
    if i
    not in (
        "id",
        "gmt_create",
        "recorded_by",
        "attendee",
        "actual_close_day",
        "action_item_update",
        "owner",
        "approver",
        "status",
        "dept_id",
        "approver_comment",
    )
]

query_table_columns = [
    {
        "title": i.title(),
        "field": i,
    }
    for i in table_columns
    if i
    not in (
        "recorded_by",
        "gmt_create",
        "id",
        "dept_id",
        "approver_comment",
    )
]

for i in query_table_columns:
    if i["field"] in ("subject", "category", "owner", "approver"):
        i["headerFilter"] = "select"
        i["headerFilterParams"] = {"values": True}
    elif i["field"] == "status":
        i["headerFilter"] = "select"
        i["headerFilterParams"] = {"values": {1: "open", 2: "processing", 3: "close"}}
        i["formatter"] = "traffic"
        i["formatterParams"] = {
            "min": 1,
            "max": 3,
            "color": ["red", "orange", "green"],
        }
    elif i["field"] in ("due_day", "actual_close_day"):
        i["headerFilter"] = ns("dateEditor")
        i["headerFilterFunc"] = "="
    else:
        i["headerFilter"] = "input"

create_meeting_table = dbc.Spinner(
    DashTabulator(
        id="table-create-meeting",
        theme=table_theme,
        options={
            "selectable": 1,
            "height": "300px",
            "rowContextMenu": ns("RowMenu"),
            "clipboard": True,
        },
        data=[],
        columns=create_table_columns,
    )
)

process_meeting_table = dbc.Spinner(
    DashTabulator(
        id="table-process-meeting",
        theme=table_theme,
        options={
            "selectable": 1,
            "height": "440px",
            "layout": "fitColumns",
            "groupBy": ["status"],
            "groupHeader": ns("meetingProcessHeader"),
        },
        columns=porcess_table_columns,
    )
)

query_meeting_table = dbc.Spinner(
    DashTabulator(
        id="table-query-meeting",
        theme=table_theme,
        options={
            "selectable": 1,
            "layout": "fitColumns",
            "height": "400px",
            "clipboard": True,
            "clipboardCopyStyled": False,
            "clipboardPasteAction": "update",
            # "groupBy": ["subject", "category"],
            # "groupStartOpen": False,
        },
        data=[{}] * 9,
        downloadButtonType={
            "css": "btn btn-sm btn-outline-primary",
            "text": "Export",
            "type": "xlsx",
        },
        clearFilterButtonType={
            "css": "btn btn-sm btn-outline-dark",
            "text": "Clear Filters",
        },
        columns=query_table_columns,
    )
)

create_meeting = dmc.Stack(
    [
        dmc.Grid(
            [
                dmc.GridCol(
                    dmc.TextInput(
                        id="meeting-subject",
                        label="会议主题",
                    ),
                    span=3,
                ),
                dmc.GridCol(
                    dmc.MultiSelect(
                        id="meeting-attendee", label="与会人员", searchable=True
                    ),
                    span=9,
                ),
            ],
        ),
        create_meeting_table,
        dbc.Row(
            [
                dbc.Col(
                    dbc.Button(
                        className="fa fa-plus",
                        id="meeting-add-row",
                        size="sm",
                        color="light",
                    ),
                    width=1,
                ),
                dbc.Col(
                    dbc.Alert(
                        id="create-meeting-alert",
                        is_open=False,
                        duration=5000,
                        color="warning",
                    ),
                    width=9,
                ),
                dbc.Col(
                    dbc.Button("暂存", id="btn-save-meeting-create", color="success"),
                    width=1,
                ),
                dbc.Col(
                    dbc.Button("提交", id="btn-meeting-create"),
                    width=1,
                ),
            ],
            justify="between",
        ),
        dbc.Modal(
            [
                dbc.ModalHeader("Create Meeting Subject"),
                dbc.ModalBody(
                    [
                        dbc.RadioItems(
                            options=[
                                {"label": "主题会议", "value": 1},
                                {"label": "自定义会议", "value": 2},
                            ],
                            value=1,
                            id="cms-meeting-type",
                            inline=True,
                            labelCheckedClassName="text-success",
                            input_style={"margin-left": "75px"},
                            input_checked_style={"margin-left": "75px"},
                        ),
                        html.Br(),
                        dbc.Input(id="input-modal-cms", style={"display": "none"}),
                        dcc.Dropdown(
                            id="dropdown-modal-cms",
                            style={"border-color": "#1abc9c"},
                            placeholder="Select Meeting Subject",
                        ),
                    ]
                ),
                dbc.ModalFooter(
                    [
                        dbc.Alert(
                            id="alert-modal-cms",
                            is_open=False,
                            duration=5000,
                            color="warning",
                        ),
                        dbc.Button("Submit", id="btn-modal-cms", size="md"),
                        # dbc.Button(
                        #     "Close", id="btn-close-modal-cms", size="md", color="danger"
                        # ),
                    ]
                ),
            ],
            id="modal-cms",
            is_open=False,
            centered=True,
            backdrop=False,
        ),
    ]
)

modal_body = dmc.Stack(
    [
        html.Div(id="meeting-id", style={"display": "none"}),
        html.Div(id="modal-body-part1"),
        dmc.Textarea(id="action-item-update", label="Action item update"),
        dmc.Group(
            [
                fac.AntdUpload(
                    id="meeting-uploader",
                    apiUrl="/upload/",
                    multiple=True,
                    buttonContent="Upload attachment",
                ),
                dmc.DatePickerInput(id="new-due-day",
                placeholder="更新Due Day",
                size="xs",),
            ],
            align="flex-start",
        ),
        dmc.Group(id="meeting-attachment"),
        dbc.RadioItems(
            options=[
                {"label": "Reject", "value": "open"},
                {"label": "Approve", "value": "close"},
            ],
            value="close",
            id="radioitems-status",
            labelCheckedClassName="text-success",
            labelStyle={"color": "red"},
            inline=True,
        ),
        dbc.Textarea(
            id="approver-comment",
            placeholder="Enter your comment",
            hidden=True,
        ),
    ]
)

process_meeting = html.Div(
    [
        html.Br(),
        process_meeting_table,
        dbc.Modal(
            [
                dbc.ModalHeader(
                    id="modal-header-meeting-process", className="bg-light"
                ),
                dbc.ModalBody(modal_body),
                dbc.ModalFooter(
                    [
                        dbc.Alert(
                            id="alert-meeting-process",
                            is_open=False,
                            duration=3000,
                            color="danger",
                        ),
                        dbc.Button(
                            "save",
                            id="btn-save-meeting-process",
                            size="md",
                            color="success",
                        ),
                        dbc.Button(
                            "Submit",
                            id="btn-submit-meeting-process",
                            size="md",
                            color="primary",
                        ),
                    ],
                ),
            ],
            id="modal-meeting-process",
            centered=True,
            size="xl",
            # fullscreen="xxl-down"
            # backdrop=False,
        ),
    ],
)

query_meeting = html.Div(
    [
        html.Br(),
        query_meeting_table,
    ]
)

manage_meeting = html.Div(
    [
        html.Br(),
        dbc.Spinner(
            dbc.Row(
                [
                    dbc.Col(
                        html.Div(
                            [
                                dbc.Button(
                                    className="fa fa-plus btn-success btn-sm",
                                    id="add-meeting-subject",
                                    n_clicks_timestamp=0,
                                ),
                                dbc.Button(
                                    className="fa fa-remove btn-danger btn-sm",
                                    id="del-meeting-subject",
                                    n_clicks_timestamp=0,
                                ),
                                DashTabulator(
                                    id="table-meeting-subject",
                                    theme=table_theme,
                                    options={
                                        "selectable": True,
                                        "height": "380px",
                                    },
                                    columns=[
                                        {
                                            "formatter": "rowSelection",
                                            "titleFormatter": "rowSelection",
                                            "hozAlign": "center",
                                            "headerSort": False,
                                            "frozen": True,
                                            "width": 30,
                                            "minWidth": 30,
                                        },
                                        {
                                            "title": "Subject",
                                            "field": "subject",
                                            "editor": "input",
                                        },
                                    ],
                                ),
                                dbc.Row(
                                    [
                                        dbc.Col(
                                            dbc.Button(
                                                "Submit", id="submit-meeting-subject"
                                            ),
                                            width=2,
                                        ),
                                        dbc.Col(
                                            dbc.Alert(
                                                id="alert-meeting-subject",
                                                is_open=False,
                                                duration=5000,
                                            ),
                                            width=8,
                                        ),
                                    ]
                                ),
                            ]
                        ),
                        width=6,
                    ),
                    dbc.Col(
                        html.Div(
                            [
                                dbc.Button(
                                    className="fa fa-plus btn-success btn-sm",
                                    id="add-meeting-category",
                                    n_clicks_timestamp=0,
                                ),
                                dbc.Button(
                                    className="fa fa-remove btn-danger btn-sm",
                                    id="del-meeting-category",
                                    n_clicks_timestamp=0,
                                ),
                                DashTabulator(
                                    id="table-meeting-category",
                                    theme=table_theme,
                                    options={
                                        "selectable": True,
                                        "height": "380px",
                                    },
                                    columns=[
                                        {
                                            "formatter": "rowSelection",
                                            "titleFormatter": "rowSelection",
                                            "hozAlign": "center",
                                            "headerSort": False,
                                            "frozen": True,
                                            "width": 30,
                                            "minWidth": 30,
                                        },
                                        {
                                            "title": "Category",
                                            "field": "category",
                                            "editor": "input",
                                        },
                                    ],
                                ),
                                dbc.Row(
                                    [
                                        dbc.Col(
                                            dbc.Button(
                                                "Submit", id="submit-meeting-category"
                                            ),
                                            width=2,
                                        ),
                                        dbc.Col(
                                            dbc.Alert(
                                                id="alert-meeting-category",
                                                is_open=False,
                                                duration=5000,
                                            ),
                                            width=8,
                                        ),
                                    ]
                                ),
                            ]
                        ),
                        width=6,
                    ),
                ]
            )
        ),
    ],
)


def layout(**kwargs):
    create_meeting = dmc.Stack(
        [
            dmc.Grid(
                [
                    dmc.GridCol(
                        dmc.TextInput(
                            id="meeting-subject",
                            label="会议主题",
                        ),
                        span=3,
                    ),
                    dmc.GridCol(
                        dmc.MultiSelect(
                            id="meeting-attendee",
                            label="与会人员",
                            searchable=True,
                            data=get_ssp_user()["nt_name"].tolist(),
                        ),
                        span=9,
                    ),
                ],
            ),
            create_meeting_table,
            dbc.Row(
                [
                    dbc.Col(
                        dbc.Button(
                            className="fa fa-plus",
                            id="meeting-add-row",
                            size="sm",
                            color="light",
                        ),
                        width=1,
                    ),
                    dbc.Col(
                        dbc.Alert(
                            id="create-meeting-alert",
                            is_open=False,
                            duration=5000,
                            color="warning",
                        ),
                        width=9,
                    ),
                    dbc.Col(
                        dbc.Button(
                            "暂存", id="btn-save-meeting-create", color="success"
                        ),
                        width=1,
                    ),
                    dbc.Col(
                        dbc.Button("提交", id="btn-meeting-create"),
                        width=1,
                    ),
                ],
                justify="between",
            ),
        ]
    )
    return dbc.Container(
        [
            dbc.Tabs(
                [
                    dbc.Tab(create_meeting, label="创建", tab_id="tab-1"),
                    dbc.Tab(process_meeting, label="处理", tab_id="tab-2"),
                    dbc.Tab(query_meeting, label="查询", tab_id="tab-3"),
                    dbc.Tab(
                        manage_meeting,
                        label="管理",
                        tab_id="tab-4",
                        id="meeting-manage-tab",
                        disabled=True,
                    ),
                ],
                id="meeting-tabs",
                active_tab="tab-2",
                style={"color": "#16a085"},
            )
        ],
        fluid=True,
        # className="ml-3 pr-5",
    )


# * ---------创建----------------
# @callback(
#     Output("table-create-meeting", "columns"),
#     Input("meeting-tabs", "active_tab"),
#     State("user", "data"),
# )
# @db_session
def initial_dropdown_options(active_tab, user):
    """初始化下拉菜单"""
    dept_id = user.get("dept_id")
    users = sorted(
        j.title() for j in select(i.nt_name for i in User if not i.termdate)[:]
    )

    nt_name = user.get("nt_name").lower()
    meeting_subject_options = [
        {"label": i, "value": i}
        for i in select(
            i.subject for i in Meeting if (i.recorded_by).lower() == nt_name
        )[:]
    ]
    meeting_attendee_options = [{"label": i, "value": i} for i in users]

    category = select(i.category for i in Meeting_category if i.dept_id == dept_id)[:]
    # breakpoint()

    for i in create_table_columns:
        if i["field"] == "category":
            i["editor"] = "autocomplete"
            i["editorParams"] = {
                "values": [i for i in category],
                "showListOnEmpty": True,
            }

        elif i["field"] == "owner":
            i["editor"] = "autocomplete"  # "select"
            i["editorParams"] = {
                "values": users,
                "multiselect": True,
                "showListOnEmpty": True,
                "freetext": True,
            }

        elif i["field"] == "approver":
            i["editor"] = "autocomplete"
            i["editorParams"] = {
                "values": users,
                "showListOnEmpty": True,
            }

        elif i["field"] == "due_day":
            i["editor"] = ns("dateEditor")

    return (
        meeting_subject_options,
        meeting_attendee_options,
        create_table_columns,
    )


@callback(
    Output("modal-cms", "is_open"),
    Input("add-new-meeting-subject", "n_clicks"),
)
def create_meeting_subject(n_clicks):
    """创建新meeting-subject"""
    if n_clicks:
        return True
    else:
        raise PreventUpdate


@callback(
    Output("table-create-meeting", "data"),
    Input("meeting-add-row", "n_clicks"),
    State("table-create-meeting", "data"),
)
def table_create_meeting_add_row(n_clicks, data):
    """create_meeting_table插入行"""
    if n_clicks:
        data.append({})
        return data
    else:
        raise PreventUpdate


@callback(
    Output("table-create-meeting", "data"),
    Input("table-create-meeting", "dataChanged"),
    State("table-create-meeting", "data"),
)
def update_data(new_data, old_data):
    """删除行时更新数据"""
    if new_data is None:
        raise PreventUpdate

    if len(new_data) < len(old_data):
        return new_data
    else:
        raise PreventUpdate


@callback(
    Output("dropdown-modal-cms", "options"),
    Input("modal-cms", "is_open"),
    State("user", "data"),
)
@db_session
def meeting_subject_dropdown(is_open, user):
    """meeting-subject下拉菜单"""
    if not is_open:
        raise PreventUpdate
    dept_id = user.get("dept_id")
    subject = select(i.subject for i in Meeting_subject if i.dept_id == dept_id)[:]
    options = [{"label": i, "value": i} for i in subject]
    return options


@callback(
    [Output("dropdown-modal-cms", "style"), Output("input-modal-cms", "style")],
    Input("cms-meeting-type", "value"),
)
@db_session
def meeting_type_radio(value):
    """meeting subject类型"""
    if value == 1:
        return {"display": "block"}, {"display": "none"}
    elif value == 2:
        return {"display": "none"}, {"display": "block"}


@callback(
    [
        Output("alert-modal-cms", "is_open"),
        Output("alert-modal-cms", "color"),
        Output("alert-modal-cms", "children"),
    ],
    Input("btn-modal-cms", "n_clicks"),
    [
        State("cms-meeting-type", "value"),
        State("input-modal-cms", "value"),
        State("dropdown-modal-cms", "value"),
        State("user", "data"),
    ],
)
@db_session
def modal_cms_btn(n_clicks, meeting_type, input_value, dropdown_value, user):
    """meeting subject类型"""
    if meeting_type == 1:
        subject = f"{dropdown_value}:{datetime.now():%Y/%m/%d}"
    else:
        subject = f"{input_value}:{datetime.now():%Y/%m/%d}"
    nt_name = user.get("nt_name")
    meeting_exists = select(
        i.subject
        for i in Meeting
        if (i.subject == subject) and (i.recorded_by == nt_name)
    )[:]
    if meeting_exists:
        return True, "danger", "Meeting subject already exists"
    else:
        return True, "success", "Meeting subject create successful"


@callback(
    Output("modal-cms", "is_open"),
    Input("alert-modal-cms", "color"),
)
def close_modal_cms(color):
    """关闭弹窗"""
    if color == "success":
        return False
    else:
        raise PreventUpdate


@callback(
    [
        Output("meeting-subject", "options"),
        Output("meeting-subject", "value"),
    ],
    Input("alert-modal-cms", "color"),
    [
        State("meeting-subject", "options"),
        State("cms-meeting-type", "value"),
        State("input-modal-cms", "value"),
        State("dropdown-modal-cms", "value"),
    ],
)
@db_session
def update_meeting_subject_options(color, options, meeting_type, input, dropdown):
    """更新meeting_subject下拉菜单"""
    if color == "success":
        if meeting_type == 1:
            value = f"{dropdown}:{datetime.now():%Y/%m/%d}"
        else:
            value = f"{input}:{datetime.now():%Y/%m/%d}"
        # Meeting_subject(subject=value)
        options = [{"label": value, "value": value}] + options
        return options, value
    else:
        raise PreventUpdate


@callback(
    [
        Output("create-meeting-alert", "children"),
        Output("create-meeting-alert", "is_open"),
        Output("create-meeting-alert", "color"),
    ],
    Input("btn-meeting-create", "n_clicks"),
    Input("btn-save-meeting-create", "n_clicks"),
    [
        State("meeting-subject", "value"),
        State("meeting-attendee", "value"),
        State("table-create-meeting", "data"),
        State("user", "data"),
    ],
)
@db_session
def create_submit_alert(n_clicks1, n_clicks2, subject, attendee, data, user):
    """提交创建会议"""
    if not (subject and attendee and any(data)):
        return "Subject,Attendee,会议内容不能为空", True, "danger"
    nt = user.get("nt_name")
    dept_id = user.get("dept_id")
    att = ",".join(attendee) or nt
    for i in data:
        if i:
            data_set = {
                "category",
                "action_item_description",
                "owner",
                "due_day",
                "approver",
            } - set(i)
            if data_set:
                return f"{','.join(data_set)}不能为空", True, "danger"

            owner = i["owner"]
            if isinstance(owner, list):
                owner = ",".join(owner)

            id = i.get("id")
            if id:
                mt = Meeting.get(id=id)
                if mt.status == "open":
                    mt.category = i["category"]
                    mt.action_item_description = i["action_item_description"]
                    mt.owner = owner
                    mt.due_day = i["due_day"]
                    mt.approver = i["approver"]
                    mt.attendee = att
            else:
                Meeting(
                    subject=subject,
                    category=i["category"],
                    action_item_description=i["action_item_description"],
                    owner=owner,
                    due_day=i["due_day"],
                    approver=i["approver"],
                    recorded_by=nt,
                    attendee=att,
                    dept_id=dept_id,
                )

    trigger = callback_context.triggered[0]["prop_id"].split(".")[0]
    if trigger == "btn-meeting-create":
        alert = "提交成功"
        df = pd.DataFrame(data)
        df = df.reindex(
            columns=[
                "category",
                "action_item_description",
                "owner",
                "due_day",
                "approver",
            ]
        )
        df = df.dropna()
        df["action_item_description"] = df["action_item_description"].str.replace(
            "\n", "<br>"
        )

        to = df["owner"].str.split(",").explode().unique().tolist() + attendee + [nt]
        to = {f"{i}@deltaww.com" for i in to}
        to = ";".join(to)
        title = f"Dear ALL,<br>以下是本次{subject}的会议记录,请查收<br>"
        body = df_to_html(df, title=title)
        href = "http://sup.deltaww.com/apps/meeting"
        style = (
            "display:block;background-color:#00a0e9;height:40px;width:40px;color:white"
        )
        link = f"<a href={href} style={style}>进入会议记录系统</a>"
        body = f"{body}<br>{link}"
        sub = f"【会议记录】{subject}"
        bg_mail(to, sub, body)
    else:
        alert = "保存成功"
    return alert, True, "success"


@callback(
    Output("table-create-meeting", "data"),
    Output("meeting-attendee", "value"),
    Input("meeting-subject", "value"),
    State("user", "data"),
)
@db_session
def meeting_subject_change_value(subject, user):
    """选择已有meeting-subject"""
    if not subject:
        return [], []
    nt_name = user.get("nt_name")

    meetings = select(
        i for i in Meeting if (i.subject == subject) and (i.recorded_by == nt_name)
    )[:]
    if meetings:
        data = [i.to_dict(with_lazy=True) for i in meetings]
        attendee = sorted(set(",".join(i.attendee for i in meetings).split(",")))
        return data, attendee
    else:
        return [], []


@callback(
    Output("meeting-subject", "value"),
    Input("create-meeting-alert", "color"),
)
def refresh_create_meeting_table(color):
    """提交成功时刷新页面"""
    if color == "success":
        return None
    else:
        raise PreventUpdate


# # *------------处理---------------
@callback(
    Output("table-process-meeting", "data"),
    Input("meeting-tabs", "active_tab"),
    State("user", "data"),
    prevent_initial_call=False,
)
@db_session
def initial_table_process_meeting_data(active_tab, user):
    if active_tab != "tab-2":
        raise PreventUpdate
    nt = user.get("nt_name")
    meetings = Meeting.select(
        lambda x: (nt in x.owner and x.status == "open")
        or (x.approver == nt and x.status == "processing")
    )[:]
    data = [i.to_dict(with_lazy=True) for i in meetings]
    return data


@callback(
    [
        Output("modal-meeting-process", "is_open"),
        Output("modal-header-meeting-process", "children"),
        Output("modal-body-part1", "children"),
        Output("radioitems-status", "style"),
        Output("radioitems-status", "value"),
        Output("action-item-update", "value"),
        Output("action-item-update", "disabled"),
        Output("meeting-attachment", "children"),
        Output("meeting-id", "children"),
        Output("meeting-uploader", "uploadId"),
        Output("btn-submit-meeting-process", "style"),
        Output("btn-save-meeting-process", "style"),
        Output("approver-comment", "hidden"),
        Output("approver-comment", "value"),
        Output("approver-comment", "disabled"),
        # Output("btn-submit-meeting-process", "style"),
    ],
    Input("table-process-meeting", "multiRowsClicked"),
    Input("table-query-meeting", "multiRowsClicked"),
    State("user", "data"),
)
@db_session
def open_process_meeting_page(mrc1, mrc2, user):
    trigger = callback_context.triggered[0]["prop_id"].split(".")[0]
    if trigger == "table-process-meeting":
        if not mrc1:
            raise PreventUpdate
        meeting_id = mrc1[0]["id"]
    else:
        if not mrc2:
            raise PreventUpdate
        meeting_id = mrc2[0]["id"]

    mt = Meeting.get(id=meeting_id)
    id = mt.id
    header = mt.subject
    category = mt.category
    due_day = mt.due_day
    recorded_by = mt.recorded_by
    approver = mt.approver
    owner = mt.owner
    status = mt.status
    aid = mt.action_item_description
    aiu = mt.action_item_update
    create_day = f"{mt.gmt_create:%Y-%m-%d}"
    approver_comment = mt.approver_comment
    nt = user.get("nt_name").lower()

    if (nt == approver.lower()) and (status == "processing"):
        comment_hidden = False
        comment_disabled = False
    elif nt == owner.lower():
        if approver_comment:
            comment_hidden = False
        else:
            comment_hidden = True
        comment_disabled = True
    else:
        comment_hidden = True
        comment_disabled = True

    attachment_dir = UPLOAD_FOLDER_ROOT / f"meeting_attachment_{id}"
    if attachment_dir.exists():
        attachment = [
            html.Div(
                [
                    dcc.Download(
                        id={"type": "download-meeting-attachment", "index": i.stem},
                    ),
                    dbc.Button(
                        f"{i.name}",
                        # variant="outline",
                        size="sm",
                        color="link",
                        # outline=True,
                        download=i.as_posix(),
                        # compact=True,
                        id={
                            "type": "btn-download-meeting-attachment",
                            "index": i.stem,
                        },
                    ),
                ]
            )
            for i in attachment_dir.glob("*")
        ]
    else:
        attachment = None

    if status == "open":
        radio_style = {"display": "none"}
        aiu_disabled = False

    elif status == "processing":
        radio_style = {"display": "block"}
        aiu_disabled = True

    submit_style = None
    save_style = None
    if trigger == "table-query-meeting":
        radio_style = {"display": "none"}
        aiu_disabled = True
        submit_style = {"display": "none"}
        save_style = {"display": "none"}
        comment_disabled = True

    part1 = dmc.Stack(
        [
            fac.AntdDescriptions(
                [
                    fac.AntdDescriptionItem(category, label="Category"),
                    fac.AntdDescriptionItem(create_day, label="Create Day"),
                    fac.AntdDescriptionItem(due_day, label="Due Day"),
                    fac.AntdDescriptionItem(recorded_by, label="Recorded By"),
                    fac.AntdDescriptionItem(owner, label="Owner"),
                    fac.AntdDescriptionItem(approver, label="Approver"),
                ],
                bordered=True,
                column=3,
                size="small",
            ),
            dmc.Textarea(value=aid, label="Action item description"),
        ]
    )
    return (
        True,
        header,
        part1,
        radio_style,
        "close",
        aiu,
        aiu_disabled,
        attachment,
        id,
        attachment_dir.stem,
        submit_style,
        save_style,
        comment_hidden,
        approver_comment,
        comment_disabled,
    )


@callback(
    Output("alert-meeting-process", "is_open"),
    Output("alert-meeting-process", "children"),
    Output("alert-meeting-process", "color"),
    Input("btn-submit-meeting-process", "n_clicks"),
    State("radioitems-status", "value"),
    State("action-item-update", "value"),
    State("meeting-id", "children"),
    State("approver-comment", "value"),
    State("new-due-day", "value"),
)
@db_session
def submit_process_meeting(n, radio, aiu, id, approver_comment, new_due_day):
    if n is None:
        raise PreventUpdate

    if not aiu:
        return True, "Action Item Update不能为空", "danger"

    mt = Meeting.get(id=id)
    mt.action_item_update = aiu
    if new_due_day:
        mt.due_day = new_due_day

    if mt.status == "open":
        mt.status = "processing"
        action = "更新"
        to = f"{mt.approver}@deltaww.com"
    else:
        mt.status = radio
        mt.approver_comment = approver_comment
        if radio == "close":
            action = "核准"
            mt.actual_close_day = datetime.now()
        else:
            action = "退回"
        to = mt.owner.split(",")
        to = ";".join(f"{i}@deltaww.com" for i in to)

    if mt.approver != mt.owner:
        sub = f"【会议{action}】{mt.subject}"
        df = pd.DataFrame([mt.to_dict(with_lazy=True)])
        col = [
            "subject",
            "category",
            "action_item_description",
            "action_item_update",
            "owner",
            "due_day",
            "approver",
            "approver_comment",
        ]
        df = df.reindex(columns=col)
        df["action_item_update"] = aiu.replace("\n", "<br>")
        title = f"Dear Sir,<br>以下是本次【{mt.subject}】的{action}内容<br>"
        body = df_to_html(df, title=title)
        href = "http://sup.deltaww.com/apps/meeting"
        style = (
            "display:block;background-color:#00a0e9;height:40px;width:40px;color:white"
        )
        link = f"<a href={href} style={style}>进入会议记录系统</a>"
        body = f"{body}<br>{link}"
        bg_mail(to, sub, body)

    return True, "提交成功", "success"


@callback(
    Output("btn-submit-meeting-process", "disabled"),
    Output("btn-save-meeting-process", "disabled"),
    Input("alert-meeting-process", "color"),
)
@db_session
def disable_submit(color):
    """提交成功后禁止再次提交"""
    if color == "success":
        return True, True
    else:
        return False, False


@callback(
    Output("alert-meeting-process", "color"),
    Input("modal-meeting-process", "is_open"),
)
@db_session
def reflesh_color_when_modal_close(is_open):
    """关闭窗口,重新激活submit按钮"""
    if not is_open:
        return "danger"
    else:
        raise PreventUpdate


@callback(
    Output("alert-meeting-process", "is_open"),
    Output("alert-meeting-process", "children"),
    Output("alert-meeting-process", "color"),
    Input("btn-save-meeting-process", "n_clicks"),
    State("action-item-update", "value"),
    State("meeting-id", "children"),
    State("approver-comment", "value"),
    State("new-due-day", "value"),
)
@db_session
def save_action_item_update(n, value, meeting_id, approver_comment, new_due_day):
    """仅保存action_item_update,不发出审核"""
    if not n:
        raise PreventUpdate
    if not value:
        return True, "Action Item Update不能为空", "danger"
    mt = Meeting.get(id=meeting_id)
    mt.action_item_update = value
    if approver_comment:
        mt.approver_comment = approver_comment
    if new_due_day:
        mt.due_day = new_due_day

    return True, "已保存更新内容", "warning"


@callback(
    Output("meeting-tabs", "active_tab"),
    Input("modal-meeting-process", "is_open"),
    State("meeting-tabs", "active_tab"),
)
def reflesh_processing_table(is_open, active_tab):
    if active_tab == "tab-3":
        raise PreventUpdate

    if is_open == False:
        return "tab-2"
    else:
        raise PreventUpdate


@callback(
    Output({"type": "download-meeting-attachment", "index": MATCH}, "data"),
    Input({"type": "btn-download-meeting-attachment", "index": MATCH}, "n_clicks"),
    State({"type": "btn-download-meeting-attachment", "index": MATCH}, "download"),
)
def download_meeting_attachment(n, file):
    return dcc.send_file(file)


@callback(
    Output({"type": "delete-meeting-attachment", "index": MATCH}, "children"),
    Input({"type": "delete-meeting-attachment", "index": MATCH}, "is_open"),
    State({"type": "delete-meeting-attachment", "index": MATCH}, "id"),
    State("meeting-id", "children"),
)
def delete_meeting_attachment(is_open, id, meeting_id):
    if is_open == False:
        file_name = id["index"]
        file_path = UPLOAD_FOLDER_ROOT / f"meeting_attachment_{meeting_id}" / file_name
        if file_path.exists():
            file_path.unlink()
    raise PreventUpdate


# *------------查询---------------
@callback(
    Output("table-query-meeting", "data"),
    Output("table-query-meeting", "columns"),
    Input("meeting-tabs", "active_tab"),
    State("user", "data"),
    State("table-query-meeting", "columns"),
)
@db_session
def initial_table_query_meeting_data(active_tab, user, columns):
    if active_tab != "tab-3":
        raise PreventUpdate
    nt = user.get("nt_name")
    role = user.get("role_group")
    if role.lower() == "manager":
        dept_id = user.get("dept_id")
        meetings = Meeting.select(lambda x: x.dept_id == dept_id)[:]
    else:
        meetings = Meeting.select(
            lambda x: nt in x.attendee
            or nt in x.owner
            or nt in x.approver
            or nt in x.recorded_by
        )[:]
    data = [i.to_dict(with_lazy=True) for i in meetings]
    d = {"open": 1, "processing": 2, "close": 3}
    for i in data:
        i["status"] = d.get(i["status"], 1)

    header_filter_params = list({i["subject"].split(":")[0] for i in data})
    for i in columns:
        if i["field"] == "subject":
            i["headerFilterParams"] = header_filter_params
    return data, columns


# *------------管理---------------
@callback(
    Output("meeting-manage-tab", "disabled"),
    Input("user", "data"),
    prevent_initial_call=False,
)
def manage_limit_authority(user):
    """管理权限"""
    role = user.get("role_group")
    if role == "PM":
        return False
    else:
        raise PreventUpdate


@callback(
    Output("table-meeting-subject", "data"),
    Output("table-meeting-category", "data"),
    Input("meeting-tabs", "active_tab"),
    State("user", "data"),
)
@db_session
def active_manage_meeting_data(active_tab, user):
    if active_tab != "tab-4":
        raise PreventUpdate
    dept_id = user.get("dept_id")

    subject = select(i for i in Meeting_subject if i.dept_id == dept_id)[:]
    category = select(i for i in Meeting_category if i.dept_id == dept_id)[:]
    if subject:
        subject_data = [i.to_dict() for i in subject]
    else:
        subject_data = [{}]

    if category:
        category_data = [i.to_dict() for i in category]
    else:
        category_data = [{}]
    return subject_data, category_data


@callback(
    Output("table-meeting-subject", "data"),
    Input("add-meeting-subject", "n_clicks_timestamp"),
    Input("del-meeting-subject", "n_clicks_timestamp"),
    State("table-meeting-subject", "data"),
    State("table-meeting-subject", "multiRowsClicked"),
)
def add_meeting_subject_row(click_add, click_del, data, row_selected):
    """add_del_meeting_subject_row"""
    if not data:
        raise PreventUpdate
    if click_add > click_del:
        data.append({})
    else:
        if row_selected:
            data = [
                i
                for i in data
                if i["subject"] not in (j["subject"] for j in row_selected)
            ]
    return data


@callback(
    Output("table-meeting-category", "data"),
    Input("add-meeting-category", "n_clicks_timestamp"),
    Input("del-meeting-category", "n_clicks_timestamp"),
    State("table-meeting-category", "data"),
    State("table-meeting-category", "multiRowsClicked"),
)
def add_meeting_category_row(click_add, click_del, data, row_selected):
    """add_del_meeting_category_row"""
    if not data:
        raise PreventUpdate
    if click_add > click_del:
        data.append({})
    else:
        if row_selected:
            data = [
                i
                for i in data
                if i["category"] not in (j["category"] for j in row_selected)
            ]
    return data


@callback(
    Output("alert-meeting-category", "children"),
    Output("alert-meeting-category", "is_open"),
    Input("submit-meeting-category", "n_clicks"),
    State("table-meeting-category", "data"),
    State("user", "data"),
)
@db_session
def submit_meeting_category(n, data, user):
    """submit_meeting_category"""
    if not data:
        return "请先输入category", True
    dept_id = user.get("dept_id")
    nt = user.get("nt_name")
    Meeting_category.select(lambda x: x.dept_id == dept_id).delete(bulk=True)
    for i in data:
        Meeting_category(dept_id=dept_id, category=i["category"], owner=nt)
    return "Category修改成功", True


@callback(
    Output("alert-meeting-subject", "children"),
    Output("alert-meeting-subject", "is_open"),
    Input("submit-meeting-subject", "n_clicks"),
    State("table-meeting-subject", "data"),
    State("user", "data"),
)
@db_session
def submit_meeting_subject(n, data, user):
    """submit_meeting_subject"""
    if not data:
        return "请先输入subject", True

    dept_id = user.get("dept_id")
    nt = user.get("nt_name")
    Meeting_subject.select(lambda x: x.dept_id == dept_id).delete(bulk=True)
    for i in data:
        Meeting_subject(dept_id=dept_id, subject=i["subject"], owner=nt)
    return "Subject修改成功", True
