# -*- coding: utf-8-*-
from datetime import datetime, timedelta

import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
import feffery_antd_components.alias as fac
import numpy as np
import pandas as pd
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    Input,
    Output,
    State,
    callback,
    callback_context,
    dash,
    dcc,
    html,
    no_update,
)
from dash_extensions.javascript import Namespace
from dash_tabulator import DashTabulator
from pony.orm import db_session

from db.ssp import Dept, Project, Prt, Prt_temp, User
from tasks import bg_access_record, bg_mail

dash.register_page(__name__, title="项目")

ns = Namespace("myNamespace", "tabulator")

tab1_content = dmc.Stack(
    [
        dbc.Modal(
            [
                dbc.ModalHeader("请输入项目名称:"),
                dbc.ModalBody(dbc.Input(id="proj-new-model")),
                dbc.<PERSON>ooter(
                    [
                        dbc.<PERSON><PERSON>(id="alert-modal-proj", is_open=False, duration=5000),
                        dbc.<PERSON><PERSON>(
                            "提交", id="btn-modal-foot-proj", color="success", size="md"
                        ),
                    ]
                ),
            ],
            id="modal-proj",
            centered=True,
        ),
        dmc.Space(h=10),
        dbc.Row(
            [
                dbc.Col(
                    dcc.Dropdown(
                        placeholder="选择部门", clearable=False, id="proj-dept-dropdown"
                    ),
                    width=2,
                ),
                dbc.Col(
                    dcc.Dropdown(
                        clearable=False,
                        id="project-dropdown",
                        placeholder="选择项目",
                    ),
                    width=3,
                ),
                dbc.Col(
                    dbc.Button(
                        className="fa fa-plus mt-1",
                        id="btn-add-new-proj",
                        color="light",
                    )
                ),
            ],
        ),
        dbc.Row(
            [
                dbc.Col(
                    dbc.InputGroup(
                        [
                            dbc.InputGroupText("EE"),
                            dcc.Dropdown(
                                id="proj-ee",
                                style={"width": 180},
                            ),
                        ]
                    ),
                    width=3,
                ),
                dbc.Col(
                    dbc.InputGroup(
                        [
                            dbc.InputGroupText("ME"),
                            dcc.Dropdown(
                                id="proj-me",
                                style={"width": 180},
                            ),
                        ]
                    ),
                    width=3,
                ),
                dbc.Col(
                    dbc.InputGroup(
                        [
                            dbc.InputGroupText("MAG"),
                            dcc.Dropdown(
                                id="proj-mag",
                                style={"width": 180},
                            ),
                        ]
                    ),
                    width=3,
                ),
                dbc.Col(
                    dbc.InputGroup(
                        [
                            dbc.InputGroupText("Layout"),
                            dcc.Dropdown(
                                id="proj-layout",
                                style={"width": 180},
                            ),
                        ]
                    ),
                    width=3,
                ),
            ]
        ),
        DashTabulator(
            id="proj-pcb-table",
            theme="tabulator_site",
            columns=[
                {"title": "PCB名称", "field": "pcb", "editor": "input"},
                {"title": "PCB料号", "field": "pcbpn", "editor": "input"},
                {"title": "EE(选填)", "field": "ee", "editor": "autocomplete"},
                {"title": "ME(选填)", "field": "me", "editor": "autocomplete"},
                {
                    "title": "LAYOUT(选填)",
                    "field": "layout",
                    "editor": "autocomplete",
                },
            ],
            data=[{}],
        ),
        dbc.Row(
            [
                dbc.Col(
                    dbc.Button("AddRow", id="pcb-add-row", color="success", size="sm"),
                    width=1,
                ),
                dbc.Col(
                    dbc.Alert(id="project-alert", is_open=False, duration=5000),
                    width=10,
                ),
                dbc.Col(
                    dbc.Button(
                        "提交",
                        id="submit-project",
                        color="primary",
                        size="md",
                    ),
                    width=1,
                ),
            ],
            justify="between",
        ),
    ]
)

tab2_content = html.Div(
    [
        dbc.Modal(
            [
                dbc.ModalHeader("请输入样制日期和数量:"),
                dbc.ModalBody(
                    dbc.Row(
                        [
                            dbc.Col(
                                dbc.InputGroup(
                                    [
                                        dbc.InputGroupText("需求日期"),
                                        dcc.DatePickerSingle(
                                            id="sm-date", display_format="YYYY-MM-DD"
                                        ),
                                    ]
                                ),
                                width=4,
                            ),
                            dbc.Col(
                                dbc.InputGroup(
                                    [
                                        dbc.InputGroupText("样制数量"),
                                        dbc.Input(id="sm-qty", type="number", min=1),
                                    ]
                                ),
                                width=4,
                            ),
                            dbc.Col(
                                dbc.InputGroup(
                                    [
                                        dbc.InputGroupText("首样数量"),
                                        dbc.Input(id="sm-fsqty", type="number", min=1),
                                    ]
                                ),
                                width=4,
                            ),
                        ]
                    )
                ),
                dbc.ModalFooter(
                    dbc.Button(
                        "提交",
                        id="btn-modal-foot-sm",
                        color="success",
                        size="md",
                        n_clicks_timestamp=0,
                    ),
                ),
            ],
            id="modal-sm",
            centered=True,
            size="lg",
        ),
        html.Br(),
        dbc.Row(
            [
                dbc.Col(
                    dcc.Dropdown(
                        placeholder="选择部门", clearable=False, id="sm-dept-dropdown"
                    ),
                    width=2,
                ),
                dbc.Col(
                    dcc.Dropdown(
                        placeholder="选择项目", clearable=False, id="sm-proj-dropdown"
                    ),
                    width=4,
                ),
                dbc.Col(
                    dcc.Dropdown(
                        placeholder="选择阶段", clearable=False, id="sm-stage-dropdown"
                    ),
                    width=2,
                ),
                dbc.Col(
                    dbc.Button(
                        className="fa fa-plus mt-1",
                        id="btn-add-new-stage",
                        color="light",
                        n_clicks_timestamp=0,
                    )
                ),
            ],
            align="start",
        ),
        html.Pre(),
        DashTabulator(
            id="sm-table",
            theme="tabulator_site",
            columns=[
                {
                    "title": "ACTION",
                    "field": "action",
                    "editor": "select",
                    "editorParams": ["申请", "多方案", "不申请"],
                    "width": 100,
                },
                {"title": "project_id", "field": "project_id", "visible": False},
                {"title": "PCB名称", "field": "pcb", "editor": "input"},
                {"title": "PCB料号", "field": "pcbpn", "editor": "input"},
                {
                    "title": "PCB状况",
                    "field": "pcbstatus",
                    "editor": "select",
                    "editorParams": ["需采购", "库存板", "工厂调"],
                    "width": 80,
                },
                {
                    "title": "需求日期",
                    "field": "fsdate_req",
                    "editor": ns("dateEditor"),
                },
                {
                    "title": "样制数量",
                    "field": "qty",
                    "editor": "input",
                    "validator": "numeric",
                    "width": 70,
                },
                {
                    "title": "首样数量",
                    "field": "fsqty",
                    "editor": "input",
                    "validator": "numeric",
                    "width": 70,
                },
                {"title": "申请状态", "field": "status", "width": 90},
                {"title": "PCB库存", "field": "pcb_stock", "width": 90},
                {"title": "单机用量", "field": "qpa", "width": 70},
                {"title": "scheme", "field": "scheme", "visible": False},
                {"title": "ee", "field": "ee", "visible": False},
                {"title": "me", "field": "me", "visible": False},
                {"title": "layout", "field": "layout", "visible": False},
                {"title": "id", "field": "id", "visible": False},
            ],
        ),
        dbc.Row(
            [
                dbc.Col(
                    dbc.Alert(
                        id="sm-alert",
                        fade=False,
                        is_open=False,
                        duration=5000,
                        dismissable=True,
                    ),
                    width=9,
                ),
                dbc.Col(
                    fac.Popconfirm(
                        dbc.Button("提交", color="primary", id="submit-sm"),
                        title=dmc.List(
                            [
                                dmc.ListItem(
                                    "请确认是否样制申请数量正确(有无板子是2倍及以上用量)?"
                                ),
                                dmc.ListItem("请确认是否有工厂调料PCB或者库存板?"),
                            ]
                        ),
                        trigger="click",
                        id="sm-submit-confirm",
                        placement="left",
                    ),
                    width=1,
                ),
            ],
            justify="end",
            className="my-1",
        ),
        dbc.Modal(
            [
                dbc.ModalHeader("请选择方案类型,并输入方案数量"),
                dbc.ModalBody(
                    [
                        dbc.RadioItems(
                            options=[
                                {"label": "PCB相同,但BOM不同", "value": 1},
                                {"label": "PCB不同", "value": 2},
                            ],
                            value=2,
                            id="scheme-type",
                            inline=True,
                        ),
                        dbc.Input(
                            type="number",
                            value=2,
                            min=2,
                            max=5,
                            step=1,
                            id="sm-modal-input",
                        ),
                    ]
                ),
                dbc.ModalFooter(
                    dbc.Button("提交", color="success", id="sm-modal-submit")
                ),
            ],
            id="sm-modal",
            centered=True,
        ),
        dbc.Modal(
            [
                dbc.ModalHeader("Notice:", style={"background-color": "yellow"}),
                dbc.ModalBody(id="limit-msg"),
                dbc.ModalFooter(
                    [
                        dbc.Row(
                            [
                                dbc.Col(
                                    dbc.Button(
                                        "返回修改",
                                        id="btn-return",
                                        color="success",
                                        style={"width": "200px"},
                                    )
                                ),
                                dbc.Col(
                                    dbc.Button(
                                        "仍然申请",
                                        id="btn-continue",
                                        color="danger",
                                        style={"width": "200px"},
                                    )
                                ),
                            ]
                        ),
                    ]
                ),
            ],
            id="limit-notice",
            centered=True,
        ),
    ]
)

layout = dbc.Container(
    [
        dbc.Tabs(
            [
                dbc.Tab(
                    tab1_content, label="创建项目", id="project-tab-1", tab_id="tab-1"
                ),
                dbc.Tab(
                    tab2_content, label="申请样制", id="project-tab-2", tab_id="tab-2"
                ),
            ],
            id="proj-tabs",
            active_tab="tab-2",
            style={"color": "#16a085"},
        ),
    ],
    fluid=True,
    # className="ml-3 pr-5",
)


# ? --------项目管理回调------------------
@callback(
    [
        Output("proj-dept-dropdown", "value"),
        Output("proj-dept-dropdown", "options"),
        Output("sm-dept-dropdown", "value"),
        Output("sm-dept-dropdown", "options"),
        Output("sm-proj-dropdown", "value"),
    ],
    [Input("proj-tabs", "active_tab")],
    [
        State("user", "data"),
        State("project-dropdown", "value"),
        State("proj-dept-dropdown", "value"),
    ],
    prevent_initial_call=False,
)
@db_session
def sm_dept_dropdown(id, user, proj, dept_id):
    """初始化时，设置部门默认值"""
    dept_id = dept_id if dept_id else user.get("dept_id")
    dept_group = Dept.get(id=dept_id).dept_group
    depts = Dept.select(lambda x: x.dept_group == dept_group)[:]
    options = [
        {"label": "_".join([i.dept_group, i.dept_name]), "value": i.id} for i in depts
    ]
    return dept_id, options, dept_id, options, proj


@callback(
    [
        Output("modal-proj", "is_open"),
    ],
    [
        Input("btn-add-new-proj", "n_clicks"),
    ],
    # group='proj-grp-1'
)
def add_new_proj(n):
    """点加号，弹出创建新项目窗口"""
    if n is None:
        raise PreventUpdate

    return True


@callback(
    [
        Output("project-dropdown", "options"),
        Output("project-dropdown", "value"),
        Output("modal-proj", "is_open"),
        Output("alert-modal-proj", "is_open"),
        Output("alert-modal-proj", "children"),
        Output("alert-modal-proj", "color"),
    ],
    [
        Input("btn-modal-foot-proj", "n_clicks"),
    ],
    [
        State("proj-new-model", "value"),
        State("project-dropdown", "options"),
        State("user", "data"),
    ],
    # group='proj-grp-1'
)
@db_session
def add_new_proj(n, value, options, user):
    """创建新项目"""
    if n is None:
        raise PreventUpdate

    if not value:
        return no_update, no_update, no_update, True, "请输入项目名称", "danger"

    p = Project.select(lambda x: x.project == value)[:]
    if p:
        return (
            no_update,
            no_update,
            no_update,
            True,
            f"该项目名已存在,请修改,如:{value}{datetime.now():%y%m%d}",
            "danger",
        )

    options = options or []
    options1 = [{"label": value, "value": value}] + options
    return options1, value, False, True, "项目创建成功", "success"


@callback(
    [
        Output("project-dropdown", "options"),
        Output("proj-ee", "options"),
        Output("proj-me", "options"),
        Output("proj-mag", "options"),
        Output("proj-layout", "options"),
        Output("proj-pcb-table", "columns"),
    ],
    [
        Input("proj-dept-dropdown", "value"),
    ],
    [
        State("user", "data"),
        State("proj-pcb-table", "columns"),
    ],
    # group='proj-grp-1'
)
@db_session
def dropdown_options(dept_id, user, columns):
    """切换部门时，更新下拉选项"""
    nt_name = user.get("nt_name")
    proj = Project.select(lambda x: (x.owner == nt_name) and (x.dept_id == dept_id))[:]
    options = [{"label": i.project, "value": i.project} for i in proj]
    dept_group = Dept.get(id=dept_id).dept_group
    d = Dept.select(lambda x: x.dept_group == dept_group)[:]
    ids = [i.id for i in d]
    u = User.select(lambda x: (x.dept_id in ids) and (x.termdate == None))[:]

    mag = User.select(lambda x: x.dept_id == 8)[:]
    na_options = [{"label": "NA", "value": "NA"}]

    mag = na_options + [
        {"label": i.nt_name.title(), "value": i.nt_name.title()} for i in mag
    ]

    ee = [
        {"label": i.nt_name.title(), "value": i.nt_name.title()}
        for i in u
        if i.role_group.upper() == "EE"
    ]

    me = na_options + [
        {"label": i.nt_name.title(), "value": i.nt_name.title()}
        for i in u
        if i.role_group.upper() == "ME"
    ]

    layout = na_options + [
        {"label": i.nt_name.title(), "value": i.nt_name.title()}
        for i in u
        if i.role_group.upper() == "LAYOUT"
    ]

    for i in columns:
        if i["field"] == "ee":
            i["editorParams"] = {"values": ee, "showListOnEmpty": True}
        elif i["field"] == "me":
            i["editorParams"] = {"values": me, "showListOnEmpty": True}
        elif i["field"] == "layout":
            i["editorParams"] = {"values": layout, "showListOnEmpty": True}

    return options, ee, me, mag, layout, columns


@callback(
    [
        Output("proj-mag", "value"),
        Output("proj-ee", "value"),
        Output("proj-me", "value"),
        Output("proj-layout", "value"),
        Output("proj-pcb-table", "data"),
    ],
    [
        Input("project-dropdown", "value"),
        Input("btn-modal-foot-proj", "n_clicks"),
    ],
    State("user", "data"),
    # group='proj-grp-2'
)
@db_session
def project_dropdown(value, n, user):
    """切换项目，刷新PCB表格"""
    if not value:
        raise PreventUpdate

    p = Project.select(project=value, owner=user.get("nt_name")).first()
    if p:
        return p.mag, p.ee, p.me, p.layout, p.board
    else:
        return None, None, None, None, [{}]


@callback(
    [
        Output("proj-pcb-table", "data"),
    ],
    [
        Input("pcb-add-row", "n_clicks"),
    ],
    [
        State("proj-pcb-table", "data"),
    ],
    # group='proj-grp-2'
)
@db_session
def pcb_table_add_row(n, data):
    """pcb table插入行"""
    data.append({})
    return data


@callback(
    [
        Output("project-alert", "children"),
        Output("project-alert", "color"),
        Output("project-alert", "is_open"),
    ],
    [Input("submit-project", "n_clicks")],
    [
        State("user", "data"),
        State("project-dropdown", "value"),
        State("proj-mag", "value"),
        State("proj-ee", "value"),
        State("proj-me", "value"),
        State("proj-layout", "value"),
        State("proj-pcb-table", "data"),
        State("proj-dept-dropdown", "value"),
    ],
)
@db_session
def submit_project(n, user, project, mag, ee, me, layout, pcb_data, dept_id):
    if not all([project, mag, ee, me, layout]):
        return "请完整填写项目成员和PCB信息", "danger", True

    boards = pd.DataFrame(pcb_data)
    boards = boards.reindex(columns=["pcb", "pcbpn", "ee", "me", "layout"])
    if (boards[["pcb", "pcbpn"]].fillna("") == "").any().any():
        return "PCB名称和料号需填写完整", "danger", True

    boards = boards.loc[boards["pcb"].notnull()]  # PCB信息填写格式
    boards["ee"] = boards["ee"].fillna(ee)
    boards["me"] = boards["me"].fillna(me)
    boards["layout"] = boards["layout"].fillna(layout)

    proj = Project.select(project=project, owner=user.get("nt_name")).first()
    if proj:
        proj.set(
            gmt_modified=datetime.now(),
            project=project,
            board=boards.to_dict(orient="records"),
            mag=mag,
            ee=ee,
            me=me,
            layout=layout,
            pm=user.get("nt_name"),
        )
        bg_access_record(user, "样制", "更新项目")
        return f"{project}项目更新成功", "success", True
    else:
        user_nt = user.get("nt_name")
        p = Project(
            gmt_create=datetime.now(),
            gmt_modified=datetime.now(),
            owner=user_nt,
            project=project,
            board=boards.to_dict(orient="records"),
            pm=user_nt,
            mag=mag,
            ee=ee,
            me=me,
            layout=layout,
            dept_id=dept_id,
        )
        bg_access_record(user, "样制", "创建项目")
        d = Dept.select(lambda x: x.category == "other")[:]
        if dept_id in (i.id for i in d):
            df = pd.DataFrame([p.to_dict()])
            df["board"] = df["board"].map(lambda x: [i["pcb"] for i in x])
            df = df.explode("board")
            df = df.drop(
                ["id", "gmt_create", "gmt_modified", "owner", "dept_id", "sm_forecast"],
                axis=1,
            )
            df.columns = df.columns.str.upper()
            body = f"Dear {user_nt}:<br>&nbsp;&nbsp;您创建了{project}项目,\
                请将此邮件转发给你部门经理核准。待核准完成后，您可以进入“样制申请”页面申请样制项目号。\
                    <br><br>{df.to_html()}"
            to = (
                f"{user_nt}@deltaww.com;<EMAIL>;<EMAIL>"
            )
            subject = f"【样制核准】{project}"
            bg_mail(to, subject, body)
        return f"{project}项目创建成功", "success", True


# ?--------------样制申请回调----------------


@callback(
    [Output("sm-proj-dropdown", "options")],
    [Input("sm-dept-dropdown", "value")],
    [State("user", "data")],
)
@db_session
def sm_stage_options(dept_id, user):
    """选择部门时，更新项目名称选项"""
    nt_name = user.get("nt_name")
    proj = Project.select(lambda x: (x.owner == nt_name) and (x.dept_id == dept_id))[:]
    options = [{"label": i.project, "value": i.project} for i in proj]
    return options


@callback(
    [
        Output("modal-sm", "is_open"),
    ],
    [
        Input("btn-add-new-stage", "n_clicks_timestamp"),
        Input("btn-modal-foot-sm", "n_clicks_timestamp"),
    ],
    [
        State("modal-sm", "is_open"),
        State("sm-proj-dropdown", "value"),
        State("sm-date", "date"),
        State("sm-qty", "value"),
    ],
)
def open_modal_sm(n1, n2, is_open, proj, date, qty):
    """创建新阶段弹窗控制"""
    if n1 > n2:
        if proj is None:
            raise PreventUpdate
        else:
            return not is_open
    if n2 > n1:
        if date is None or qty is None:
            raise PreventUpdate
        else:
            return not is_open


@callback(
    [
        Output("sm-date", "min_date_allowed"),
    ],
    [Input("btn-add-new-stage", "n_clicks")],
)
def sm_date_min_allowed(n):
    """需求日期为两周以后"""
    return datetime.now() + timedelta(weeks=2) + timedelta(days=1)


@callback(
    [
        Output("sm-stage-dropdown", "options"),
        Output("sm-stage-dropdown", "value"),
    ],
    [
        Input("btn-modal-foot-sm", "n_clicks"),
    ],
    [
        State("sm-stage-dropdown", "options"),
        State("sm-proj-dropdown", "value"),
        State("sm-date", "date"),
        State("sm-qty", "value"),
        State("sm-fsqty", "value"),
        State("user", "data"),
    ],
    # group='sm-grp-1'
)
@db_session
def add_new_stage(n, options, proj, date, qty, fsqty, user):
    if not all([date, qty, fsqty]):
        raise PreventUpdate
    proj = Project.select(project=proj, owner=user.get("nt_name")).first()
    date = pd.to_datetime(date).strftime("%Y/%m/%d")
    value = max(i["value"] for i in options) + 1 if options else 0
    new_stage = [
        {"样制数量": qty, "样制日期": date, "阶段名称": f"S{value}", "首样数量": fsqty}
    ]

    if proj.sm_forecast:
        proj.sm_forecast += new_stage
    else:
        proj.sm_forecast = new_stage
    options = (options or []) + [{"label": f"S{value}", "value": value}]
    return options, value


@callback(
    [
        Output("sm-stage-dropdown", "options"),
        Output("sm-stage-dropdown", "value"),
    ],
    [Input("sm-proj-dropdown", "value")],
    State("user", "data"),
)
@db_session
def sm_stage_options(proj, user):
    if not proj:
        raise PreventUpdate
    proj = Project.select(project=proj, owner=user.get("nt_name")).first()

    if proj.sm_forecast:
        options = [
            {"label": f"S{i}", "value": i} for i, _ in enumerate(proj.sm_forecast)
        ]
        max_value = max(i["value"] for i in options) if options else 0
        return options, max_value
    else:
        return [], None


@callback(
    [Output("sm-table", "data")],
    [
        Input("sm-stage-dropdown", "value"),
    ],
    [
        State("sm-proj-dropdown", "value"),
        State("user", "data"),
    ],
    # group='sm-grp-2'
)
@db_session
def sm_table_data(stage, proj, user):
    if (stage is None) or (proj is None):
        raise PreventUpdate
    proj = Project.select(project=proj, owner=user.get("nt_name")).first()
    if not proj.sm_forecast:
        return []

    df = pd.DataFrame(proj.board)
    prt_temp = Prt_temp.select(
        lambda x: (x.project_id == proj.id) and (x.stage == stage)
    )[:]
    prt = Prt.select(lambda x: (x.project_id == proj.id) and (x.stage == stage))[:]
    prt = prt + prt_temp
    prt = pd.DataFrame([i.to_dict() for i in prt])
    prt = prt.reindex(columns=["board"])
    prt = prt.drop_duplicates(["board"])
    df = df.merge(prt, left_on="pcb", right_on="board", how="left")

    df["project_id"] = proj.id
    df["pcbstatus"] = "需采购"
    sm_stage = proj.sm_forecast[stage]
    df["qty"] = sm_stage.get("样制数量", 0)
    df["fsqty"] = sm_stage.get("首样数量", 0)
    df["fsdate_req"] = sm_stage.get("样制日期", 0)
    df["id"] = df.index
    df["scheme"] = ""
    df["status"] = np.where(df["board"].notna(), "已申请", "未申请")
    return df.to_dict(orient="records")


@callback(
    [Output("sm-modal", "is_open")],
    [Input("sm-table", "cellEdited")],
    [State("sm-table", "data")],
    # group='sm-grp-2'
)
def open_multi_scheme(cell_edited, data):
    """打开多方案弹窗"""
    if cell_edited is None:
        raise PreventUpdate

    if cell_edited["value"] == "多方案":
        return True
    else:
        raise PreventUpdate


@callback(
    [Output("sm-table", "data"), Output("sm-modal", "is_open")],
    [Input("sm-modal-submit", "n_clicks")],
    [
        State("sm-table", "cellEdited"),
        State("sm-table", "data"),
        State("scheme-type", "value"),
        State("sm-modal-input", "value"),
    ],
    # group='sm-grp-2'
)
def submit_multi_scheme(n, update_data, data, scheme_type, scheme_qty):
    """提交多方案"""
    if n is None:
        raise PreventUpdate

    df = pd.DataFrame(data)
    idx = update_data["row"]["id"]
    scheme = [
        f"_PCB{i}" if scheme_type == 2 else f"_BOM{i}" for i in range(1, scheme_qty + 1)
    ]
    df.at[idx, "scheme"] = scheme
    df = df.explode("scheme")
    df = df.reset_index()
    df["id"] = df.index
    return df.to_dict(orient="records"), False


@callback(
    [
        Output("sm-alert", "is_open"),
        Output("sm-alert", "children"),
        Output("sm-alert", "color"),
        Output("limit-notice", "is_open"),
        Output("limit-msg", "children"),
    ],
    [
        Input("sm-submit-confirm", "confirmCounts"),
        Input("btn-return", "n_clicks"),
        Input("btn-continue", "n_clicks"),
    ],
    [
        State("sm-table", "data"),
        State("sm-proj-dropdown", "value"),
        State("sm-stage-dropdown", "value"),
        State("user", "data"),
    ],
)
@db_session
def sm_submit(n, n1, n2, data, proj, stage, user):
    if proj is None or stage is None:
        return True, "项目和阶段不能为空", "danger", no_update, no_update

    ctx = callback_context
    id = ctx.triggered[0]["prop_id"].split(".")[0]
    if id == "btn-return":
        return no_update, no_update, no_update, False, no_update

    nt_name = user.get("nt_name")
    df = pd.DataFrame(data)
    if "action" not in df.columns:
        return True, "请选择Action", "danger", no_update, no_update

    df = df.fillna("")
    c1 = df["action"].isin(["申请", "多方案"])
    c2 = df["status"] == "未申请"
    df = df.loc[c1 & c2]
    if df.empty:
        return True, "没有需要申请的项目", "danger", no_update, no_update

    columns_needed = [
        "action",
        "pcb",
        "pcbpn",
        "pcbstatus",
        "fsdate_req",
        "qty",
        "fsqty",
    ]
    if (df.reindex(columns=columns_needed).fillna("") == "").any().any():
        return True, "请填写完整申请信息", "danger", no_update, no_update

    p = Project.select(project=proj, owner=nt_name).first()
    d = Dept.get(id=p.dept_id)

    df["fsdate_req"] = pd.to_datetime(df["fsdate_req"], errors="coerce")

    if (df["fsdate_req"] < (datetime.now() + pd.DateOffset(weeks=2))).any():
        return True, "需求日期需大于当前日期2周,请修改", "danger", no_update, no_update

    df["stage"] = stage
    df["qty_limit"] = np.where(df["qty"].astype(int) > d.sm_qty_limit, 0, 1)
    prt = Prt.select(lambda x: x.project_id == p.id)[:]
    prt = pd.DataFrame(i.to_dict() for i in prt)
    prt = prt.reindex(columns=["project_id", "board", "pcbpn", "fsdate_req"])
    prt = prt.drop_duplicates(["project_id", "board", "pcbpn"], keep="last")
    prt = prt.rename(columns={"board": "pcb", "fsdate_req": "fsdate_req_old"})

    df = df.merge(prt, on=["project_id", "pcb", "pcbpn"], how="left")
    df["fsdate_req_old"] = pd.to_datetime(df["fsdate_req_old"], errors="coerce")
    c1 = (df["fsdate_req"] - pd.DateOffset(months=1)) < df["fsdate_req_old"]
    df["date_limit"] = np.where(c1, 0, 1)
    c1 = df["qty_limit"] == 0
    c2 = df["date_limit"] == 0
    df["approved"] = np.where(c1 | c2, 0, 1)

    qty_limit = df.loc[df["qty_limit"] == 0]
    date_limit = df.loc[df["date_limit"] == 0]
    msg1 = ""
    msg2 = ""
    if not qty_limit.empty:
        msg1 = (
            qty_limit["pcb"].str.cat(sep=",")
            + f"超过样制承接数量({d.sm_qty_limit}台),请修改样制数量"
        )

    if not date_limit.empty:
        msg2 = date_limit["pcb"].str.cat(sep=",") + "一个月内重复样制,请修改需求日期"

    if (msg1 or msg2) and (id == "submit-sm"):
        color = "warning"
        msg = html.Div(
            [
                html.H6(msg1),
                html.Br(),
                html.H6(msg2),
                html.Br(),
                html.H6(
                    "若不修改,请发邮件给部门经理,并抄送王波,待部门经理同意后才予受理"
                ),
            ]
        )
        return no_update, no_update, no_update, True, msg
    else:
        color = "success"
        msg = "样制申请已提交"

    for i, j in df.iterrows():
        Prt_temp(
            project_id=p.id,
            board=j["pcb"],
            stage=stage,
            proj=proj + "_" + j["pcb"] + j["scheme"],
            qty=j["qty"],
            dept="_".join([d.dept_group, d.dept_name]),
            pm=nt_name,
            ee=j["ee"],
            me=j["me"],
            mag=p.mag,
            layout=j["layout"],
            appdate=datetime.now(),
            fsdate_req=j["fsdate_req"],
            fsqty=j["fsqty"],
            pcbpn=j["pcbpn"],
            pcbstatus=j["pcbstatus"],
            scheme=j["scheme"],
            approved=j["approved"],
            dept_id=p.dept_id,
        )
    bg_access_record(user, "样制", "申请样制")
    mailto = f"<EMAIL>;{nt_name}@deltaww.com"
    df1 = df[
        [
            "pcb",
            "pcbpn",
            "pcbstatus",
            "fsdate_req",
            "qty",
            "fsqty",
            "ee",
            "me",
            "layout",
        ]
    ]
    df1.columns = df1.columns.str.upper()
    bg_mail(mailto, f"样制申请通知:{proj}", df1.to_html())
    return True, msg, color, False, no_update


@callback(
    [Output("sm-stage-dropdown", "value")],
    [Input("sm-alert", "children")],
    [State("sm-stage-dropdown", "value")],
    # group='sm-grp-1'
)
def refresh_sm_table(c, value):
    """提交后刷新sm_table"""
    if c == "样制申请已提交":
        return value
    else:
        raise PreventUpdate
