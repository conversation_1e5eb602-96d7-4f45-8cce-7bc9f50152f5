import dash_mantine_components as dmc
from dash_iconify import DashIconify
from common import id_factory
from config import UPLOAD_FOLDER_ROOT
from dash_extensions.enrich import (
    callback,
    Output,
    Input,
    MATCH,
    State,
    no_update,
    html,
)
from dash.exceptions import PreventUpdate
from common import parse_search, df_to_html, get_db
from tasks import bg_mail
import pandas as pd

id = id_factory(__name__)


def checkbox(_id):
    return dmc.CheckboxGroup(
        children=[
            dmc.Checkbox(label="MSL", value="MSL", size="xs", mb=10),
            dmc.Checkbox(label="ESD", value="ESD", size="xs", mb=10),
            dmc.Checkbox(label="WS", value="WS", size="xs", mb=10),
            dmc.Checkbox(label="HF", value="HF", size="xs", mb=10),
            dmc.Checkbox(label="LF", value="LF", size="xs", mb=10),
        ],
        id=id(_id),
        label="承认资料确认",
        orientation="horizontal",
        withAsterisk=True,
        offset="md",
        size="xs",
        mb=5,
    )


def get_attachment(title, folder):
    if not folder:
        return
    title = [dmc.Text(title, underline=True, size=13, weight=500)]
    div = [
        dmc.Anchor(
            dmc.Badge(
                f"{p.name}",
                leftSection=DashIconify(
                    icon="material-symbols:cloud-download", width=20
                ),
                color="orange",
            ),
            href=f"/upload/{folder}/{p.name}",
            target="_blank",
            variant="link",
        )
        for p in (UPLOAD_FOLDER_ROOT / folder).glob("*.*")
    ]

    return dmc.Group(title + div, align="end")


class CeRejectAIO(html.Div):
    class ids:
        reject = lambda aio_id: {  # noqa: E731
            "component": "CeRejectAIO",
            "subcomponent": "reject",
            "aio_id": aio_id,
        }
        modal = lambda aio_id: {  # noqa: E731
            "component": "CeRejectAIO",
            "subcomponent": "modal",
            "aio_id": aio_id,
        }
        input = lambda aio_id: {  # noqa: E731
            "component": "CeRejectAIO",
            "subcomponent": "input",
            "aio_id": aio_id,
        }
        submit = lambda aio_id: {  # noqa: E731
            "component": "CeRejectAIO",
            "subcomponent": "submit",
            "aio_id": aio_id,
        }
        close = lambda aio_id: {  # noqa: E731
            "component": "CeRejectAIO",
            "subcomponent": "close",
            "aio_id": aio_id,
        }
        alert = lambda aio_id: {  # noqa: E731
            "component": "CeRejectAIO",
            "subcomponent": "alert",
            "aio_id": aio_id,
        }

    # Make the ids class a public class
    ids = ids

    def __init__(self, aio_id=None):
        super().__init__(
            [
                dmc.Button(
                    "退件", color="red", id=self.ids.reject(aio_id), fullWidth=True
                ),
                dmc.Modal(
                    id=self.ids.modal(aio_id),
                    title="退件原因",
                    children=[
                        dmc.TextInput(id=self.ids.input(aio_id)),
                        dmc.Space(h=20),
                        dmc.Group(
                            [
                                dmc.Alert(id=self.ids.alert(aio_id), hide=True),
                                dmc.Button("提交", id=self.ids.submit(aio_id)),
                                dmc.Button(
                                    "关闭",
                                    color="red",
                                    variant="outline",
                                    id=self.ids.close(aio_id),
                                ),
                            ],
                            position="right",
                        ),
                    ],
                    withCloseButton=False,
                    centered=True,
                    closeOnClickOutside=False,
                    # opened=True,
                ),
            ]
        )

    @callback(
        Output(ids.modal(MATCH), "opened"),
        Input(ids.reject(MATCH), "n_clicks"),
    )
    def open_modal(n_clicks):
        if not n_clicks:
            raise PreventUpdate
        return True

    @callback(
        Output(ids.modal(MATCH), "opened"),
        Input(ids.close(MATCH), "n_clicks"),
    )
    def close_modal(n_clicks):
        if not n_clicks:
            raise PreventUpdate
        return False

    @callback(
        Output(ids.submit(MATCH), "disabled"),
        Output(ids.alert(MATCH), "hide"),
        Output(ids.alert(MATCH), "children"),
        Output(ids.alert(MATCH), "color"),
        Input(ids.submit(MATCH), "n_clicks"),
        State(ids.input(MATCH), "value"),
        State("url", "search"),
    )
    def modal_submit(n1, value, url):
        if not n1:
            raise PreventUpdate
        if not value:
            return no_update, False, "请输入原因", "red"
        url = parse_search(url)
        task_id = url.get("task")
        db = get_db()
        db.update(
            "ce.task",
            {"id": task_id, "ce_comment": f"退件原因:{value}", "status": "reject"},
        )

        task = db.find_one("ce.task", {"id": task_id})

        # *---------邮件通知开始-----------*
        to = [task.get("applicant"), task.get("ce")]
        to = ";".join(f"{i}@deltaww.com" for i in to)
        subject = f"【退件通知】{task.get('type')}"
        df = pd.DataFrame([task])
        columns = [
            "type",
            "dept",
            "applicant",
            "deltapn",
            "mfgname",
            "mfgpn",
            "ce",
            "ce_comment",
        ]
        df = df.reindex(columns=columns)
        content = df_to_html(
            df,
            "您的申请被退件，具体原因如下！请至SSP做进一步处理",
            href="http://sup.deltaww.com/info/rd?page=task",
        )
        bg_mail(to, subject, content)
        # *---------邮件通知结束-----------*

        return True, False, "提交成功", "green"


class CeCancelAIO(html.Div):
    class ids:
        cancel = lambda aio_id: {  # noqa: E731
            "component": "CeCancelAIO",
            "subcomponent": "cancel",
            "aio_id": aio_id,
        }
        modal = lambda aio_id: {  # noqa: E731
            "component": "CeCancelAIO",
            "subcomponent": "modal",
            "aio_id": aio_id,
        }
        input = lambda aio_id: {  # noqa: E731
            "component": "CeCancelAIO",
            "subcomponent": "input",
            "aio_id": aio_id,
        }
        submit = lambda aio_id: {  # noqa: E731
            "component": "CeCancelAIO",
            "subcomponent": "submit",
            "aio_id": aio_id,
        }
        close = lambda aio_id: {  # noqa: E731
            "component": "CeCancelAIO",
            "subcomponent": "close",
            "aio_id": aio_id,
        }
        alert = lambda aio_id: {  # noqa: E731
            "component": "CeCancelAIO",
            "subcomponent": "alert",
            "aio_id": aio_id,
        }

    # Make the ids class a public class
    ids = ids

    def __init__(self, aio_id=None, disabled=False):
        super().__init__(
            [
                dmc.Button(
                    "取消",
                    color="yellow",
                    id=self.ids.cancel(aio_id),
                    fullWidth=True,
                    disabled=disabled,
                ),
                dmc.Modal(
                    id=self.ids.modal(aio_id),
                    title="取消原因",
                    children=[
                        dmc.TextInput(id=self.ids.input(aio_id)),
                        dmc.Space(h=20),
                        dmc.Group(
                            [
                                dmc.Alert(id=self.ids.alert(aio_id), hide=True),
                                dmc.Button("提交", id=self.ids.submit(aio_id)),
                                dmc.Button(
                                    "关闭",
                                    color="red",
                                    variant="outline",
                                    id=self.ids.close(aio_id),
                                ),
                            ],
                            position="right",
                        ),
                    ],
                    withCloseButton=False,
                    centered=True,
                    closeOnClickOutside=False,
                    # opened=True,
                ),
            ]
        )

    @callback(
        Output(ids.modal(MATCH), "opened"),
        Input(ids.cancel(MATCH), "n_clicks"),
    )
    def open_modal(n_clicks):
        if not n_clicks:
            raise PreventUpdate
        return True

    @callback(
        Output(ids.modal(MATCH), "opened"),
        Input(ids.close(MATCH), "n_clicks"),
    )
    def close_modal(n_clicks):
        if not n_clicks:
            raise PreventUpdate
        return False

    @callback(
        Output(ids.submit(MATCH), "disabled"),
        Output(ids.alert(MATCH), "hide"),
        Output(ids.alert(MATCH), "children"),
        Output(ids.alert(MATCH), "color"),
        Input(ids.submit(MATCH), "n_clicks"),
        State(ids.input(MATCH), "value"),
        State("url", "search"),
    )
    def modal_submit(n1, value, url):
        if not n1:
            raise PreventUpdate
        if not value:
            return no_update, False, "请输入原因", "red"
        url = parse_search(url)
        task_id = url.get("task") or url.get("tid")
        db = get_db()
        sql = "update ce.task set \
            ce_comment=concat_ws(',',ce_comment,%s),status=%s where id=%s"
        db.execute(sql, (f"取消原因:{value}", "cancel", task_id))
        return True, False, "提交成功", "green"


def time_line(ce_remark: list[dict]):
    time_line_chidren = []
    for item in ce_remark:
        time_line_item_children = []
        if comment := item.get("comment"):
            time_line_item_children.append(
                dmc.Group(
                    [
                        dmc.Text(
                            "备注:",
                            color="orange",
                            size="xs",
                            underline=True,
                            weight=700,
                        ),
                        dmc.Text(comment, size="xs"),
                    ]
                )
            )
        if attachment := item.get("attachment"):
            list1 = [
                dmc.Text("附件:", color="green", size="xs", underline=True, weight=700)
            ]
            list1.extend(
                [
                    dmc.Anchor(
                        i.name,
                        href=f"/upload/{attachment}/{i.name}",
                        target="_blank",
                        variant="link",
                        size="xs",
                    )
                    for i in (UPLOAD_FOLDER_ROOT / attachment).glob("*.*")
                ]
            )
            time_line_item_children.append(dmc.Group(list1))

        if item.get("sample"):
            time_line_item_children.append(
                dmc.Text(
                    "厂商已经接到样品",
                    color="blue",
                    size="xs",
                    underline=True,
                    weight=700,
                )
            )

        if item.get("report"):
            time_line_item_children.append(
                dmc.Text(
                    "首次报告提供",
                    color="blue",
                    size="xs",
                    underline=True,
                    weight=700,
                )
            )
        if item.get("doc_review_date"):
            time_line_item_children.append(
                dmc.Text(
                    "文件评审已完成",
                    color="blue",
                    size="xs",
                    underline=True,
                    # weight=700,
                )
            )
        if item.get("case_closed_date"):
            time_line_item_children.append(
                dmc.Text(
                    "现场稽核已完成",
                    color="blue",
                    size="xs",
                    underline=True,
                    # weight=700,
                )
            )

        if time_line_item_children:
            time_line_chidren.append(
                dmc.TimelineItem(
                    time_line_item_children,
                    title=dmc.Group(
                        [
                            dmc.Text(item.get("time"), color="dimmed", size="xs"),
                            dmc.Text(item.get("user"), color="dimmed", size="xs"),
                        ]
                    ),
                )
            )

    if time_line_chidren:
        ce_remark_timeline = dmc.Timeline(
            time_line_chidren,
            active=len(time_line_chidren),
            bulletSize=10,
            lineWidth=2,
        )
    else:
        ce_remark_timeline = dmc.Timeline()
    return ce_remark_timeline
