# -*- coding: utf-8 -*-
from dataclasses import dataclass
from datetime import datetime, timedelta
from io import BytesIO

import dash_ag_grid as dag
import dash_dynamic_grid_layout as dgl
import dash_mantine_components as dmc
import feffery_antd_charts as fact
import feffery_antd_components as fac
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import plotly.io as pio
import polars as pl
import polars.selectors as cs
from chinese_calendar import get_holidays, get_workdays, is_workday
from dash import no_update, set_props
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    Input,
    Output,
    State,
    callback,
    dcc,
    html,
)
from dash_iconify import DashIconify
from feffery_dash_utils.style_utils import style
from xlsxwriter import Workbook

from common import id_factory, read_db, read_sql
from config import cfg
from tasks import bg_access_record, task_ai_sql, huey_ai
from utils import db

from ..utils import spec_report_dept, spec_report_owner

# 1. 定义清爽风格模板 (修正版)
clean_template_v2 = go.layout.Template()

clean_template_v2.layout = go.Layout(
    # --- 全局字体 ---
    font=dict(
        family="sans-serif",  # 无衬线字体
        size=12,
        color="#444444",  # 深灰色，比纯黑柔和
    ),
    # --- 背景色 ---
    paper_bgcolor="#E6F0FF",  # 图表区外背景色#E6F0FF#F0FFF0
    plot_bgcolor="#E6F0FF",  # 绘图区内背景色
    # --- 标题 ---
    title=dict(
        font=dict(size=18, color="#222222"),  # 图表主标题
        x=0.5,  # 居中
        xanchor="center",
    ),
    # --- 坐标轴通用设置 (修正 titlefont -> title.font) ---
    xaxis=dict(
        showgrid=True,  # 显示网格线
        gridcolor="#E0E0E0",  # 浅灰色网格线
        gridwidth=1,
        showline=True,  # 显示坐标轴线
        linecolor="#BBBBBB",  # 中灰色坐标轴线
        linewidth=1,
        zeroline=True,  # 显示零基线
        zerolinecolor="#CCCCCC",  # 稍浅的灰色零基线
        zerolinewidth=1,
        tickfont=dict(color="#555555"),  # 刻度文字颜色
        # --- 修正：轴标题字体在 title.font 中设置 ---
        title=dict(
            # text="X Axis Default Title", # 模板里通常不设具体文字
            font=dict(size=14, color="#333333")
        ),
    ),
    yaxis=dict(
        showgrid=True,  # 显示网格线
        gridcolor="#E0E0E0",  # 浅灰色网格线
        gridwidth=1,
        showline=False,  # Y轴通常不显示轴线，依靠网格线
        linecolor="#BBBBBB",
        linewidth=1,
        zeroline=True,  # 显示零基线
        zerolinecolor="#CCCCCC",
        zerolinewidth=1,
        tickfont=dict(color="#555555"),  # 刻度文字颜色
        # --- 修正：轴标题字体在 title.font 中设置 ---
        title=dict(
            # text="Y Axis Default Title", # 模板里通常不设具体文字
            font=dict(size=14, color="#333333")
        ),
    ),
    # --- 颜色序列 (数据系列的颜色顺序) ---
    # 这个 colorway 应用于 fig.add_trace 添加的轨迹（线条、标记等）
    colorway=[
        "#1f77b4",
        "#ff7f0e",
        "#2ca02c",
        "#d62728",
        "#9467bd",
        "#8c564b",
        "#e377c2",
        "#7f7f7f",
        "#bcbd22",
        "#17becf",
    ],
    # --- 图例 ---
    legend=dict(
        bgcolor="rgba(255,255,255,0.8)",  # 半透明白色背景
        bordercolor="#CCCCCC",
        borderwidth=0.5,
    ),
    # --- 边距 ---
    margin=dict(l=60, r=30, t=80, b=60),  # 左、右、上、下边距
    # --- 悬停模式 ---
    hovermode="closest",  # 悬停时显示最近的数据点信息
)

# 2. 注册新模板
pio.templates["clean_v2"] = clean_template_v2

id = id_factory(__name__)
approver = {
    "SH-SPEC": "Kuangyi.Gu",
    "SH-CE": "Ying.Gao",
    "SH-PUR": "Mona.Zhang",
    "SH-SMT": "Bo.Sm.Wang",
    "SH-SM": "Yingzhi.Zhang",
    "HZ-SM": "Weiming.Li",
    "WH-SM": "Hq.Cai",
    "SH-CNAS": "Lin.Xia",
}


@dataclass
class KpiDate:
    current_start: datetime
    current_end: datetime
    last_start: datetime
    last_end: datetime
    year_start: datetime
    year_end: datetime
    start: datetime
    end: datetime
    months: int


def kpi_card(
    title: str,
    current: float,
    last: float,
    annual: float,
    rate: float = 0,
    tip: str = "",
):
    if rate > 0:
        if current > rate:
            icon = "antd-rise"
            color = "#cf1322"
        else:
            icon = "antd-fall"
            color = "#3f8600"
        current = f"{current:.0%}"
        last = f"{rate:.0%}"
        annual = f"{annual:.0%}"
        refer = "目标"
    else:
        refer = "上月"
        if current > last:
            icon = "antd-rise"
            color = "#cf1322"
        else:
            icon = "antd-fall"
            color = "#3f8600"

    card = fac.AntdCard(
        [
            fac.AntdCardMeta(
                # title=fuc.FefferyCountUp(
                #     end=389,
                #     duration=1,
                #     # decimals=3,
                #     style={"font-size": "35px"},
                # ),
                title=fac.AntdStatistic(
                    id="statistic-demo",
                    value=current,
                    valueStyle={"color": color},
                    suffix=fac.AntdIcon(icon=icon),
                    # style=style(marginTop="15px"),
                ),
                style=style(marginTop="-15px"),
            ),
            fac.AntdCardMeta(
                description=fac.AntdRow(
                    [
                        fac.AntdCol(
                            [
                                fac.AntdText(
                                    refer,
                                    type="secondary",
                                    style={"font-size": "14px"},
                                ),
                                fac.AntdText(last, style={"font-size": "14px"}),
                            ],
                            span=6,
                            style={
                                "display": "flex",
                                "flex-direction": "column",
                            },
                        ),
                        fac.AntdCol(
                            [
                                fac.AntdText(
                                    "年均",
                                    type="secondary",
                                    style={"font-size": "14px"},
                                ),
                                fac.AntdText(annual, style={"font-size": "14px"}),
                            ],
                            span=6,
                            style={
                                "display": "flex",
                                "flex-direction": "column",
                            },
                        ),
                    ],
                    # gutter=[10, 20],
                ),
                style={"margin-top": "10px"},
            ),
        ],
        bodyStyle=style(flexDirection="column", paddingLeft="30px", minHeight="20px"),
        title=fac.AntdTooltip(title, title=dcc.Markdown(tip), placement="right"),
        id=id("kpi-card"),
    )
    return card


def kpi_card_pur(title: str, a: float, b: float, c: float, d: float):
    card = fac.AntdCard(
        [
            fac.AntdCardMeta(
                description=fac.AntdRow(
                    [
                        fac.AntdCol(
                            [
                                fac.AntdTooltip(
                                    fac.AntdText(
                                        "库存率",
                                        type="secondary",
                                        style={"font-size": "14px"},
                                    ),
                                    title=dcc.Markdown("""
                                        筛选条件：  
                                        1.当月部门样制BOM总材料种类数(分母)    
                                        2.当月部门样制BOM扣库状况Ok的材料种类数(分子)
                                        """),
                                ),
                                fac.AntdText(f"{a:.0%}", style={"font-size": "14px"}),
                            ],
                            span=3,
                            style={
                                "display": "flex",
                                "flex-direction": "column",
                            },
                        ),
                        fac.AntdCol(
                            [
                                fac.AntdTooltip(
                                    fac.AntdText(
                                        "新料率",
                                        type="secondary",
                                        style={"font-size": "14px"},
                                    ),
                                    title=dcc.Markdown("""
                                        筛选条件：  
                                        1.当月部门样制BOM总材料种类数(分母)  
                                        2.当月部门样制BOM扣库状况No Stock材料种类数(分子)
                                        """),
                                ),
                                fac.AntdText(f"{b:.0%}", style={"font-size": "14px"}),
                            ],
                            span=3,
                            style={
                                "display": "flex",
                                "flex-direction": "column",
                            },
                        ),
                        fac.AntdCol(
                            [
                                fac.AntdTooltip(
                                    fac.AntdText(
                                        "缺料率",
                                        type="secondary",
                                        style={"font-size": "14px"},
                                    ),
                                    title=dcc.Markdown("""
                                        参考缺料达成率
                                        """),
                                ),
                                fac.AntdText(f"{c:.0%}", style={"font-size": "14px"}),
                            ],
                            span=3,
                            style={
                                "display": "flex",
                                "flex-direction": "column",
                            },
                        ),
                        fac.AntdCol(
                            [
                                fac.AntdTooltip(
                                    fac.AntdText(
                                        "准时率",
                                        type="secondary",
                                        style={"font-size": "14px"},
                                    ),
                                    title=dcc.Markdown("""
                                        参考缺料准时率
                                        """),
                                ),
                                fac.AntdText(f"{d:.0%}", style={"font-size": "14px"}),
                            ],
                            span=3,
                            style={
                                "display": "flex",
                                "flex-direction": "column",
                            },
                        ),
                    ],
                    justify="space-between",
                    # gutter=[10, 20],
                ),
                style={"margin-top": "-15px"},
            ),
        ],
        bodyStyle=style(flexDirection="column", paddingLeft="30px", minHeight="20px"),
        title=title,
        id=id("kpi-card"),
    )
    return card


def get_kpi_date(month: str):
    now = datetime.now()
    date = pd.to_datetime(month)
    current_start = pd.offsets.MonthBegin(normalize=True).rollback(date)
    if (now.year == date.year) and (now.month == date.month):
        current_end = now + pd.offsets.DateOffset(days=1, normalize=True)
    else:
        current_end = date + pd.offsets.MonthBegin(normalize=True)

    last_start = current_start - pd.offsets.DateOffset(months=1)
    last_end = current_end - pd.offsets.DateOffset(months=1)
    year_start = pd.offsets.YearBegin(normalize=True).rollback(date)
    start = min(last_start, year_start)
    if now.year == date.year:
        year_end = now + pd.offsets.MonthBegin(normalize=True)
        end = now + pd.offsets.DateOffset(days=1, normalize=True)
    else:
        year_end = date + pd.offsets.YearBegin(normalize=True)
        end = year_end
    months = pd.date_range(start=year_start, end=year_end, freq="M").size
    return KpiDate(
        current_start=current_start,
        current_end=current_end,
        last_start=last_start,
        last_end=last_end,
        year_start=year_start,
        year_end=year_end,
        start=start,
        end=end,
        months=months,
    )


def quality_issue(kd: KpiDate, team: str):
    sql = "select accountable as spec,score,closed_date from ssp.issue \
        where closed_date between %s and %s and role_group=%s"
    dfz = read_db(sql, params=[kd.start, kd.end, team])

    quality0 = dfz.filter(
        pl.col("closed_date").is_between(kd.current_start, kd.current_end)
    ).height
    quality1 = dfz.filter(
        pl.col("closed_date").is_between(kd.last_start, kd.last_end)
    ).height
    quality2 = dfz.filter(
        pl.col("closed_date").is_between(kd.year_start, kd.year_end)
    ).height
    quality2 = int(quality2 / kd.months)
    return dfz, quality0, quality1, quality2


# @cache.memoize(expire=3600, tag="pur_data")
def pur_data(start: datetime, end: datetime):
    # TODO:排除掉重工项目
    sql = "select id as prt_id,prtno,pcbstatus,findate_act,appdate,\
        smd_count+dip_count as mat_count,dept,smtstadate,bom_date, \
        proj,ee,pm,qty,mat_ready_date,dip_mat_ready,dept_id \
        from ssp.prt a \
        left join (SELECT prt_id,max(finish_date) AS bom_date \
        FROM bom_record WHERE SOURCE=%s GROUP BY prt_id)b \
        on a.id=b.prt_id \
        where findate_act between %s and %s"
    prt = (
        read_db(sql, params=["bom", start, end])
        .with_columns(
            pl.col("pcbstatus")
            .str.to_datetime(strict=False)
            .dt.truncate("1s")
            .fill_null(pl.col("appdate"))
            .alias("pcb_date"),
            pl.col("smtstadate").dt.offset_by("3d").alias("smd_start_3d"),
        )
        .with_columns((pl.col("bom_date") <= pl.col("pcb_date")).alias("bom_achieved"))
    )
    sql = "select id as dept_id,category from ssp.dept"
    dept = read_db(sql)
    prt = prt.join(dept, on="dept_id", how="left")

    prt_id = prt["prt_id"].drop_nulls().unique().to_list()

    sql = "select id,pur,start_date,pur_date,application,es_maint_date,dept_id, \
        prt_id,req_date,mat_receiveddate,checkcode,is_new_material,price, \
        mat_catelogue as mat_type,dept,safetystock_id,mfgname as vendor,qty,r_qty \
        from ssp.pur \
        where pur_status!=%s \
        and start_date between %s and %s \
        or mat_receiveddate between %s and %s \
        or es_maint_date between %s and %s\
        or prt_id in %s"
    pur = read_db(
        sql,
        params=["cancel", start, end, start, end, start, end, prt_id],
        schema_overrides={
            "is_new_material": pl.String,
            "price": pl.Decimal,
        },
    )
    sql = "select count(*) as count from ssp.pur_plant \
        where mat_receiveddate between %s and %s"
    plant = read_db(sql, params=[start, end])
    plant = plant.get_column("count").sum()
    sql = "select latestowner as pur from ssp.safetystock \
        where addtype!=%s and (adddate between %s and %s \
        or canceldate between %s and %s)"
    ss = (
        read_db(sql, params=["auto", start, end, start, end])
        .with_columns(pl.col("pur").str.to_titlecase())
        .group_by("pur")
        .count()
        .rename({"count": "ss"})
    )

    holidays = get_holidays(datetime(start.year, 1, 1), datetime(start.year, 12, 31))
    now = datetime.now()
    c0 = pl.col("application") == "project"
    c1 = pl.col("mat_receiveddate") > pl.col("pcb_date")
    c2 = pl.col("mat_receiveddate") > pl.col("findate_act")
    c3 = pl.col("mat_receiveddate") > pl.col("smd_start_3d")
    pur = (
        pur.join(prt, on="prt_id", how="left")
        .join(dept, on="dept_id", how="left")
        .with_columns(
            pl.col("pur").str.to_titlecase(),
            pl.col("application").str.to_lowercase(),
            pl.col("price").fill_null(0),
            pl.when(pl.col("mat_type").str.to_lowercase().is_in(["active", "passive"]))
            .then(pl.lit("ee"))
            .otherwise(pl.lit("me"))
            .alias("mat_type"),
        )
        .with_columns((pl.col("price") * pl.col("r_qty")).round(0).alias("total_price"))
        .with_columns(
            pl.business_day_count(
                pl.col("start_date").dt.date(),
                pl.col("pur_date").fill_null(now).dt.date(),
                holidays=holidays,
            ).alias("process_work_days")
        )
        .with_columns(
            pl.when(pl.col("application") == "project")
            .then(pl.col("process_work_days") <= 1)
            .when(pl.col("application") == "debug")
            .then(pl.col("process_work_days") <= 3)
            .otherwise(False)
            .alias("process_achieved")
        )
        .with_columns(
            pl.when(pl.col("application") == "project")
            .then(pl.lit("a"))
            .when(
                pl.col("application").is_in(["debug", "stock"]) & (pl.col("price") == 0)
            )
            .then(pl.lit("b"))
            .when(
                pl.col("application").is_in(["debug", "stock"]) & (pl.col("price") > 0)
            )
            .then(pl.lit("c"))
            .alias("type_owner")
        )
        .with_columns(
            pl.business_day_count(
                pl.col("pur_date").fill_null(now).dt.date(),
                pl.col("es_maint_date").fill_null(now).dt.date(),
                holidays=holidays,
            ).alias("es_maint_work_days")
        )
        .with_columns((pl.col("es_maint_work_days") <= 3).alias("es_maint_achieved"))
        .with_columns((c0 & c1).fill_null(True).alias("received_gt_pcb"))
        .with_columns((c0 & c2).fill_null(True).alias("received_gt_findate"))
        .with_columns(
            pl.when(c0)
            .then(pl.when(c3).then(True).otherwise(False))
            .otherwise(None)
            .alias("received_gt_smd_start_3d")
        )
        .with_columns((pl.col("mat_receiveddate") <= pl.col("req_date")).alias("ata"))
    )

    return pur, prt, plant, ss


def spec_dashboard(kd: KpiDate):
    psl_doc = [
        "CBOM",
        "SBOM",
        "DFMS",
        "CM",
        "DFCS",
        "GRP CHANGE",
        "COST QUERY",
        "BOM COST",
        "ES",
        "TS",
        "SN",
        "RC",
        "OTHERS",
        "PN",
    ]
    ecn_doc = ["ECN", "SM"]
    bom_doc = ["ME PART", "55X", "TOOLA", "TOOLB", "CHANGE", "SMBOM"]
    ssp_doc = ["CBOM", "SBOM", "DFMS", "CM", "DFCS"]
    smbom_doc = ["CHANGE", "SMBOM"]
    holidays = get_holidays(
        datetime(kd.year_start.year, 1, 1),
        datetime(kd.year_start.year, 12, 31),
    )

    sql = "select doc_type,spec,release_date,work_time_minute,input_date,submit_date,\
        ecn_qty,psl_qty from ssp_spec.task \
        where status!=%s and release_date between %s and %s"
    dfx = read_db(sql, params=["canceled", kd.start - timedelta(days=365), kd.end])

    dfx = dfx.with_columns(
        pl.col("ecn_qty").fill_null(0),
        pl.col("psl_qty").fill_null(0),
        pl.col("doc_type").str.to_uppercase(),
        pl.col("spec").str.to_titlecase(),
        month_start=pl.col("release_date").dt.date().dt.month_start(),
        month_end=pl.when(
            pl.col("release_date").dt.date().dt.month() == datetime.now().month
        )
        .then(datetime.now().date())
        .otherwise(pl.col("release_date").dt.date().dt.month_end()),
    ).with_columns(
        month_work_days=pl.business_day_count(
            pl.col("month_start").dt.date(),
            pl.col("month_end").dt.date(),
            holidays=holidays,
        )
    )

    tip1 = """
    当月Release的规格文件数量  
    文件类型:非BOM/ECN  
    状态:Release  
    Release Date:当月
    """
    psl = dfx.filter(pl.col("doc_type").is_in(psl_doc))
    psl0 = psl.filter(
        pl.col("release_date").is_between(kd.current_start, kd.current_end)
    ).height
    psl1 = psl.filter(
        pl.col("release_date").is_between(kd.last_start, kd.last_end)
    ).height
    psl2 = psl.filter(
        pl.col("release_date").is_between(kd.year_start, kd.year_end)
    ).height
    psl2 = int(psl2 / kd.months)

    tip2 = """
    当月Release的变更单数量  
    文件类型:SM/ECN  
    状态:Release  
    Release Date:当月
    """
    ecn = dfx.filter(pl.col("doc_type").is_in(ecn_doc))
    ecn0 = ecn.filter(
        pl.col("release_date").is_between(kd.current_start, kd.current_end)
    ).height
    ecn1 = ecn.filter(
        pl.col("release_date").is_between(kd.last_start, kd.last_end)
    ).height
    ecn2 = ecn.filter(
        pl.col("release_date").is_between(kd.year_start, kd.year_end)
    ).height
    ecn2 = int(ecn2 / kd.months)

    tip3 = """
    当月Release的BOM数量  
    文件类型:TOOLA/B/SMBOM/CHANGE/55X/ME PART  
    状态:Release  
    Release Date:当月
    """
    bom = dfx.filter(pl.col("doc_type").is_in(bom_doc))
    bom0 = bom.filter(
        pl.col("release_date").is_between(kd.current_start, kd.current_end)
    ).height
    bom1 = bom.filter(
        pl.col("release_date").is_between(kd.last_start, kd.last_end)
    ).height
    bom2 = bom.filter(
        pl.col("release_date").is_between(kd.year_start, kd.year_end)
    ).height
    bom2 = int(bom2 / kd.months)

    # TODO:要精确到小时算,反查规格submit_ontime的计算方式是不是按照小时,暂时不做
    # *SSP达成率

    ssp = dfx.filter(pl.col("doc_type").is_in(ssp_doc))
    ssp = ssp.with_columns(
        ssp=pl.business_day_count(
            pl.col("input_date").dt.date(),
            pl.col("submit_date").dt.date(),
            holidays=holidays,
        )
        <= 2
    )
    ssp0 = ssp.filter(
        pl.col("release_date").is_between(kd.current_start, kd.current_end)
    )
    tip5 = """
    当月Release的SSP文件达成率(尚未启用)  
    文件类型:CBOM/SBOM/DFMS/CM/DFCS   
    Due Day=3天  
    来源:SSP线上需求  
    状态:Release  
    Release Date:当月  
    计算公式:on schedule数量/总数量
    """
    if ssp0.is_empty():
        ssp0_rate = 0
    else:
        ssp0_rate = ssp0.filter(pl.col("ssp")).height / ssp0.height
    ssp1 = ssp.filter(pl.col("release_date").is_between(kd.last_start, kd.last_end))
    if ssp1.is_empty():
        ssp1_rate = 0
    else:
        ssp1_rate = ssp1.filter(pl.col("ssp")).height / ssp1.height
    # TODO:submit_date有缺失
    ssp2 = ssp.filter(pl.col("release_date").is_between(kd.year_start, kd.year_end))
    if ssp2.is_empty():
        ssp2_rate = 0
    else:
        ssp2_rate = ssp2.filter(pl.col("ssp")).height / ssp2.height

    # *SMBOM按照1天(24小时)算，change按照0.25天（6小时）计算
    smbom = dfx.filter(pl.col("doc_type").is_in(smbom_doc))
    smbom = smbom.with_columns(
        achieve=pl.datetime_ranges(
            "input_date",
            "release_date",
            interval="1h",
        ).map_elements(
            lambda x: sum(1 for i in x if is_workday(i)), return_dtype=pl.Int8
        )
    ).with_columns(
        achieve=((pl.col("doc_type") == "CHANGE") & (pl.col("achieve") <= 6))
        | ((pl.col("doc_type") == "SMBOM") & (pl.col("achieve") <= 24)),
    )
    smbom0 = smbom.filter(
        pl.col("release_date").is_between(kd.current_start, kd.current_end)
    )
    tip6 = """
    当月Release的样制BOM达成率  
    文件类型1:SMBOM,Due Day:1天  
    文件类型2:CHANGE,Due Day:0.25天  
    状态:Release  
    Release Date:当月  
    计算公式:on schedule数量/总数量
    """
    if smbom0.is_empty():
        smbom0_rate = 0
    else:
        smbom0_rate = smbom0.filter(pl.col("achieve")).height / smbom0.height
    smbom1 = smbom.filter(pl.col("release_date").is_between(kd.last_start, kd.last_end))
    if smbom1.is_empty():
        smbom1_rate = 0
    else:
        smbom1_rate = smbom1.filter(pl.col("achieve")).height / smbom1.height
    smbom2 = smbom.filter(pl.col("release_date").is_between(kd.year_start, kd.year_end))
    if smbom2.is_empty():
        smbom2_rate = 0
    else:
        smbom2_rate = smbom2.filter(pl.col("achieve")).height / smbom2.height

    sql = "select spec,month,qty_on_schedule,qty_total from ssp_spec.index \
        where month between %s and %s"
    dfy = read_db(
        sql,
        params=[kd.start, kd.end],
        schema_overrides={
            "spec": pl.String,
            "month": pl.Date,
            "qty_on_schedule": pl.Int64,
            "qty_total": pl.Int64,
        },
    )
    dfy = dfy.with_columns(pl.col("spec").str.strip_chars().str.to_titlecase())

    ecn0_rate = (
        dfy.filter(pl.col("month") == kd.last_start)
        .group_by("month")
        .agg(rate=pl.col("qty_on_schedule").sum() / pl.col("qty_total").sum())["rate"]
        .first()
        or 0
    )

    ecn1_rate = (
        dfy.filter(pl.col("month") == kd.last_start)
        .group_by("month")
        .agg(rate=pl.col("qty_on_schedule").sum() / pl.col("qty_total").sum())["rate"]
        .first()
        or 0
    )
    ecn2_rate = dfy["qty_on_schedule"].sum() / (dfy["qty_total"].sum() or 1)

    # *-----品质------
    tip4 = """
    当月品质问题数量  
    状态:close  
    责任单位：SPEC  
    Release Date:当月
    """
    dfi, q0, q1, q2 = quality_issue(kd, "SPEC")

    df10 = dfy.group_by("spec").agg(
        x1=pl.col("qty_on_schedule").sum(),
        y1=pl.col("qty_total").sum(),
    )

    df11 = dfx.group_by("spec").agg(
        (pl.col("ecn_qty").sum() / (105 * kd.months)) * 0.3 * 80,
        (pl.col("psl_qty").sum() / (80 * kd.months)) * 0.15 * 80,
    )

    df12 = smbom.group_by("spec").agg(x2=pl.col("achieve").sum(), y2=pl.count())

    df13 = (
        dfi.with_columns(pl.col("spec").fill_null("").str.to_titlecase().str.split(","))
        .explode("spec")
        .group_by("spec")
        .agg(pl.col("score").sum())
    )

    sql = "select nt_name from ssp.user where role_group=%s"
    users = read_db(sql, params=["SPEC"])
    users = users.with_columns(pl.col("nt_name").str.strip_chars().str.to_titlecase())
    df1 = (
        df10.join(df11, on="spec", how="full", coalesce=True)
        .join(df12, on="spec", how="full", coalesce=True)
        .join(df13, on="spec", how="full", coalesce=True)
        .fill_null(0)
        .filter(pl.col("spec").is_in(users["nt_name"]))
        .with_columns(lt=(pl.col("x1") + pl.col("x2")) / (pl.col("y1") + pl.col("y2")))
        .with_columns((pl.col("lt") / 0.85) * 0.35 * 80)
        .with_columns(score=(100 - pl.col("score")) * 0.2)
        .with_columns(
            value=pl.sum_horizontal(["ecn_qty", "psl_qty", "lt", "score"]).round(0)
        )
        .sort("value", descending=True)
    )

    # *-----工作量------
    df2 = (
        dfx.filter(pl.col("spec") != "Kuangyi.Gu")
        .with_columns(
            month=pl.col("release_date").dt.month(),
            year=pl.col("release_date").dt.year(),
        )
        .group_by(["year", "month"])
        .agg(
            pl.col("work_time_minute").sum(),
            pl.col("spec").n_unique(),
            pl.col("month_work_days").first(),
        )
        .with_columns(
            loading=(
                pl.col("work_time_minute")
                / (pl.col("spec") * pl.col("month_work_days") * 8 * 60)
            ).round(2)
        )
        .sort(["month"])
        .with_columns(
            month=pl.col("month").cast(pl.String),
            year=pl.col("year").cast(pl.String),
        )
    )

    # *:留边距
    chart1 = fact.AntdBar(
        data=df1.to_dicts(),
        xField="value",
        yField="spec",
        color="#f0884d",
        yAxis={"grid": None},
        xAxis={"grid": None},
        style=style(backgroundColor="white", height="100%", width="100%", padding=10),
        label={"position": "left"},
        key=f"{datetime.now()}",
        # groupField="spec",
        # isPercent=True,
        # padding=10,
        # meta={"日期": {"type": "cat"}},
        # label={"position": "top"},
        # yAxis={"max": 100000},
    )

    # *:留边距
    chart2 = fact.AntdLine(
        # height="100%",
        data=df2.to_dicts(),
        xField="month",
        yField="loading",
        seriesField="year",
        # legend=False,
        color=["#87CEFA", "#F58E8E", "#22C55E"],
        # color=,
        yAxis={
            "grid": None,
            "label": {"formatter": {"func": "(e) => `${(e * 100).toFixed(0)}%`"}},
        },
        xAxis={"grid": None},
        label={
            "position": "center",
            "formatter": {"func": "({ loading }) => `${(loading * 100).toFixed(0)}%`"},
        },
        point={"shape": "circle"},
        style=style(backgroundColor="white", height="100%", padding=10),
        key=f"{datetime.now()}",
    )
    tip7 = """
上月Release的变更单达成率  
文件类型:SM/ECN,Due Day:1天  
状态:Release  
Release Date:上月  
计算公式:on schedule数量/总数量(管理-时效数据上传数据)
    """
    grid = dgl.DashGridLayout(
        id="grid-layout",
        items=[
            dgl.DraggableWrapper(
                children=[kpi_card("PSL文件数量", psl0, psl1, psl2, 0, tip1)], id="1"
            ),
            dgl.DraggableWrapper(
                children=[kpi_card("ECN数量", ecn0, ecn1, ecn2, 0, tip2)],
                id="2",
            ),
            dgl.DraggableWrapper(
                children=[kpi_card("BOM数量", bom0, bom1, bom2, 0, tip3)],
                id="3",
            ),
            dgl.DraggableWrapper(
                children=[kpi_card("品质", q0, q1, q2, 0, tip4)],
                id="4",
            ),
            dgl.DraggableWrapper(
                children=[
                    kpi_card("SSP达成率", ssp0_rate, ssp1_rate, ssp2_rate, 0.9, tip5)
                ],
                id="5",
            ),
            dgl.DraggableWrapper(
                children=[
                    kpi_card(
                        "BOM达成率", smbom0_rate, smbom1_rate, smbom2_rate, 0.75, tip6
                    )
                ],
                id="6",
            ),
            dgl.DraggableWrapper(
                children=[
                    kpi_card("ECN达成率", ecn0_rate, ecn1_rate, ecn2_rate, 0.9, tip7)
                ],
                id="7",
            ),
            dgl.DraggableWrapper(
                children=[chart1],
                id="8",
            ),
            dgl.DraggableWrapper(
                children=[chart2],
                id="9",
            ),
        ],
        showRemoveButton=False,
        showResizeHandles=False,
        rowHeight=125,
        cols={"lg": 12, "md": 12, "sm": 6, "xs": 4, "xxs": 2},
        compactType=None,
        itemLayout=[
            {"i": "1", "x": 0, "y": 0, "w": 3, "h": 1},
            {"i": "2", "x": 3, "y": 0, "w": 3, "h": 1},
            {"i": "3", "x": 6, "y": 0, "w": 3, "h": 1},
            {"i": "4", "x": 9, "y": 0, "w": 3, "h": 1},
            {"i": "5", "x": 0, "y": 1, "w": 3, "h": 1},
            {"i": "6", "x": 3, "y": 1, "w": 3, "h": 1},
            {"i": "7", "x": 6, "y": 1, "w": 3, "h": 1},
            {"i": "8", "x": 9, "y": 1, "w": 3, "h": 3},
            {"i": "9", "x": 0, "y": 1, "w": 9, "h": 2},
        ],
    )
    return grid


# @cache.memoize(ttl=5000, typed=True)
# def pur_modify(start, end):
#     sql = "select create_date,pur,id,pur_status as status from pur_modify \
#         where create_date between %s and %s and type=%s"
#     dfx = read_db(sql, params=[start, end, "after"])
#     return dfx


# @timer
def pur_dashboard(kd: KpiDate):
    df, prt, plant, ss = pur_data(kd.start, kd.end)

    df = df.filter(pl.col("pur") != "Bo.Sm.Wang")

    dfc = df.filter(pl.col("start_date").is_between(kd.current_start, kd.current_end))
    dfl = df.filter(pl.col("start_date").is_between(kd.last_start, kd.last_end))
    dfy = df.filter(pl.col("start_date").is_between(kd.year_start, kd.year_end))

    prtc = prt.filter(
        pl.col("findate_act").is_between(kd.current_start, kd.current_end)
    )
    prtl = prt.filter(pl.col("findate_act").is_between(kd.last_start, kd.last_end))
    prty = prt.filter(pl.col("findate_act").is_between(kd.year_start, kd.year_end))

    # 申请笔数
    c1 = dfc.height
    l1 = dfl.height
    y1 = dfy.height
    y1 = int(y1 / kd.months)
    tip1 = """
        当月采购申请笔数  
        筛选条件：  
        1.材料类别--All  
        2.职责人--非王波  
        3.采购申请日--当月
        """

    # 到货笔数
    c2 = df.filter(
        pl.col("mat_receiveddate").is_between(kd.current_start, kd.current_end)
    ).height
    l2 = df.filter(
        pl.col("mat_receiveddate").is_between(kd.last_start, kd.last_end)
    ).height
    y2 = df.filter(
        pl.col("mat_receiveddate").is_between(kd.year_start, kd.year_end)
    ).height
    y2 = int(y2 / kd.months)
    tip2 = """
        当月采购到料笔数  
        筛选条件：  
        1.材料类别--All  
        2.职责人--非王波  
        3.材料到货日--当月
        """

    # 缺料未到数
    cond1 = pl.col("received_gt_pcb")
    cond2 = pl.col("findate_act").is_between(kd.current_start, kd.current_end)
    c3 = df.filter(cond1 & cond2).height

    cond2 = pl.col("findate_act").is_between(kd.last_start, kd.last_end)
    l3 = dfl.filter(cond1 & cond2).height

    cond2 = pl.col("findate_act").is_between(kd.year_start, kd.year_end)
    y3 = dfy.filter(cond1 & cond2).height
    y3 = int(y3 / kd.months)
    tip3 = """
        当月影响样制的缺料笔数  
        筛选条件：  
        1.材料类别--All  
        2.职责人--非王波  
        3.部门-SDC.HDC  
        4.当月样制结束的项目  
        5.项目中晚于PCB到板日（包含空白）的缺料笔数  
        """

    # *-----品质------
    dfi, q0, q1, q2 = quality_issue(kd, "PUR")
    tip4 = """
        当月品质问题数量  
        状态：close  
        责任单位：SPEC  
        Release Date：当月
            """

    # 处理时效性
    x1 = pl.col("application") != "stock"
    if c1 > 0:
        c5 = dfc.filter(pl.col("process_achieved")).height / dfc.filter(x1).height
    else:
        c5 = 0
    if l1 > 0:
        l5 = dfl.filter(pl.col("process_achieved")).height / dfl.filter(x1).height
    else:
        l5 = 0
    tip5 = """
        当月下单处理延误笔数  
        筛选条件：  
        1.材料类别--All  
        2.职责人--非王波  
        3.当月下单数  
        4.采购申请日-材料发起（project<1天笔数,debugt<3天笔数）  
        5.状态是Cancel，采购处理日为空，不纳入计算  
        """

    # 交期维护时效性
    if c1 > 0:
        c6 = dfc.filter(pl.col("es_maint_achieved")).height / c1
    else:
        c6 = 0
    if l1 > 0:
        l6 = dfl.filter(pl.col("es_maint_achieved")).height / l1
    else:
        l6 = 0
    tip6 = """
        当月confirm_etd 不及时笔数  
        筛选条件：  
        1.材料类别--All  
        2.职责人--非王波  
        3.当月confirm_etd 维护时间与采购申请日晚于3个工作日
        """

    # 交期达成率
    x1 = pl.col("application") == "debug"
    x2 = pl.col("mat_receiveddate").is_between(kd.current_start, kd.current_end)
    x3 = pl.col("mat_receiveddate") <= pl.col("req_date")
    c7 = df.filter(x1 & x2 & x3).height / df.filter(x1 & x2).height
    x2 = pl.col("mat_receiveddate").is_between(kd.year_start, kd.year_end)
    l7 = df.filter(x1 & x2 & x3).height / df.filter(x1 & x2).height
    tip7 = """
        当月材料类别:debug 满足交期比率  
        筛选条件：  
        1.材料类别--All  
        2.职责人--非王波  
        3.当月debug到货笔数  
        4.到货日-需求时间>1的笔数
        """

    # 缺料达成率by项目(PCB到板日)# TODO 核对数据62.7，外部192项目，内部186，内部缺料项目69
    x1 = pl.col("category").is_in(["SDC", "HDC"])
    prtc1 = prtc.filter(x1)
    prtl1 = prtl.filter(x1)
    # prtc1.write_excel("prtc1.xlsx")
    # df.write_excel("df.xlsx")
    cond1 = pl.col("received_gt_pcb")
    cond2 = pl.col("prt_id").is_in(prtc1["prt_id"])
    cond3 = pl.col("prt_id").is_in(prtl1["prt_id"])
    if prtc1.height > 0:
        c8 = (
            prtc1.height - df.filter(cond1 & cond2)["prt_id"].unique().len()
        ) / prtc1.height
    else:
        c8 = 0
    if prtl1.height > 0:
        l8 = (
            prtl1.height - df.filter(cond1 & cond3)["prt_id"].unique().len()
        ) / prtl1.height
    else:
        l8 = 0
    tip8 = """
        当月样制缺料达成率  
        筛选条件：  
        1.部门-SDC.HDC  
        2.当月样制项目结束总项目数（分母）  
        3.无缺料(到货日不为空且到货日<pcb到货日)项目数(分子)
        """

    # 项目缺料率by项目(样制完成日)
    cond1 = pl.col("received_gt_smd_start_3d")
    cond2 = pl.col("prt_id").is_in(prtc1["prt_id"])
    cond3 = pl.col("prt_id").is_in(prtl1["prt_id"])
    if prtc1.height > 0:
        c9 = (
            prtc1.height - df.filter(cond1 & cond2)["prt_id"].unique().len()
        ) / prtc1.height
    else:
        c9 = 0
    if prtl1.height > 0:
        l9 = (
            prtl1.height - df.filter(cond1 & cond3)["prt_id"].unique().len()
        ) / prtl1.height
    else:
        l9 = 0
    tip9 = """
        当月样制缺料达成率  
        筛选条件：  
        1.部门-SDC.HDC  
        2.当月样制项目结束总项目数（分母）  
        3.无缺料(到货日不为空且到货日<SMT开始日后3天)项目数(分子)
        """

    # 缺料准时率by料号(样制完成日)
    cond1 = pl.col("received_gt_smd_start_3d")
    cond2 = pl.col("prt_id").is_in(prtc1["prt_id"])
    cond3 = pl.col("prt_id").is_in(prtl1["prt_id"])
    dfc10 = df.filter(cond2)
    dfl10 = df.filter(cond3)
    if dfc10.height > 0:
        c10 = (dfc10.height - dfc10.filter(cond1).height) / dfc10.height
    else:
        c10 = 0
    if dfl10.height > 0:
        l10 = (dfl10.height - dfl10.filter(cond1).height) / dfl10.height
    else:
        l10 = 0
    tip10 = """
        当月样制缺料达成率  
        筛选条件：  
        1.部门-SDC.HDC  
        2.当月样制结束项目的缺料总笔数（分母）  
        3.当月样制结束项目的无缺料数(到货日不为空且到货日<SMT开始日后3天)(分子)
        """

    # ----------DCBU------------
    # 库存达成率#TODO：确认数值
    prtc_dcbu = prtc.filter(pl.col("dept") == "DCBU_DCBU")
    cond0 = pl.col("prt_id").is_in(prtc_dcbu["prt_id"].unique())
    project_count = (
        df.filter(cond0)
        .filter(pl.col("application") == "project")
        .get_column("checkcode")
        .unique()
        .len()
    )
    denominator = prtc_dcbu["mat_count"].sum()
    c11a = (denominator - project_count) / denominator if denominator > 0 else 0

    # 新料比率#TODO：确认数值
    cond0 = pl.col("prt_id").is_in(prtc_dcbu["prt_id"].unique())
    cond1 = pl.col("is_new_material") == "Y"
    cond2 = pl.col("application") == "project"
    c11b = (
        (
            df.filter(cond0 & cond1 & cond2).get_column("checkcode").unique().len()
            / denominator
        )
        if denominator > 0
        else 0
    )

    # 缺料达成率by项目(PCB到板日)
    cond1 = pl.col("received_gt_pcb")
    c11c = (
        (
            prtc_dcbu.height
            - df.filter(cond0 & cond1).get_column("prt_id").unique().len()
        )
        / prtc_dcbu.height
        if prtc_dcbu.height > 0
        else 0
    )

    # 缺料达成率by料号(样制完成日)
    cond1 = pl.col("received_gt_findate")
    c11d_numerator = df.filter(cond0).height - df.filter(cond0 & cond1).height
    c11d_denominator = df.filter(cond0).height
    if c11d_denominator > 0:
        c11d = c11d_numerator / c11d_denominator
    else:
        c11d = 0

    # ------------CDBU-----------
    # 库存达成率
    prtc_cdbu = prtc.filter(pl.col("dept") == "DES_CDBU")
    cond0 = pl.col("prt_id").is_in(prtc_cdbu["prt_id"].unique())
    project_count = (
        df.filter(cond0)
        .filter(pl.col("application") == "project")
        .get_column("checkcode")
        .unique()
        .len()
    )

    denominator = prtc_cdbu["mat_count"].sum()
    c12a = (denominator - project_count) / denominator if denominator > 0 else 0

    # CDBU新料比率
    cond1 = pl.col("is_new_material") == "Y"
    cond2 = pl.col("application") == "project"
    c12b = (
        (
            df.filter(cond0 & cond1 & cond2).get_column("checkcode").unique().len()
            / denominator
        )
        if denominator > 0
        else 0
    )

    # CDBU缺料达成率by项目(PCB到板日)
    cond1 = pl.col("received_gt_pcb")
    c12c = (
        (prtc_dcbu.height - df.filter(cond0 & cond1)["prt_id"].unique().len())
        / prtc_dcbu.height
        if prtc_dcbu.height > 0
        else 0
    )

    # CDBU缺料达成率by料号(样制完成日)
    cond1 = pl.col("received_gt_findate")
    c12d = (
        (df.filter(cond0).height - df.filter(cond0 & cond1).height)
        / df.filter(cond0).height
        if df.filter(cond0).height > 0
        else 0
    )

    # -------工作量排名-------
    dfmy = df.filter(
        pl.col("mat_receiveddate").is_between(kd.year_start, kd.year_end)
    ).with_columns(
        pl.when(pl.col("application") == "project")
        .then(15)
        .when(pl.col("application").is_in(["stock", "debug"]) & (pl.col("price") == 0))
        .then(13)
        .when(pl.col("application").is_in(["stock", "debug"]) & (pl.col("price") > 0))
        .then(19)
        .otherwise(0)
        .alias("loading")
    )

    wm = len(get_workdays(kd.year_start, kd.year_end)) * 8 * 60
    dfx = (
        dfmy.group_by("pur")
        .agg(pl.col("loading").sum())
        .with_columns(
            pl.when(pl.col("pur") == "Mona.Zhang")
            .then(pl.col("loading") + prty.height * 20)
            .when(pl.col("pur") == "Mona.Zhang")
            .then(pl.col("loading") + plant * 0.5)
            .otherwise(pl.col("loading"))
        )
        .join(ss, on="pur", how="left")
        .fill_null(0)
        .with_columns(total=((pl.col("loading") + pl.col("ss")) * 100 / wm).round(1))
        .sort("total", descending=True)
    )
    chart1 = fact.AntdBar(
        data=dfx.to_dicts(),
        xField="total",
        yField="pur",
        color="#f0884d",
        yAxis={"grid": None},
        xAxis={"grid": None},
        style=style(backgroundColor="white", height="100%", width="100%", padding=10),
        label={"position": "left"},
        key=f"{datetime.now()}",
    )
    grid = dgl.DashGridLayout(
        id="grid-layout",
        items=[
            dgl.DraggableWrapper(
                children=[kpi_card("申请笔数", c1, l1, y1, 0, tip1)], id="1"
            ),
            dgl.DraggableWrapper(
                children=[kpi_card("到货笔数", c2, l2, y2, 0, tip2)],
                id="2",
            ),
            dgl.DraggableWrapper(
                children=[kpi_card("缺料未到数", c3, l3, y3, 0, tip3)],
                id="3",
            ),
            dgl.DraggableWrapper(
                children=[kpi_card("品质", q0, q1, q2, 0, tip4)],
                id="4",
            ),
            dgl.DraggableWrapper(
                children=[kpi_card("处理时效性", c5, l5, l5, 0.9, tip5)],
                id="5",
            ),
            dgl.DraggableWrapper(
                children=[kpi_card("交期维护时效性", c6, l6, l6, 0.95, tip6)],
                id="6",
            ),
            dgl.DraggableWrapper(
                children=[kpi_card("交期达成率", c7, l7, l7, 0.8, tip7)],
                id="7",
            ),
            dgl.DraggableWrapper(
                children=[kpi_card("缺料达成率", c8, l8, l8, 0.85, tip8)],
                id="8",
            ),
            dgl.DraggableWrapper(
                children=[kpi_card("项目缺料率", c9, l9, l9, 0.85, tip9)],
                id="9",
            ),
            dgl.DraggableWrapper(
                children=[kpi_card("缺料准时率", c10, l10, l10, 0.9, tip10)],
                id="10",
            ),
            dgl.DraggableWrapper(
                children=[kpi_card_pur("DCBU", c11a, c11b, c11c, c11d)],
                id="11",
            ),
            dgl.DraggableWrapper(
                children=[kpi_card_pur("CDBU", c12a, c12b, c12c, c12d)],
                id="12",
            ),
            # dgl.DraggableWrapper(
            #     children=[kpi_card("交期达成率", 0, 0, 0, 0.9)],
            #     id="13",
            # ),
            dgl.DraggableWrapper(
                children=[chart1],
                id="14",
            ),
        ],
        showRemoveButton=False,
        showResizeHandles=False,
        rowHeight=125,
        cols={"lg": 12, "md": 12, "sm": 6, "xs": 4, "xxs": 2},
        compactType=None,
        itemLayout=[
            {"i": "1", "x": 0, "y": 0, "w": 3, "h": 1},
            {"i": "2", "x": 3, "y": 0, "w": 3, "h": 1},
            {"i": "3", "x": 6, "y": 0, "w": 3, "h": 1},
            {"i": "4", "x": 9, "y": 0, "w": 3, "h": 1},
            {"i": "5", "x": 0, "y": 1, "w": 3, "h": 1},
            {"i": "6", "x": 3, "y": 1, "w": 3, "h": 1},
            {"i": "7", "x": 6, "y": 1, "w": 3, "h": 1},
            {"i": "8", "x": 0, "y": 2, "w": 3, "h": 1},
            {"i": "9", "x": 3, "y": 2, "w": 3, "h": 1},
            {"i": "10", "x": 6, "y": 2, "w": 3, "h": 1},
            {"i": "11", "x": 0, "y": 3, "w": 3, "h": 1},
            {"i": "12", "x": 3, "y": 3, "w": 3, "h": 1},
            {"i": "13", "x": 6, "y": 3, "w": 3, "h": 1},
            {"i": "14", "x": 9, "y": 1, "w": 3, "h": 3},
        ],
    )
    return grid


def spec_report_by_owner(start_date, end_date):
    df = spec_report_owner(start_date, end_date)
    if df.empty:
        return fac.AntdEmpty(image="simple")

    total = df.loc[df["SPEC"] == "All"]
    df = df.loc[df["SPEC"] != "All"]
    total = total.to_dict("records")

    table = dag.AgGrid(
        id=id("report-table"),
        className="ag-theme-quartz",
        columnDefs=cols_owner,
        rowData=df.to_dict("records"),
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        # columnSize="responsiveSizeToFit",
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "pinnedBottomRowData": total,
        },
        style={"height": "75vh"},
        getRowStyle={
            "styleConditions": [
                {
                    "condition": "params.data.SPEC == 'All'",
                    "style": {"backgroundColor": "sandybrown"},
                },
            ],
        },
    )
    return table


def spec_report_by_dept(start_date, end_date):
    df = spec_report_dept(start_date, end_date)
    if df.empty:
        return fac.AntdEmpty()

    total = df.loc[df["DEPT"] == "All"]
    df = df.loc[df["DEPT"] != "All"]
    total = total.to_dict("records")

    table = dag.AgGrid(
        id=id("report-table"),
        className="ag-theme-quartz",
        columnDefs=cols_dept,
        rowData=df.to_dict("records"),
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        columnSize="autoSize",
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "pinnedBottomRowData": total,
        },
        style={"height": "75vh"},
        getRowStyle={
            "styleConditions": [
                {
                    "condition": "params.data.DEPT == 'All'",
                    "style": {"backgroundColor": "sandybrown"},
                },
            ],
        },
    )
    return table


def pur_report_by_owner(start_date: str, end_date: str):
    start_date: datetime = datetime.strptime(start_date, "%Y-%m-%d %H:%M:%S")
    end_date: datetime = datetime.strptime(end_date, "%Y-%m-%d %H:%M:%S")
    pur, prt, plant, ss = pur_data(start_date, end_date)
    work_minutes = len(get_workdays(start_date, end_date)) * 8 * 60

    c1 = pl.col("application") != "debug"
    c2 = pl.col("findate_act").is_between(start_date, end_date)
    pur1 = (
        pur.filter(c1 & c2)
        .group_by("pur")
        .agg(ar_mat=1 - (pl.col("received_gt_pcb").sum() / pl.len()))
    )
    pur = (
        pur.filter(pl.col("mat_receiveddate").is_between(start_date, end_date))
        .group_by("pur")
        .agg(
            a=(pl.col("type_owner") == "a").sum(),
            b=(pl.col("type_owner") == "b").sum(),
            c=(pl.col("type_owner") == "c").sum(),
            tat_proces=pl.col("process_achieved").sum()
            / (pl.col("application") != "stock").sum(),
            tat_lt=pl.col("es_maint_achieved").sum() / pl.len(),
            tat_ata=(pl.col("ata") & (pl.col("application") == "debug")).sum()
            / (pl.col("application") == "debug").sum(),
        )
        .with_columns(
            pl.when(pl.col("pur") == "Mona.Zhang")
            .then(prt.height)
            .otherwise(0)
            .alias("d")
        )
        .with_columns(
            pl.when(pl.col("pur") == "Mona.Zhang").then(plant).otherwise(0).alias("e")
        )
        .join(pur1, on="pur", how="left")
        .join(ss, on="pur", how="left")
        .fill_null(0)
        .rename({"ss": "f"})
        .with_columns(
            loading=(
                (
                    pl.col("a") * 15
                    + pl.col("b") * 13
                    + pl.col("c") * 19
                    + pl.col("d") * 20
                    + pl.col("e") * 0.5
                    + pl.col("f") * 1
                )
                / work_minutes
            ).round(3)
        )
        .filter(pl.col("pur") != "Bo.Sm.Wang")
    )

    total = pur.select(
        cs.by_name("a", "b", "c", "d", "e", "f").sum(),
        cs.by_name("tat_proces", "tat_lt", "tat_ata", "ar_mat", "loading")
        .mean()
        .round(3),
        pl.lit("汇总").alias("pur"),
    )

    # print(pur[["pur", "p", "es", "ata", "short_ach", "proj_ach", "loading"]])
    table = dag.AgGrid(
        id=id("report-table"),
        className="ag-theme-quartz",
        columnDefs=cols_pur_owner,
        rowData=pur.to_dicts(),
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        # columnSize="responsiveSizeToFit",
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "pinnedBottomRowData": total.to_dicts(),
        },
        style={"height": "75vh"},
        # getRowStyle={
        #     "styleConditions": [
        #         {
        #             "condition": "params.data.pur == '汇总'",
        #             "style": {"backgroundColor": "sandybrown"},
        #         },
        #     ],
        # },
    )
    return table


def pur_report_by_dept(start_date, end_date):
    # TODO:材料数不对，导出时无采购笔数，没有项目数，没有材料数
    start_date: datetime = datetime.strptime(start_date, "%Y-%m-%d %H:%M:%S")
    end_date: datetime = datetime.strptime(end_date, "%Y-%m-%d %H:%M:%S")
    pur, prt, plant, ss = pur_data(start_date, end_date)
    pur_rd = pur.filter(pl.col("pur") == "Bo.Sm.Wang")
    # pur = pur.filter(pl.col("pur") != "Bo.Sm.Wang")
    # work_minutes = len(get_workdays(start_date, end_date)) * 8 * 60

    c1 = pl.col("mat_receiveddate").is_between(start_date, end_date)
    c2 = pl.col("findate_act").is_between(start_date, end_date)

    c0x = pl.col("received_gt_smd_start_3d").sum()
    c0y = pl.col("received_gt_smd_start_3d").count()

    c1x = pl.col("prt_id").filter(pl.col("received_gt_smd_start_3d")).n_unique()
    c1y = pl.col("prt_id").n_unique()

    c2x = (pl.col("is_new_material") == "Y").sum()
    c2y = (pl.col("application") == "project").sum()

    c3x = (pl.col("is_new_material") == "N").sum()
    c4x = pl.col("safetystock_id").is_not_null().sum()

    pur1 = (
        pur.filter(c1)
        .group_by("dept")
        .agg(
            pur_count=pl.count(),
            ee_count=(pl.col("mat_type") == "ee").sum(),
            me_count=(pl.col("mat_type") == "me").sum(),
        )
    )

    pur2 = (
        pur.filter(c2)
        .group_by("dept")
        .agg(
            ach_proj=(c1y - c1x) / c1y,
            ach_mat=(c0y - c0x) / c0y,
            new_mat=c2x,
            less_stock=c3x,
            safety_stock=c4x,
        )
    )

    pur3 = pur_rd.filter(c1).group_by("dept").agg(rd_supply=pl.len())

    prt = prt.group_by("dept").agg(
        mat_count=pl.col("mat_count").sum(),
        prt_count=pl.len(),
        bom=pl.col("bom_achieved").sum() / pl.len(),
    )

    pur = (
        pl.DataFrame({"dept": cfg.kpi_depts})
        .join(pur1, on="dept", how="left")
        .join(pur2, on="dept", how="left")
        .join(pur3, on="dept", how="left")
        .join(prt, on="dept", how="left")
        .with_columns(
            pl.when(pl.col("prt_count").is_null())
            .then(pl.lit(None))
            .otherwise(pl.col("new_mat"))
            .alias("new_mat"),
            pl.when(pl.col("prt_count").is_null())
            .then(pl.lit(None))
            .otherwise(pl.col("less_stock"))
            .alias("less_stock"),
        )
        .with_columns(
            ach_stock=(pl.col("mat_count") - pl.col("new_mat")) / pl.col("mat_count")
        )
    )

    table = dag.AgGrid(
        id=id("report-table"),
        className="ag-theme-quartz",
        columnDefs=cols_pur_dept,
        rowData=pur.to_dicts(),
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        # columnSize="responsiveSizeToFit",
        # dashGridOptions={
        #     "rowSelection": "single",
        #     "stopEditingWhenCellsLoseFocus": True,
        #     "singleClickEdit": True,
        #     "pinnedBottomRowData": total,
        # },
        style={"height": "75vh"},
        getRowStyle={
            "styleConditions": [
                {
                    "condition": "params.data.SPEC == 'All'",
                    "style": {"backgroundColor": "sandybrown"},
                },
            ],
        },
    )
    return table


def pur_report_by_vendor(start_date, end_date):
    start_date: datetime = datetime.strptime(start_date, "%Y-%m-%d %H:%M:%S")
    end_date: datetime = datetime.strptime(end_date, "%Y-%m-%d %H:%M:%S")
    pur, prt, plant, ss = pur_data(start_date, end_date)
    pur = pur.filter(pl.col("pur") != "Bo.Sm.Wang")

    c1 = pl.col("mat_receiveddate").is_between(start_date, end_date)
    cx1 = (pl.col("mat_receiveddate") - pl.col("pur_date")).mean().dt.total_days()
    cx2 = pl.col("total_price").sum()
    pur1 = (
        pur.filter(c1)
        .group_by("vendor")
        .agg(
            received=pl.len(),
            lt_mean=cx1,
            total_price=cx2,
        )
    )

    c2 = pl.col("start_date").is_between(start_date, end_date)
    pur2 = pur.filter(c2).group_by("vendor").agg(apply=pl.len())

    pur = pur1.join(pur2, on="vendor", how="full", coalesce=True).sort(
        "total_price", descending=True, nulls_last=True
    )

    table = dag.AgGrid(
        id=id("report-table"),
        className="ag-theme-quartz",
        columnDefs=cols_pur_vendor,
        rowData=pur.to_dicts(),
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        # columnSize="responsiveSizeToFit",
        # dashGridOptions={
        #     "rowSelection": "single",
        #     "stopEditingWhenCellsLoseFocus": True,
        #     "singleClickEdit": True,
        #     "pinnedBottomRowData": total,
        # },
        style={"height": "75vh"},
        getRowStyle={
            "styleConditions": [
                {
                    "condition": "params.data.SPEC == 'All'",
                    "style": {"backgroundColor": "sandybrown"},
                },
            ],
        },
    )
    return table


def pur_report_by_proj(start_date, end_date):
    start_date: datetime = datetime.strptime(start_date, "%Y-%m-%d %H:%M:%S")
    end_date: datetime = datetime.strptime(end_date, "%Y-%m-%d %H:%M:%S")
    pur, prt, plant, ss = pur_data(start_date, end_date)

    pur = pur.group_by("prt_id").agg(
        new_stock=(pl.col("is_new_material") == "Y").sum(),
        less_stock=(pl.col("is_new_material") == "N").sum(),
        safety_stock=(pl.col("safetystock_id").is_not_null()).sum(),
        rd_stock=(pl.col("pur") == "Bo.Sm.Wang").sum(),
        gt_smd_3d=pl.col("received_gt_smd_start_3d")
        .filter(pl.col("received_gt_smd_start_3d"))
        .count(),
        gt_pcb=pl.col("received_gt_pcb").filter(pl.col("received_gt_pcb")).count(),
    )
    prt = prt.join(pur, on="prt_id", how="left")

    table = dag.AgGrid(
        id=id("report-table"),
        className="ag-theme-quartz",
        columnDefs=cols_pur_proj,
        rowData=prt.to_dicts(),
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        # columnSize="responsiveSizeToFit",
        # dashGridOptions={
        #     "rowSelection": "single",
        #     "stopEditingWhenCellsLoseFocus": True,
        #     "singleClickEdit": True,
        #     "pinnedBottomRowData": total,
        # },
        style={"height": "75vh"},
        # getRowStyle={
        #     "styleConditions": [
        #         {
        #             "condition": "params.data.SPEC == 'All'",
        #             "style": {"backgroundColor": "sandybrown"},
        #         },
        #     ],
        # },
    )
    return table


dashboard_layouts = {
    "SPEC": spec_dashboard,
    "PUR": pur_dashboard,
    "CE": lambda x: fac.AntdEmpty(),
    "SM": lambda x: fac.AntdEmpty(),
    "WH": lambda x: fac.AntdEmpty(),
}

report_layouts = {
    "SPEC-owner": spec_report_by_owner,
    "SPEC-dept": spec_report_by_dept,
    "PUR-owner": pur_report_by_owner,
    "PUR-dept": pur_report_by_dept,
    "PUR-vendor": pur_report_by_vendor,
    "PUR-proj": pur_report_by_proj,
    "CE-owner": lambda x, y: fac.AntdEmpty(),
    "CE-dept": lambda x, y: fac.AntdEmpty(),
    "SM-owner": lambda x, y: fac.AntdEmpty(),
    "SM-dept": lambda x, y: fac.AntdEmpty(),
    "WH-owner": lambda x, y: fac.AntdEmpty(),
    "WH-dept": lambda x, y: fac.AntdEmpty(),
}


def summary_layout():
    df = read_db(
        "select id,dept,SMTStaDate as smd_date,DIPStaDate as dip_start,\
            FSDate_Act as dip_end\
            from ssp.prt where DIPStaDate!=FSDate_Act \
                and DIPStaDate is not null and FSDate_Act is not null and DIPStaDate>%s",
        params=["2024-01-01"],
    )
    df1 = read_db(
        "select prt_id as id,max(finish_date) as bom_date from ssp.bom_record where finish_date is not null group by prt_id"
    )
    df = (
        df.join(df1, on="id", how="left")
        .filter(pl.col("dip_start").is_not_null() & (pl.col("dept") == "DES_CDBU"))
        .with_columns(
            (pl.col("dip_end") - pl.col("dip_start")).dt.total_hours().alias("days"),
            # pl.col("bom_date").cast(pl.Float64),
            # pl.col("smd_date").cast(pl.Float64),
            pl.col("smd_date").dt.timestamp(),
            pl.col("bom_date").dt.timestamp(),
            pl.col("dip_start").dt.timestamp().alias("dip_start_ts"),
            pl.col("dip_end").dt.timestamp().alias("dip_end_ts"),
        )
        .with_columns(
            pl.col("days").mean().alias("mean"),
        )
        .select(
            [
                # "id",
                "dept",
                "dip_start",
                "dip_end",
                "dip_start_ts",
                "dip_end_ts",
                "mean",
            ]
        )
        .sort(by=["dip_start"])
    )
    print(df)
    fig = px.scatter(
        df, x="dip_start_ts", y="dip_end_ts", color="dept", trendline="ols"
    )
    results = px.get_trendline_results(fig)
    print(results.px_fit_results[0].params)
    print(datetime.now().timestamp())
    print(datetime.fromtimestamp(1732706365))

    graph = dcc.Graph(figure=fig)
    return graph
    # df = pl.read_csv(r"D:\proj\debug\log\log.csv")
    # df = df.cast({"datetime": pl.Datetime}).with_columns(
    #     pl.col("datetime").dt.day().alias("day"),
    #     pl.col("datetime").dt.month().alias("month"),
    #     pl.col("datetime").dt.date().alias("date"),
    #     pl.col("datetime").dt.week().alias("week"),
    #     pl.col("datetime").dt.weekday().alias("weekday"),
    # )
    # df4 = df.group_by(["month"]).count().sort(by=["month"])
    # graph = fact.AntdArea(
    #     data=df4.to_dicts(),
    #     xField="month",
    #     yField="count",
    # )
    # return graph


def dashboard_layout(user: dict):
    role_group = user.get("role_group")
    now = datetime.now()
    layout = dmc.Tabs(
        [
            dmc.TabsList(
                [
                    dmc.Tab("规格", value="SPEC", color="red"),
                    dmc.Tab("物料", value="PUR", color="green"),
                    dmc.Tab("材料", value="CE", color="blue"),
                    dmc.Tab("样制", value="SM", color="orange"),
                    dmc.Tab("仓库", value="WH", color="pink"),
                    fac.AntdSpace(
                        [
                            dmc.ActionIcon(
                                DashIconify(
                                    icon="material-symbols:download",
                                    width=20,
                                ),
                                id=id("dashboard-download"),
                                color="blue",
                                # variant="light",
                            ),
                            dcc.Store(id=id("dashboard-store")),
                            "记录时间段:",
                            fac.AntdDatePicker(
                                picker="month",
                                format="YYYY-MM",
                                # disabledDatesStrategy=[
                                #     {
                                #         "mode": "gt",
                                #         "target": "specific-date",
                                #         "value": f"{now:%Y-%m-%d}",
                                #     },
                                # ],
                                id=id("dashboard-month"),
                                value=f"{now:%Y-%m}",
                            ),
                        ],
                        style={"margin-left": "auto"},
                    ),
                ],
            ),
            dmc.Space(h=5),
            html.Div(id=id("dashboard-content")),
        ],
        value=role_group,
        id=id("dashboard-tabs"),
    )

    return layout


def report_layout(user: dict):
    role_group = user.get("role_group")
    today = pd.Timestamp.now().date()
    start_date = today.replace(day=1)
    end_date = today.replace(day=1) + pd.offsets.MonthEnd(1)
    end_date = end_date.date()
    # start_date = "2025-02-01"
    # end_date = "2025-02-28"
    layout = dmc.Tabs(
        [
            dmc.TabsList(
                [
                    dmc.Tab("规格", value="SPEC", color="red"),
                    dmc.Tab("物料", value="PUR", color="green"),
                    dmc.Tab("材料", value="CE", color="blue"),
                    dmc.Tab("样制", value="SM", color="orange"),
                    dmc.Tab("仓库", value="WH", color="pink"),
                    # dmc.Tab("仓库", value="仓库", color="violet"),
                ],
            ),
            dmc.Space(h=10),
            fac.AntdSpace(
                [
                    fac.AntdDateRangePicker(
                        id=id("date_range"),
                        defaultValue=[f"{start_date}", f"{end_date}"],
                        value=[f"{start_date}", f"{end_date}"],
                    ),
                    fac.AntdSelect(
                        options=[
                            {"label": "人员绩效", "value": "owner"},
                            {"label": "部门分摊", "value": "dept"},
                            {"label": "项目清单", "value": "proj"},
                            {"label": "供应商", "value": "vendor"},
                        ],
                        value="owner",
                        id=id("kpi_type"),
                        style={"width": "200px"},
                    ),
                    fac.AntdButton(
                        "导出报表",
                        id=id("report-table-export"),
                        type="primary",
                        # variant="outline",
                        # type="reset",
                    ),
                ],
                size=100,
            ),
            dcc.Loading(id=id("report-content")),
        ],
        value=role_group,
        id=id("report-tabs"),
    )
    return layout


def issue_layout(user):
    nt_name = user.get("nt_name").lower()
    role_group = user.get("role_group")

    if nt_name in ("zhen.liu", "weiming.li"):
        sql = "select * from ssp.issue"
        data = db.execute(sql)
    else:
        sql = "select * from ssp.issue \
            where initiator=%s or approver=%s or handler=%s or role_group=%s"
        data = db.execute(sql, [nt_name, nt_name, nt_name, role_group])

    sql = "select distinct nt_name from ssp.user where dept_id=%s"
    users = db.execute(sql, [10])
    handler_options = [
        {"label": i["nt_name"].title(), "value": i["nt_name"].title()} for i in users
    ]
    sql = "select concat_ws('_',dept_group,dept_name) as dept from ssp.dept"
    dept = db.execute(sql)
    dept_options = [
        {"label": "SUP_SUP", "value": "SUP_SUP"},
        {"label": "Others", "value": "Others"},
    ] + [{"label": i["dept"], "value": i["dept"]} for i in dept]
    table = dag.AgGrid(
        id=id("issue-table"),
        className="ag-theme-quartz",
        columnSize="sizeToFit",
        columnDefs=[
            {"field": "status", "headerName": "状态", "width": 140, "sort": "desc"},
            {
                "field": "gmt_create",
                "headerName": "创建日期",
                "width": 130,
                "valueGetter": {
                    "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.gmt_create)"
                },
                "valueFormatter": {"function": "d3.timeFormat('%m/%d')(params.value)"},
            },
            {"field": "initiator", "headerName": "发起人", "width": 150},
            {"field": "description", "headerName": "问题描述"},
            {"field": "dept", "headerName": "责任部门", "width": 130},
            {"field": "role_group", "headerName": "责任单位", "width": 130},
            {"field": "approver", "headerName": "责任主管", "width": 130},
            {"field": "handler", "headerName": "处理人", "width": 150},
            {"field": "analysis", "headerName": "问题分析"},
            {"field": "improvement", "headerName": "改善对策"},
            {"field": "update_spec", "headerName": "更新规范", "width": 130},
            {"field": "accountable", "headerName": "责任人", "width": 150},
            # {"field": "gmt_create", "headerName": "发起日期", "width": 150},
            # {"field": "kpi", "headerName": "是否影响绩效", "width": 130},
            # {"field": "score", "headerName": "绩效分", "width": 150},
            # {"field": "appointer", "headerName": "指派人", "width": 150},
            # {"field": "status", "headerName": "状态"},
            # {"field": "start_date", "headerName": "开始日期"},
            # {"field": "end_date", "headerName": "结束日期"},
        ],
        rowData=data,
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
        },
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        getRowStyle={
            "styleConditions": [
                {
                    "condition": "params.data.status=='new'",
                    "style": {"backgroundColor": "#99CC99"},
                },
                {
                    "condition": "params.data.status=='open'",
                    "style": {"backgroundColor": " #FFCC99"},
                },
                {
                    "condition": "params.data.status=='processing'",
                    "style": {"backgroundColor": " #FFCC99"},
                },
            ]
        },
        style={"height": "80vh"},
        getRowId="params.data.id",
    )
    div = dmc.Stack(
        [
            dmc.ActionIcon(
                DashIconify(
                    icon="material-symbols-light:fiber-new-outline",
                    color="green",
                    height=30,
                ),
                id=id("new-issue"),
            ),
            table,
            fac.AntdModal(
                fac.AntdFlex(
                    [
                        dcc.Store(id=id("store")),
                        fac.AntdSpace(
                            [
                                # fac.AntdText("问题描述"),
                                fac.AntdInput(
                                    id=id("description"),
                                    mode="text-area",
                                    allowClear=True,
                                    style={"height": "100px"},
                                    placeholder="请在此描述你的问题",
                                ),
                            ],
                            direction="vertical",
                        ),
                        fac.AntdFlex(
                            [
                                fac.AntdSpace(
                                    [
                                        fac.AntdText("责任部门"),
                                        fac.AntdSelect(
                                            id=id("dept"),
                                            style={"width": "150px"},
                                            options=dept_options,
                                            placeholder="请选择责任部门",
                                        ),
                                    ],
                                    direction="horizontal",
                                ),
                                fac.AntdSpace(
                                    [
                                        fac.AntdText("责任单位"),
                                        fac.AntdSelect(
                                            id=id("role_group"),
                                            style={"width": "150px"},
                                            placeholder="请选择责任单位",
                                            options=[
                                                {"label": "SPEC", "value": "SH-SPEC"},
                                                {"label": "PUR", "value": "SH-PUR"},
                                                {"label": "CE", "value": "SH-CE"},
                                                {"label": "贴片", "value": "SH-SMT"},
                                                {"label": "上海插件", "value": "SH-SM"},
                                                {"label": "杭州插件", "value": "HZ-SM"},
                                                {"label": "武汉插件", "value": "WH-SM"},
                                                {"label": "CNAS", "value": "SH-CNAS"},
                                                {"label": "统购", "value": "SH-统购"},
                                                {
                                                    "label": "供应商",
                                                    "value": "SH-供应商",
                                                },
                                                {"label": "其它", "value": "SH-其它"},
                                                {"label": "EE", "value": "SH-EE"},
                                                {"label": "ME", "value": "SH-ME"},
                                                {
                                                    "label": "LAYOUT",
                                                    "value": "SH-LAYOUT",
                                                },
                                            ],
                                        ),
                                    ],
                                    direction="horizontal",
                                ),
                                fac.AntdSpace(
                                    [
                                        fac.AntdText("责任主管"),
                                        fac.AntdSelect(
                                            id=id("approver"),
                                            style={"width": "150px"},
                                        ),
                                    ],
                                    direction="horizontal",
                                ),
                                fac.AntdSpace(
                                    [
                                        fac.AntdText("处理人"),
                                        fac.AntdSelect(
                                            id=id("handler"),
                                            style={"width": "150px"},
                                            options=handler_options,
                                        ),
                                    ],
                                    direction="horizontal",
                                ),
                            ],
                            justify="space-between",
                            # size=50,
                        ),
                        fac.AntdSpace(
                            [
                                fac.AntdText("问题分析"),
                                fac.AntdInput(
                                    id=id("analysis"),
                                    mode="text-area",
                                    allowClear=True,
                                    style={"height": "100px"},
                                ),
                            ],
                            direction="vertical",
                        ),
                        fac.AntdSpace(
                            [
                                fac.AntdText("改善对策"),
                                fac.AntdInput(
                                    id=id("improvement"),
                                    mode="text-area",
                                    allowClear=True,
                                    style={"height": "100px"},
                                ),
                            ],
                            direction="vertical",
                        ),
                        # fac.AntdSpace(
                        #     [
                        #         fac.AntdText("是否更新规范:"),
                        #         fac.AntdSelect(
                        #             id=id("update_spec"),
                        #             style={"width": "80px"},
                        #             options=["是", "否"],
                        #         ),
                        #     ]
                        # ),
                        fac.AntdSpace(
                            [
                                # fac.AntdText("问题判定"),
                                fac.AntdForm(
                                    [
                                        fac.AntdFormItem(
                                            fac.AntdSelect(
                                                id=id("update_spec"),
                                                style={"width": "80px"},
                                                options=["是", "否"],
                                            ),
                                            label="是否更新规范",
                                        ),
                                        fac.AntdFormItem(
                                            fac.AntdSelect(
                                                id=id("kpi"),
                                                style={"width": "80px"},
                                                options=["是", "否"],
                                            ),
                                            label="是否影响绩效",
                                        ),
                                        fac.AntdFormItem(
                                            fac.AntdInputNumber(
                                                id=id("score"),
                                                min=0,
                                                max=100,
                                                step=1,
                                                style={"width": "100px"},
                                            ),
                                            label="绩效分",
                                        ),
                                        fac.AntdFormItem(
                                            fac.AntdSelect(
                                                id=id("accountable"),
                                                style={"width": "300px"},
                                                options=handler_options,
                                                mode="multiple",
                                            ),
                                            label="责任人",
                                        ),
                                    ],
                                    layout="inline",
                                    style={"justifyContent": "space-between"},
                                ),
                            ],
                            direction="vertical",
                        ),
                    ],
                    vertical=True,
                    gap=20,
                ),
                title="问题描述",
                id=id("new-modal"),
                renderFooter=True,
                okClickClose=False,
                centered=True,
                width=1200,
                zIndex=9999,
            ),
        ],
        spacing=0,
    )
    return div


def oa_layout():
    return fac.AntdFlex(
        [
            fac.AntdTitle("AI填单助手", level=3),
            fac.AntdDraggerUpload(
                text="点击上传填单记录表",
                apiUrl="/upload/oa/",
                style={"width": "100%"},
            ),
        ],
        vertical=True,
        # justify="center",
        # style={"width": "100%"},
        align="center",
    )


def mat_layout():
    # <iframe
    #  src="http://10.232.85.22:8888/chatbot/uESc8CC91Nevo7Ea"
    #  style="width: 100%; height: 100%; min-height: 700px"
    #  frameborder="0"
    #  allow="microphone">
    # </iframe>
    return html.Iframe(
        src="http://10.232.85.22:8888/chatbot/uESc8CC91Nevo7Ea",
        style={"width": "100%", "height": "100%"},
    )


def shortage_rate():
    sql = "select id as bom_id,prt_id,date(finish_date) as finish_date \
        from bom_record where finish_date>=%s"
    df1 = read_db(sql, params=["2025-01-01"])
    dfx = df1.group_by(["prt_id"]).max()
    bom_id = dfx["bom_id"].to_list()
    df1 = df1.filter(pl.col("bom_id").is_in(bom_id))
    sql = "select bom_id,area,checkcode,demand_qty,stock_qty \
        from bom_shortage where bom_id in %s"
    params = [bom_id]
    df2 = (
        read_db(sql, params=params)
        .join(df1, on="bom_id", how="left")
        .with_columns(week=pl.col("finish_date").dt.week())
    )
    df2 = df2.group_by(["week", "area", "checkcode"]).agg(
        pl.col("demand_qty").sum(), pl.col("stock_qty").max()
    )
    sql = "select  dest_area as area,checkcode,\
        received_qty as ss_qty,date(mat_receiveddate) as ss_date \
        from pur where application=%s and mat_receiveddate>=%s"
    params = ["stock", "2025-01-01"]
    pur = read_db(sql, params=params).with_columns(week=pl.col("ss_date").dt.week())
    weeks = pur["week"].unique().sort()
    x = []
    for i in weeks:
        pur_i = pur.filter(pl.col("week") <= i).with_columns(week=i)
        x.append(pur_i)
    pur = pl.concat(x)
    pur = pur.group_by(["week", "area", "checkcode"]).agg(pl.col("ss_qty").sum())
    df2 = (
        df2.join(pur, on=["week", "area", "checkcode"], how="left")
        .with_columns(ss_qty=pl.col("ss_qty").fill_null(0))
        .with_columns(
            rate1=pl.col("stock_qty") > pl.col("demand_qty"),
            rate2=(pl.col("stock_qty") - pl.col("ss_qty")) > pl.col("demand_qty"),
        )
        .group_by(["week"])
        .agg(
            rate1=pl.col("rate1").sum() / pl.len(),
            rate2=pl.col("rate2").sum() / pl.len(),
        )
        .sort("week")
        .with_columns(week=pl.col("week").cast(pl.String))
        .unpivot(["rate1", "rate2"], index=["week"])
    )
    df2 = df2.with_columns(
        pl.col("variable").replace(
            ["rate1", "rate2"],
            ["缺料率(包含安全库存)", "缺料率(不含安全库存)"],
        )
    )

    graph = fact.AntdLine(
        data=df2.to_dicts(),
        xField="week",
        yField="value",
        seriesField="variable",
        color=["#f6c022", "#61d9ab"],
        label={
            "position": "left",
            "formatter": {"func": "({ value }) => `${(value * 100).toFixed(1)}%`"},
        },
        height=200,
        animation=True,
        point={"shape": "circle"},
    )
    return graph


def turnover_year():
    sql = "select id,checkcode,des,qty as qty_end from stock where area=%s"
    params = ["SH"]
    df = read_db(sql, params=params)

    sql = "select uid,id,qty as qty,create_time,type from stock_modify where create_time>=%s and area=%s and type in %s"
    params = ["2024-03-26", "SH", ["before", "after"]]
    df1 = read_db(sql, params=params)

    # 根据type计算数量变化
    df1 = df1.with_columns(
        qty_change=pl.when(pl.col("type") == "after")
        .then(-pl.col("qty"))
        .otherwise(pl.col("qty"))
    )

    # 按id分组求和变化量
    df1 = df1.group_by("id").agg(qty_change=pl.col("qty_change").sum())

    sql = "select stock_id as id,sum(qty) as qty_so from stockout \
        where stock_id is not null and StockOutDate>=%s group by stock_id"
    params = ["2024-03-26"]
    df_so = (
        read_db(sql, params=params)
        .with_columns(qty_so=pl.col("qty_so").cast(pl.Int64))
        .with_columns(
            qty_so=pl.when(pl.col("qty_so") > 0).then(pl.col("qty_so")).otherwise(0)
        )
    )

    sql = "select  checkcode,received_qty as qty_ss \
        from pur where application=%s and mat_receiveddate>=%s and dest_area=%s"
    params = ["stock", "2023-03-26", "SH"]
    df_ss = (
        read_db(sql, params=params).group_by(["checkcode"]).agg(pl.col("qty_ss").sum())
    )

    # 将变化量应用到当前库存,得到2024-03-26时的库存
    df = (
        df.join(df1, on="id", how="left")
        .join(df_so, on="id", how="left")
        .join(df_ss, on="checkcode", how="left")
        .with_columns(qty_start=pl.col("qty_end") + pl.col("qty_change").fill_null(0))
        .fill_null(0)
        .filter(~((pl.col("qty_start") == 0) & (pl.col("qty_end") == 0)))
        .with_columns(qty_avg=(pl.col("qty_start") + pl.col("qty_end")) / 2)
        .with_columns(turnover=(pl.col("qty_so") / pl.col("qty_avg")))
    )
    # df.write_excel("d:/turnover.xlsx")

    return

    # 获取每周的库存变化
    df12 = (
        df1.with_columns(week=pl.col("create_time").dt.week())
        .group_by(["id", "week"])
        .agg(qty_change=pl.col("qty_change").sum())
    )

    weeks = df12["week"].unique().sort()
    dfi = df.select(["id", "checkcode", "qty_start"]).clone()

    data = []
    for i in weeks:
        df12_i = df12.filter(pl.col("week") == i).select(["id", "qty_change"])
        # df12_i = df12_i.group_by(["id"]).agg(pl.col("qty_change").sum())
        # df_so = df2.filter(pl.col("week") == i).select(["id", "qty_so"])
        df_pur = df3.filter(pl.col("week") == i).select(["checkcode", "qty_ss"])

        dfi = (
            dfi.join(df12_i, on="id", how="left")
            .join(df_pur, on="checkcode", how="left")
            .fill_null(0)
            .with_columns(qty_end=(pl.col("qty_start") - pl.col("qty_change")))
            .with_columns(qty_end2=(pl.col("qty_end") - pl.col("qty_ss")))
            .with_columns(qty_avg=(pl.col("qty_start") + pl.col("qty_end")) / 2)
            .with_columns(qty_avg2=(pl.col("qty_start") + pl.col("qty_end2")) / 2)
            .with_columns(
                qty_so=(
                    pl.when(pl.col("qty_change") > 0)
                    .then(pl.col("qty_change"))
                    .otherwise(0)
                )
            )
        )
        x1 = dfi.get_column("qty_so").sum() / dfi.get_column("qty_avg").sum()
        x2 = dfi.get_column("qty_so").sum() / dfi.get_column("qty_avg2").sum()
        data.extend(
            [
                {
                    "week": i,
                    "type": "周转率(含安全库存)",
                    "rate": round(x1 * 52, 2),
                },
                {
                    "week": i,
                    "type": "周转率(不含安全库存)",
                    "rate": round(x2 * 52, 2),
                },
            ]
        )
        dfi = dfi.with_columns(qty_start=pl.col("qty_end")).select(
            ["id", "checkcode", "qty_start"]
        )
    graph = fact.AntdLine(
        data=data,
        xField="week",
        yField="rate",
        seriesField="type",
        color=["#87CEFA", "#F58E8E", "#22C55E"],
        # isStack=True,
        # smooth=True,
        label={
            "position": "left",
            # "formatter": {"func": "({ rate1 }) => `${(rate1 * 100).toFixed(0)}%`"},
        },
        height=200,
        animation=True,
        point={"shape": "circle"},
    )
    return graph


def turnover_rate():
    sql = "select id,qty as qty_now,checkcode from stock where area=%s"
    params = ["SH"]
    df = read_db(sql, params=params)

    sql = "select uid,id,qty as qty,create_time,type from stock_modify where create_time>=%s and area=%s and type in %s"
    params = ["2025-01-01", "SH", ["before", "after"]]
    df1 = read_db(sql, params=params)

    # sql = (
    #     "select stock_id as id,sum(qty) as qty_so,StockOutDate as so_date from stockout \
    #     where stock_id is not null and StockOutDate>=%s group by stock_id"
    # )
    # params = ["2025-01-01"]
    # df2 = (
    #     read_db(sql, params=params)
    #     .with_columns(qty_so=pl.col("qty_so").cast(pl.Int64))
    #     .with_columns(week=pl.col("so_date").dt.week())
    #     .group_by(["week", "id"])
    #     .agg(qty_so=pl.col("qty_so").sum())
    # )

    sql = "select  checkcode,\
        received_qty as qty_ss,date(mat_receiveddate) as ss_date \
        from pur where application=%s and mat_receiveddate>=%s and dest_area=%s"
    params = ["stock", "2025-01-01", "SH"]
    df3 = read_db(sql, params=params).with_columns(week=pl.col("ss_date").dt.week())
    df3 = df3.group_by(["week", "checkcode"]).agg(pl.col("qty_ss").sum())

    # 根据type计算数量变化
    df1 = df1.with_columns(
        qty_change=pl.when(pl.col("type") == "after")
        .then(-pl.col("qty"))
        .otherwise(pl.col("qty"))
    )

    # 按id分组求和变化量
    df11 = df1.group_by("id").agg(qty_change=pl.col("qty_change").sum())

    # 将变化量应用到当前库存,得到2025-01-01时的库存
    df = df.join(df11, on="id", how="left").with_columns(
        qty_start=pl.col("qty_now") + pl.col("qty_change").fill_null(0)
    )

    # 获取每周的库存变化
    df12 = (
        df1.with_columns(week=pl.col("create_time").dt.week())
        .group_by(["id", "week"])
        .agg(qty_change=pl.col("qty_change").sum())
    )

    weeks = df12["week"].unique().sort()
    dfi = df.select(["id", "checkcode", "qty_start"]).clone()

    data = []
    for i in weeks:
        df12_i = df12.filter(pl.col("week") == i).select(["id", "qty_change"])
        # df12_i = df12_i.group_by(["id"]).agg(pl.col("qty_change").sum())
        # df_so = df2.filter(pl.col("week") == i).select(["id", "qty_so"])
        df_pur = df3.filter(pl.col("week") == i).select(["checkcode", "qty_ss"])

        dfi = (
            dfi.join(df12_i, on="id", how="left")
            .join(df_pur, on="checkcode", how="left")
            .fill_null(0)
            .with_columns(qty_end=(pl.col("qty_start") - pl.col("qty_change")))
            .with_columns(qty_end2=(pl.col("qty_end") - pl.col("qty_ss")))
            .with_columns(qty_avg=(pl.col("qty_start") + pl.col("qty_end")) / 2)
            .with_columns(qty_avg2=(pl.col("qty_start") + pl.col("qty_end2")) / 2)
            .with_columns(
                qty_so=(
                    pl.when(pl.col("qty_change") > 0)
                    .then(pl.col("qty_change"))
                    .otherwise(0)
                )
            )
        )
        x1 = dfi.get_column("qty_so").sum() / dfi.get_column("qty_avg").sum()
        x2 = dfi.get_column("qty_so").sum() / dfi.get_column("qty_avg2").sum()
        data.extend(
            [
                {
                    "week": i,
                    "type": "周转率(含安全库存)",
                    "rate": round(x1 * 52, 2),
                },
                {
                    "week": i,
                    "type": "周转率(不含安全库存)",
                    "rate": round(x2 * 52, 2),
                },
            ]
        )
        dfi = dfi.with_columns(qty_start=pl.col("qty_end")).select(
            ["id", "checkcode", "qty_start"]
        )
    graph = fact.AntdLine(
        data=data,
        xField="week",
        yField="rate",
        seriesField="type",
        color=["#87CEFA", "#F58E8E", "#22C55E"],
        # isStack=True,
        # smooth=True,
        label={
            "position": "left",
            # "formatter": {"func": "({ rate1 }) => `${(rate1 * 100).toFixed(0)}%`"},
        },
        height=200,
        animation=True,
        point={"shape": "circle"},
    )
    return graph


def home_layout(user):
    img = fac.AntdImage(src="/assets/img/ai.svg", height=100, preview=False)
    question = fac.AntdTooltip(
        fac.AntdInput(
            id=id("question"),
            placeholder="请输入问题,例如:按月统计2025年以来各部门项目申请占比，用图表显示",
            mode="search",
            autoFocus=True,
            allowClear=True,
            size="large",
        ),
        title=dcc.Markdown(
            """
            例如    
            2025年1512438C02料号的出库记录    
            SHSS2503056的样制bom   
            2025年3月份完成的bom工作记录，要有部门    
            按采购人员，统计2025年2月份各自的采购笔数
            统计2025年3月份各部门出库占比  
            按月统计2025年各采购人员的工作量，用折线图显示              
            """
        ),
    )
    # input = fac.AntdInput(
    #     id=id("question"),
    #     placeholder="请输入问题,例如:2025年3月份完成的bom工作记录，要有部门",
    #     mode="search",
    #     # autoFocus=True,
    #     allowClear=True,
    #     size="large",
    #     value="按月统计2025年以来各部门项目申请占比，用图表显示",
    # )
    # select = fac.AntdSelect(
    #     options=[{"label": f"选项{i}", "value": f"选项{i}"} for i in range(1, 6)],
    #     style={"width": "100%"},
    #     size="large",
    # )
    # question = html.Div([input, select])
    result = dcc.Loading(html.Div(id=id("ai-result")), type="dot")
    store = dcc.Store(id=id("task-store"), data={})
    interval = dcc.Interval(
        id=id("interval"), interval=5000, n_intervals=0, max_intervals=10
    )

    # turnover_year()
    # graph1 = shortage_rate()
    # graph2 = turnover_rate()

    return fac.AntdFlex([img, question, result, store, interval], vertical=True, gap=10)
    # div = dmc.Stack(
    #     [
    #         ChatComponent(
    #             id="chat-component",
    #             messages=[{"role": "assistant", "content": "Hello!"}],
    #             container_style={
    #                 "height": "80vh !important",
    #                 "width": "100%",
    #             },
    #         )
    #     ]
    # )
    # return div
    df = (
        pl.read_csv("d:/log_user.csv", low_memory=True)
        .with_columns(
            pl.col("interface")
            .str.replace("material", "材料查询")
            .str.replace("pur", "采购")
            .str.replace("tools", "工具领用")
            .str.replace("bom", "BOM作业")
            .str.replace("stock", "仓库")
            .str.to_uppercase()
        )
        .filter(~pl.col("user").is_in(["Qiqi.Zheng", "Shouxiu.Zhang", "Xiaowei.Zhu"]))
    )
    # print(
    #     df.filter(
    #         pl.col("user").is_in(["Wei.Pd.Feng"]) & (pl.col("year") == 2024)
    #     ).write_excel("d:/log_fw.xlsx")
    # )
    # return fac.AntdResult(status="404")
    # user = read_db("select nt_name as user,dept,area from user").with_columns(
    #     pl.col("user").str.to_titlecase()
    # )
    # df = df.join(user, on="user", how="left")
    # df.write_csv("d:/log_user.csv")
    # print(df["interface"].unique().to_list())
    # print(df)
    df24 = df.filter(pl.col("year") == 2024)

    df1 = df.group_by(["year", "month"]).agg(count=pl.len()).sort(["year", "month"])
    mean_2023 = int(df1.filter(pl.col("year") == 2023).get_column("count").mean())
    mean_2024 = int(df1.filter(pl.col("year") == 2024).get_column("count").mean())

    fig = px.line(
        df1.to_dicts(),
        x="month",
        y="count",
        color="year",
        title=f"月点击数量:2024年月均({mean_2024}),相较2023年({mean_2023}),增长{int((mean_2024 - mean_2023) / mean_2023 * 100)}%",
        template="simple_white",
    )
    g1 = dcc.Graph(figure=fig)

    df2 = (
        df24.group_by("user")
        .agg(count=pl.len(), dept=pl.first("dept"), area=pl.first("area"))
        .sort("count", descending=True)
    )
    g2 = fact.AntdWordCloud(
        data=df2.to_dicts(),
        wordField="user",
        weightField="count",
        colorField="dept",
        imageMask="/assets/img/2024.svg",
    )
    df3 = df24.group_by("dept").count().drop_nulls().sort("count", descending=True)
    labels = df3["dept"].to_list()
    values = df3["count"].to_list()
    fig = go.Figure(
        data=[
            go.Pie(
                labels=labels,
                values=values,
                hole=0.3,
                textinfo="label+percent",
                textposition="inside",
                title="部门分布",
            )
        ]
    )

    g3 = dcc.Graph(figure=fig)
    df4 = df24.group_by("area").count().drop_nulls().sort("count", descending=True)
    labels = df4["area"].to_list()
    values = df4["count"].to_list()
    fig = go.Figure(
        data=[
            go.Pie(
                labels=labels,
                values=values,
                hole=0.3,
                textinfo="label+percent",
                textposition="inside",
                title="地区分布",
            )
        ]
    )

    g4 = dcc.Graph(figure=fig)

    df5 = df24.group_by("interface").count().drop_nulls().sort("count", descending=True)
    labels = df5["interface"].to_list()
    values = df5["count"].to_list()
    fig = go.Figure(
        data=[
            go.Pie(
                labels=labels,
                values=values,
                hole=0.3,
                textinfo="label+percent",
                textposition="inside",
                title="接口分布",
            )
        ]
    )

    g5 = dcc.Graph(figure=fig)

    df6 = (
        df24.filter(pl.col("dept") != "SUP_SUP")
        .group_by("user")
        .agg(
            count=pl.len(),
            dept=pl.col("dept").first(),
        )
        # .count()
        .sort("count", descending=True)
        .head(10)
        .to_dicts()
    )
    g6 = px.bar(
        df6,
        x="user",
        y="count",
        color="dept",
        title="2024年TOP10用户",
        template="simple_white",
    )
    g6 = dcc.Graph(figure=g6)

    df7 = (
        df.group_by(["year", "month"])
        .agg(count=pl.col("user").n_unique())
        .sort(["year", "month"])
    )
    mean_2023 = int(df7.filter(pl.col("year") == 2023).get_column("count").mean())
    mean_2024 = int(df7.filter(pl.col("year") == 2024).get_column("count").mean())
    g7 = px.line(
        df7.to_dicts(),
        x="month",
        y="count",
        color="year",
        title=f"2024年月均活跃用户数({mean_2024}),相较2023年({mean_2023}),增长{int((mean_2024 - mean_2023) / mean_2023 * 100)}%",
        template="simple_white",
    )
    g7 = dcc.Graph(figure=g7)
    return dmc.Stack(
        children=[g1, g2, dmc.Group([g3, g4, g5], noWrap=True), g6, g7], spacing=10
    )


# @dc.memoize_stampede(cache, expire=3600)
def layout(user, page=None, **args):
    active = {"dashboard": False, "report": False, "issue": False, "oa": False}
    active[page] = True
    if page == "dashboard":
        content = dashboard_layout(user)
    elif page == "report":
        content = report_layout(user)
    elif page == "issue":
        content = issue_layout(user)
    elif page == "summary":
        content = summary_layout()
    elif page == "oa":
        content = oa_layout()
    elif page == "mat":
        content = mat_layout()
    else:
        content = home_layout(user)

    sidebar = dmc.Stack(
        [
            dmc.NavLink(
                label="个人中心",
                href="/info/sup",
                icon=DashIconify(icon="material-symbols:home", height=16),
                target="_self",
                refresh=False,
                style={
                    "font-weight": "bolder",
                    "color": "rgb(0, 159, 232)",
                },
            ),
            dmc.Divider(),
            dmc.NavLink(
                label="绩效指标",
                href="/info/sup/dashboard",
                icon=fac.AntdIcon(icon="antd-calculator", style={"margin-top": "-5px"}),
                target="_self",
                color="red",
                refresh=False,
                active=active.get("dashboard"),
            ),
            dmc.NavLink(
                label="统计报表",
                href="/info/sup/report",
                icon=fac.AntdIcon(icon="antd-bar-chart", style={"margin-top": "-5px"}),
                target="_self",
                color="red",
                refresh=False,
                active=active.get("report"),
            ),
            dmc.NavLink(
                label="问题管理",
                href="/info/sup/issue",
                icon=fac.AntdIcon(icon="antd-exception", style={"margin-top": "-5px"}),
                refresh=False,
                color="red",
                active=active.get("issue"),
            ),
            dmc.NavLink(
                label="填单助手",
                href="/info/sup/oa",
                icon=fac.AntdIcon(icon="antd-exception", style={"margin-top": "-5px"}),
                refresh=False,
                color="red",
                active=active.get("oa"),
            ),
            dmc.NavLink(
                label="设计选型",
                href="/info/sup/mat",
                icon=fac.AntdIcon(icon="antd-exception", style={"margin-top": "-5px"}),
                refresh=False,
                color="red",
                active=active.get("mat"),
            ),
        ],
        h="100%",
        w="100%",
        bg="rgba(240,240,240,1)",
        mx=0,
        my=0,
        px=0,
    )

    grid = dmc.Grid(
        [
            dmc.Col(sidebar, span=10, bg="red", p=0),
            dmc.Col(content, span=90),
            dcc.Download(id=id("download")),
        ],
        columns=100,
        h="92vh",
        w="100vw",
        mx=0,
        my=0,
    )
    return grid


D3_DATE_FMT = "params.value?d3.timeFormat('%m/%d')(params.value):''"
D3_PERCENT_FMT = "params.value?d3.format('(.1%')(params.value):''"
cols_owner = [
    {
        "headerName": "SPEC",
        "field": "SPEC",
        "pinned": "left",
        "width": 150,
        "cellClass": "lock-visible-col",
    },
    {"headerName": "EC", "field": "SM/ECN", "width": 70},
    {"headerName": "GRP", "field": "GRP CHANGE", "width": 80},
    {"headerName": "NEW ISSUE", "field": "NEW ISSUE", "width": 86},
    {"headerName": "MODIFY ISSUE", "field": "MODIFY ISSUE", "width": 90},
    {"headerName": "PSL", "field": "ES/TS/SN", "width": 90},
    {"headerName": "OTHER PSL", "field": "RC/OTHERS", "width": 90},
    {"headerName": "PN", "field": "PN", "width": 90},
    {"headerName": "SBOM", "field": "SBOM", "width": 90},
    {"headerName": "CBOM", "field": "CBOM", "width": 90},
    {"headerName": "DFMS", "field": "DFMS/CM", "width": 90},
    {"headerName": "DFCS", "field": "DFCS", "width": 90},
    {"headerName": "PART COST", "field": "PART COST", "width": 90},
    {"headerName": "COST QUERY", "field": "COST QUERY", "width": 90},
    {"headerName": "BOM COST", "field": "BOM COST", "width": 90},
    {"headerName": "TOOLA", "field": "TOOLA", "width": 90},
    {"headerName": "TOOLB", "field": "TOOLB", "width": 90},
    {"headerName": "55X", "field": "55X", "width": 90},
    {"headerName": "ME PART", "field": "ME PART", "width": 90},
    {"headerName": "SMBOM", "field": "SMBOM", "width": 90},
    {"headerName": "CHANGE", "field": "CHANGE", "width": 90},
    {"headerName": "PSL绩效数量", "field": "PSL_QTY", "width": 90},
    {"headerName": "ECN绩效数量", "field": "ECN_QTY", "width": 90},
    {"headerName": "SM工作时间", "field": "SMBOM_WKT", "width": 90},
    {
        "headerName": "SM工作LOADING",
        "field": "SMBOM_LOADING",
        "width": 90,
        "valueFormatter": {"function": "d3.format('(.1%')(params.value)"},
    },
    {
        "headerName": "SPEC工作时间",
        "field": "SPEC_WKT",
        "width": 90,
        # "valueFormatter": {"function": "d3.format('(.1%')(params.value)"},
    },
    {
        "headerName": "SPEC工作LOADING",
        "field": "SPEC_LOADING",
        "width": 90,
        "valueFormatter": {"function": "d3.format('(.1%')(params.value)"},
    },
    {"headerName": "总工作时间", "field": "WORK_TIME_MINUTE", "width": 90},
    {
        "headerName": "总工作LOADING",
        "field": "LOADING",
        "width": 90,
        "valueFormatter": {"function": "d3.format('(.1%')(params.value)"},
    },
    {"headerName": "ECN规格组签核平均时长", "field": "WORK_DAYS", "width": 90},
    {
        "headerName": "ECN规格组会签达成率",
        "field": "RATIO",
        "width": 90,
        "valueFormatter": {
            "function": "params.value?d3.format('(.1%')(params.value):''"
        },
    },
    {
        "headerName": "ECN会签平均时长",
        "field": "ECN_LT_MEAN",
        "width": 90,
        "valueFormatter": {"function": "d3.format('(.1')(params.value)"},
    },
    {
        "headerName": "ECN会签达成率",
        "field": "ECN_ONTIME",
        "width": 90,
        "valueFormatter": {"function": "d3.format('(.1%')(params.value)"},
    },
    {
        "headerName": "SMBOM平均时长",
        "field": "SMBOM_LT_MEAN",
        "width": 90,
        "valueFormatter": {"function": "d3.format('(.1')(params.value)"},
    },
    {
        "headerName": "SMBOM达成率",
        "field": "SMBOM_ONTIME",
        "width": 90,
        "valueFormatter": {"function": "d3.format('(.1%')(params.value)"},
    },
    {
        "headerName": "SMCHANGE平均时长",
        "field": "CHANGE_LT_MEAN",
        "width": 90,
        "valueFormatter": {"function": "d3.format('(.1')(params.value)"},
    },
    {
        "headerName": "SMCHANGE达成率",
        "field": "CHANGE_ONTIME",
        "width": 90,
        "valueFormatter": {"function": "d3.format('(.1%')(params.value)"},
    },
    {
        "headerName": "FOLLOW UP达成率",
        "field": "FOLLOWUP_ONTIME",
        "width": 90,
        "valueFormatter": {"function": "d3.format('(.1%')(params.value)"},
    },
]
cols_pur_owner = [
    {
        "headerName": "采购",
        "field": "pur",
        "pinned": "left",
        "width": 150,
        "cellClass": "lock-visible-col",
    },
    {"headerName": "缺料笔数", "field": "a", "width": 80},
    {"headerName": "免费样品", "field": "b", "width": 80},
    {"headerName": "下单笔数", "field": "c", "width": 80},
    {"headerName": "项目数", "field": "d", "width": 80},
    {"headerName": "工厂调料", "field": "e", "width": 80},
    {"headerName": "安库库存", "field": "f", "width": 80},
    {
        "headerName": "工作量",
        "field": "loading",
        "width": 80,
        "valueFormatter": {"function": D3_PERCENT_FMT},
    },
    {
        "headerName": "处理时效",
        "field": "tat_proces",
        "width": 80,
        "valueFormatter": {"function": D3_PERCENT_FMT},
    },
    {
        "headerName": "交期维护",
        "field": "tat_lt",
        "width": 80,
        "valueFormatter": {"function": D3_PERCENT_FMT},
    },
    {
        "headerName": "到料时效",
        "field": "tat_ata",
        "width": 80,
        "valueFormatter": {"function": D3_PERCENT_FMT},
    },
    {
        "headerName": "缺料达成",
        "field": "ar_mat",
        "width": 80,
        "valueFormatter": {"function": D3_PERCENT_FMT},
    },
]
cols_pur_dept = [
    {
        "headerName": "部门",
        "field": "dept",
        "pinned": "left",
        "width": 150,
        "cellClass": "lock-visible-col",
    },
    {"headerName": "采购笔数", "field": "pur_count", "width": 80},
    {"headerName": "机构笔数", "field": "me_count", "width": 80},
    {"headerName": "电子笔数", "field": "ee_count", "width": 80},
    {"headerName": "项目数", "field": "prt_count", "width": 80},
    {
        "headerName": "缺料达成率(项目)",
        "field": "ach_proj",
        "width": 110,
        "valueFormatter": {"function": D3_PERCENT_FMT},
    },
    {
        "headerName": "缺料达成率(料号)",
        "field": "ach_mat",
        "width": 110,
        "valueFormatter": {"function": D3_PERCENT_FMT},
    },
    {
        "headerName": "库存达成率(料号)",
        "field": "ach_stock",
        "width": 110,
        "valueFormatter": {"function": D3_PERCENT_FMT},
    },
    {
        "headerName": "BOM及时率",
        "field": "bom",
        "width": 100,
        "valueFormatter": {"function": D3_PERCENT_FMT},
    },
    {"headerName": "材料数", "field": "mat_count", "width": 80},
    {"headerName": "新料数", "field": "new_mat", "width": 80},
    {"headerName": "库存不足", "field": "less_stock", "width": 80},
    {"headerName": "RD提供数", "field": "rd_supply", "width": 100},
    # {"headerName": "安全库存", "field": "safety_stock", "width": 80},
]
cols_pur_vendor = [
    {
        "headerName": "供应商",
        "field": "vendor",
        "pinned": "left",
        "width": 150,
        "cellClass": "lock-visible-col",
    },
    {"headerName": "到货笔数", "field": "received", "width": 80},
    {"headerName": "申请笔数", "field": "apply", "width": 80},
    {"headerName": "平均交期", "field": "lt_mean", "width": 80},
    {"headerName": "下单金额", "field": "total_price", "width": 90},
]

cols_pur_proj = [
    {"headerName": "项目号", "field": "prtno", "pinned": "left", "width": 120},
    {"headerName": "部门", "field": "dept", "width": 100},
    {"headerName": "机种名", "field": "proj", "width": 150},
    {"headerName": "EE", "field": "ee", "width": 100},
    {"headerName": "PM", "field": "pm", "width": 100},
    {"headerName": "QTY", "field": "qty", "width": 80},
    {
        "headerName": "PCB到料",
        "field": "pcb_date",
        "width": 80,
        "valueGetter": {
            "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.pcb_date)"
        },
        "valueFormatter": {"function": "d3.timeFormat('%m/%d')(params.value)"},
    },
    {
        "headerName": "贴片料齐",
        "field": "mat_ready_date",
        "width": 80,
        "valueGetter": {
            "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.mat_ready_date)"
        },
        "valueFormatter": {"function": "d3.timeFormat('%m/%d')(params.value)"},
    },
    {
        "headerName": "插件料齐",
        "field": "dip_mat_ready",
        "width": 80,
        "valueGetter": {
            "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.dip_mat_ready)"
        },
        "valueFormatter": {"function": "d3.timeFormat('%m/%d')(params.value)"},
    },
    {
        "headerName": "样制结束",
        "field": "findate_act",
        "width": 80,
        "valueGetter": {
            "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.findate_act)"
        },
        "valueFormatter": {"function": "d3.timeFormat('%m/%d')(params.value)"},
    },
    {"headerName": "材料数", "field": "mat_count", "width": 80},
    {"headerName": "新料数", "field": "new_stock", "width": 80},
    {"headerName": "库存不足", "field": "less_stock", "width": 80},
    {"headerName": "RD提供", "field": "rd_stock", "width": 80},
    {"headerName": "安全库存", "field": "safety_stock", "width": 80},
    {"headerName": "延迟到料-PCB到板", "field": "gt_pcb", "width": 115},
    {"headerName": "延迟到料-SMD3日", "field": "gt_smd_3d", "width": 115},
]


cols_dept = [
    {
        "headerName": "DEPT",
        "field": "DEPT",
        "pinned": "left",
        "width": 120,
        "cellClass": "lock-visible-col",
    },
    {"headerName": "EC", "field": "SM/ECN", "width": 90},
    {"headerName": "GRP", "field": "GRP CHANGE", "width": 90},
    {"headerName": "NEW ISSUE", "field": "NEW ISSUE", "width": 90},
    {"headerName": "MODIFY ISSUE", "field": "MODIFY ISSUE", "width": 90},
    {"headerName": "PSL", "field": "ES/TS/SN", "width": 90},
    {"headerName": "OTHER PSL", "field": "RC/OTHERS", "width": 90},
    {"headerName": "PN", "field": "PN", "width": 90},
    {"headerName": "SBOM", "field": "SBOM", "width": 90},
    {"headerName": "CBOM", "field": "CBOM", "width": 90},
    {"headerName": "DFMS", "field": "DFMS/CM", "width": 90},
    {"headerName": "DFCS", "field": "DFCS", "width": 90},
    {"headerName": "PART COST", "field": "PART COST", "width": 90},
    {"headerName": "COST QUERY", "field": "COST QUERY", "width": 90},
    {"headerName": "BOM COST", "field": "BOM COST", "width": 90},
    {"headerName": "TOOLA", "field": "TOOLA", "width": 90},
    {"headerName": "TOOLB", "field": "TOOLB", "width": 90},
    {"headerName": "55X", "field": "55X", "width": 90},
    {"headerName": "ME PART", "field": "ME PART", "width": 90},
    {"headerName": "SMBOM", "field": "SMBOM", "width": 90},
    {"headerName": "SMCHANGE", "field": "CHANGE", "width": 90},
    {"headerName": "LOCATION", "field": "LOCATION", "width": 90},
    {"headerName": "SM工作时间", "field": "SMBOM_WKT", "width": 90},
    {
        "headerName": "SM工作LOADING",
        "field": "SMBOM_LOADING",
        "width": 90,
        "valueFormatter": {"function": "d3.format('(.1%')(params.value)"},
    },
    {
        "headerName": "SPEC工作时间",
        "field": "SPEC_WKT",
        "width": 90,
        # "valueFormatter": {"function": "d3.format('(.1%')(params.value)"},
    },
    {
        "headerName": "SPEC工作LOADING",
        "field": "SPEC_LOADING",
        "width": 90,
        "valueFormatter": {"function": "d3.format('(.1%')(params.value)"},
    },
    {"headerName": "总工作时间", "field": "WORK_TIME_MINUTE", "width": 90},
    {
        "headerName": "总工作LOADING",
        "field": "LOADING",
        "width": 90,
        "valueFormatter": {"function": "d3.format('(.1%')(params.value)"},
    },
    {
        "headerName": "ECN会签总时长",
        "field": "ACT_RELEASE_LT",
        "width": 90,
        "valueFormatter": {"function": "d3.format('(.2')(params.value)"},
    },
    {
        "headerName": "ECN会签达成率",
        "field": "RELEASE_ONTIME",
        "width": 90,
        "valueFormatter": {"function": "d3.format('(.1%')(params.value)"},
    },
    {"headerName": "规格数量", "field": "SPEC_QTY", "width": 90},
    {"headerName": "变更单数量", "field": "ECN_QTY", "width": 90},
    {
        "headerName": "SMBOM平均时长",
        "field": "SMBOM_LT_MEAN",
        "width": 90,
        "valueFormatter": {"function": "d3.format('(.1')(params.value)"},
    },
    {
        "headerName": "SMBOM达成率",
        "field": "SMBOM_ONTIME",
        "width": 90,
        "valueFormatter": {"function": "d3.format('(.1%')(params.value)"},
    },
    {
        "headerName": "SMCHANGE平均时长",
        "field": "CHANGE_LT_MEAN",
        "width": 90,
        "valueFormatter": {"function": "d3.format('(.1')(params.value)"},
    },
    {
        "headerName": "SMCHANGE达成率",
        "field": "CHANGE_ONTIME",
        "width": 90,
        "valueFormatter": {"function": "d3.format('(.1%')(params.value)"},
    },
    {"headerName": "费用分摊总数", "field": "TOTAL_APPORTION", "width": 90},
    {
        "headerName": "费用分摊比率",
        "field": "RATIO_APPORTION",
        "width": 90,
        "valueFormatter": {"function": "d3.format('(.1%')(params.value)"},
    },
]


# @callback(
#     Output(id("window-content"), "children"),
#     Input(id("window-size"), "_width"),
#     Input(id("window-size"), "_height"),
#     prevent_initial_call=False,
# )
# def window_size(_width, _height):
#     return f"_width: {_width}  _height: {_height}"
@callback(
    Output(id("dashboard-content"), "children"),
    Input(id("dashboard-month"), "value"),
    Input(id("dashboard-tabs"), "value"),
    State("user", "data"),
    prevent_initial_call=False,
)
def dashboard_content(month, tabs, user):
    kd = get_kpi_date(month)
    children = dashboard_layouts.get(tabs)(kd)
    return children


@callback(
    Output(id("report-content"), "children"),
    Input(id("report-tabs"), "value"),
    Input(id("date_range"), "value"),
    Input(id("kpi_type"), "value"),
    prevent_initial_call=False,
)
def report_content(tabs, date_range, kpi_type):
    if (not date_range) or (not kpi_type):
        raise PreventUpdate

    start_date, end_date = date_range
    start_date = f"{start_date} 00:00:00"
    end_date = f"{end_date} 23:59:59"
    func = report_layouts.get(f"{tabs}-{kpi_type}")
    if not func:
        return fac.AntdResult(status="404")
    return func(start_date, end_date)


@callback(
    Output(id("download"), "data"),
    Input(id("dashboard-download"), "n_clicks"),
    State(id("dashboard-tabs"), "value"),
    Input(id("dashboard-month"), "value"),
)
def download_dashboard_data(n_clicks, tabs, month):
    if not n_clicks:
        raise PreventUpdate
    kd = get_kpi_date(month)
    pur, prt, plant, ss = pur_data(kd.start, kd.end)
    bio = BytesIO()
    with Workbook(bio) as workbook:
        pur.write_excel(workbook, worksheet="pur")
        prt.write_excel(workbook, worksheet="prt")
        ss.write_excel(workbook, worksheet="ss")
    bio.seek(0)
    workbook = bio.read()
    return dcc.send_bytes(workbook, f"{tabs}_{month}月原始数据.xlsx")


@callback(
    Output(id("download"), "data"),
    Input(id("report-table-export"), "nClicks"),
    State(id("report-table"), "rowData"),
    State(id("report-table"), "columnDefs"),
    State(id("date_range"), "value"),
    State(id("report-tabs"), "value"),
    State(id("kpi_type"), "value"),
    running=[
        (Output(id("report-table-export"), "loading"), True, False),
    ],
)
def download_report(n1, table, columns, date_range, tabs, kpi_type):
    if not n1:
        raise PreventUpdate
    df = pd.DataFrame(table)
    columns = {i["field"]: i["headerName"] for i in columns}
    df = df.rename(columns=columns).reindex(columns=columns.values())
    if "ALL" in df.columns:
        df = df.drop("ALL", axis=1)

    df1 = pd.DataFrame()
    if tabs == "SPEC":
        start_date, end_date = date_range
        start_date = f"{start_date} 00:00:00"
        end_date = f"{end_date} 23:59:59"
        sql = "select * from ssp_spec.task where status!=%s and release_date between %s and %s"
        params = ["canceled", start_date, end_date]
        df1 = read_sql(sql, params=params)

    bio = BytesIO()
    with pd.ExcelWriter(bio, engine="xlsxwriter") as writer:
        df.to_excel(writer, sheet_name="Sheet1", index=False)
        df1.to_excel(writer, sheet_name="Sheet2", index=False)
        # writer.save()

    bio.seek(0)
    workbook = bio.read()
    return dcc.send_bytes(workbook, f"{tabs}-{kpi_type}.xlsx")


@callback(
    Output(id("new-modal"), "visible"),
    Output(id("new-modal"), "title"),
    Input(id("new-issue"), "n_clicks"),
)
def new_issue_open_modal(n_clicks):
    if not n_clicks:
        raise PreventUpdate
    set_props(id("description"), {"disabled": False, "value": ""})
    set_props(id("dept"), {"disabled": False, "value": ""})
    set_props(id("role_group"), {"disabled": False, "value": ""})
    set_props(id("approver"), {"disabled": True, "value": ""})
    set_props(id("handler"), {"disabled": True, "value": ""})
    set_props(id("analysis"), {"disabled": True, "value": ""})
    set_props(id("improvement"), {"disabled": True, "value": ""})
    set_props(id("update_spec"), {"disabled": True, "value": ""})
    set_props(id("kpi"), {"disabled": True, "value": ""})
    set_props(id("score"), {"disabled": True, "value": ""})
    set_props(id("accountable"), {"disabled": True, "value": []})
    return True, "发起人"


@callback(
    Output(id("approver"), "value"),
    Input(id("role_group"), "value"),
)
def new_issue_set_approver(unit):
    if not unit:
        raise PreventUpdate
    return approver.get(unit)


@callback(
    Output("msg", "children"),
    Output(id("new-modal"), "visible"),
    Output(id("issue-table"), "rowData"),
    Input(id("new-modal"), "okCounts"),
    State(id("new-modal"), "title"),
    State(id("description"), "value"),
    State(id("dept"), "value"),
    State(id("role_group"), "value"),
    State(id("approver"), "value"),
    State(id("handler"), "value"),
    State(id("analysis"), "value"),
    State(id("improvement"), "value"),
    State(id("update_spec"), "value"),
    State(id("kpi"), "value"),
    State(id("score"), "value"),
    State(id("accountable"), "value"),
    State(id("store"), "data"),
    State("user", "data"),
)
def new_issue_submit(
    ok,
    title,
    description,
    dept,
    unit,
    approver,
    handler,
    analysis,
    improvement,
    update_spec,
    kpi,
    score,
    accountable,
    store,
    user,
):
    if not ok:
        raise PreventUpdate
    nt_name = user.get("nt_name").title()
    closed_date = None

    if title == "发起人":
        if not description or not dept or not unit:
            return (
                fac.AntdMessage(
                    content="问题描述，责任部门，责任单位不能为空", type="error"
                ),
                no_update,
                no_update,
            )
        area, role_group = unit.split("-")
        status = "new"
        if dept != "SUP_SUP":
            status = "closed"
            closed_date = datetime.now()
        db.insert(
            "ssp.issue",
            {
                "description": description,
                "initiator": nt_name,
                "dept": dept,
                "area": area,
                "role_group": role_group,
                "approver": approver,
                "status": status,
                "closed_date": closed_date,
            },
        )
    elif title == "核准人":
        if kpi:
            status = "closed"
            closed_date = datetime.now()
        else:
            if handler:
                if analysis or improvement or update_spec:
                    status = "processing"
                else:
                    status = "open"
            else:
                status = "new"
        db.update(
            "ssp.issue",
            {
                "id": store["id"],
                "handler": handler,
                "analysis": analysis,
                "improvement": improvement,
                "update_spec": update_spec,
                "kpi": kpi,
                "score": score,
                "accountable": ",".join(accountable),
                "status": status,
                "closed_date": closed_date,
            },
        )
    elif title == "处理人":
        if analysis or improvement or update_spec:
            db.update(
                "ssp.issue",
                {
                    "id": store["id"],
                    "analysis": analysis,
                    "improvement": improvement,
                    "update_spec": update_spec,
                    "status": "processing",
                },
            )
    if nt_name.lower() in ("zhen.liu", "weiming.li"):
        sql = "select * from ssp.issue"
        data = db.execute(sql)
    else:
        sql = "select * from ssp.issue \
            where initiator=%s or approver=%s or handler=%s or role_group=%s"
        data = db.execute(sql, [nt_name, nt_name, nt_name, user.get("role_group")])
    return fac.AntdMessage(content="提交成功", type="success"), False, data


@callback(
    Output(id("new-modal"), "visible"),
    Output(id("new-modal"), "title"),
    Input(id("issue-table"), "selectedRows"),
    State("user", "data"),
)
def issue_selected_rows(selected, user):
    if not selected:
        raise PreventUpdate
    d = selected[0]
    d["role_group"] = f"{d['area']}-{d['role_group']}"
    items = [
        "description",
        "dept",
        "role_group",
        "approver",
        "handler",
        "analysis",
        "improvement",
        "update_spec",
        "kpi",
        "score",
        "accountable",
    ]
    d["accountable"] = [i for i in d["accountable"].split(",") if i]

    for i in items:
        set_props(id(i), {"disabled": True, "value": d[i]})

    nt_name = user.get("nt_name").lower()
    status = d["status"]
    role = ""
    if status == "new":
        if d["approver"].lower() == nt_name:
            role = "approver"
        elif d["initiator"].lower() == nt_name:
            role = "initiator"

    elif status == "open":
        if d["handler"].lower() == nt_name:
            role = "handler"
        elif d["approver"].lower() == nt_name:
            role = "approver"
        elif d["initiator"].lower() == nt_name:
            role = "initiator"

    elif status == "processing":
        if d["approver"].lower() == nt_name:
            role = "approver"
        elif d["handler"].lower() == nt_name:
            role = "handler"
        elif d["initiator"].lower() == nt_name:
            role = "initiator"

    elif status == "closed":
        set_props(id("issue-table"), {"deselectAll": True})
        return True, no_update

    if not role:
        set_props(id("issue-table"), {"deselectAll": True})
        return True, no_update

    title = "发起人"
    if role == "handler":
        title = "处理人"
        set_props(id("analysis"), {"disabled": False})
        set_props(id("improvement"), {"disabled": False})
        set_props(id("update_spec"), {"disabled": False})

    elif role == "approver":
        title = "核准人"

        if status == "closed":
            set_props(id("approved"), {"disabled": True})
        elif status == "new":
            set_props(id("handler"), {"disabled": False})
            # set_props(id("approved"), {"disabled": True})
        else:
            set_props(id("analysis"), {"disabled": False})
            set_props(id("improvement"), {"disabled": False})
            set_props(id("update_spec"), {"disabled": False})
            set_props(id("approved"), {"disabled": False, "checked": False})
            set_props(id("kpi"), {"disabled": False})
            set_props(id("score"), {"disabled": False})
            set_props(id("accountable"), {"disabled": False})

    set_props(id("issue-table"), {"deselectAll": True})
    set_props(id("store"), {"data": {"id": d["id"]}})
    return True, title


@callback(
    Output(id("task-store"), "data"),
    Input(id("question"), "nSubmit"),
    Input(id("question"), "nClicksSearch"),
    State(id("question"), "value"),
    running=[
        (Output(id("question"), "disabled"), True, False),
    ],
)
def ai_sql_task_id(n_submit, n_clicks_search, question):
    if not n_submit and not n_clicks_search:
        raise PreventUpdate
    if not question:
        raise PreventUpdate
    task = task_ai_sql(question)
    set_props(id("interval"), {"disabled": False, "n_intervals": 0})
    set_props(
        id("ai-result"),
        {
            "children": fac.AntdResult(
                title="正在执行AI查询，请稍等。。。", status="loading"
            )
        },
    )
    return {"task_id": task.id, "question": question}


@callback(
    Output(id("ai-result"), "children"),
    Input(id("interval"), "n_intervals"),
    State(id("task-store"), "data"),
    State("user", "data"),
)
def ai_sql_result(n_intervals, task_store, user):
    if not task_store:
        raise PreventUpdate
    if n_intervals >= 10:
        set_props(id("interval"), {"disabled": True})
        return fac.AntdResult(title="抱歉，查询超时", status="warning")

    task_id = task_store.get("task_id")
    question = task_store.get("question")
    result = huey_ai.get(task_id)

    if not result:
        raise PreventUpdate
    if len(result) == 1:
        raise PreventUpdate

    sql, data, figure = result
    bg_access_record(user, "ai", "query", f"{question}:\n{sql}")
    set_props(id("interval"), {"disabled": True})
    table = dag.AgGrid(
        id=id("ai-table"),
        className="ag-theme-quartz-dark compact",
        rowData=data,
        columnDefs=[{"field": i, "headerName": i} for i in data[0].keys()],
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        columnSize="autoSize",
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
        },
        style={"height": "400px", "flex": "30%"},
    )
    download = dmc.ActionIcon(
        DashIconify(
            icon="material-symbols:download",
            width=20,
        ),
        id=id("ai-download"),
        color="blue",
    )
    # figure.update_layout(template="clean_v2", showlegend=False)
    # figure.update_layout(showlegend=False)
    graph = dcc.Graph(
        figure=figure,
        config={"displayModeBar": False},
        style={"height": "400px", "flex": "70%"},
    )
    div = fac.AntdFlex(
        [
            graph,
            html.Div(
                [table, download],
                style={"flex": "30%"},
            ),
        ],
        gap=20,
    )
    return div


@callback(
    Output(id("download"), "data"),
    Input(id("ai-download"), "n_clicks"),
    State(id("ai-table"), "rowData"),
    State(id("question"), "value"),
)
def download_ai_data(n_clicks, rowData, question):
    if not n_clicks:
        raise PreventUpdate
    temp_io = BytesIO()
    pd.DataFrame(rowData).to_excel(temp_io, index=False)
    return dcc.send_bytes(temp_io.getvalue(), f"{question}_result.xlsx")


# @callback(
#     Output("chat-component", "messages"),
#     Input("chat-component", "new_message"),
#     State("chat-component", "messages"),
# )
# def handle_chat(new_message, messages):
#     if not new_message:
#         return messages

#     updated_messages = messages + [new_message]

#     if new_message["role"] == "user":
#         response = client.chat.completions.create(
#             model="deepseek-chat",
#             messages=updated_messages,
#             temperature=1.0,
#             max_tokens=150,
#         )

#         bot_response = {
#             "role": "assistant",
#             "content": response.choices[0].message.content.strip(),
#         }
#         return updated_messages + [bot_response]

#     return updated_messages


# clientside_callback(
#     """function (n) {
#         if (n) {
#             dash_ag_grid.getApi("pages-info-sup-layout-owner-report-table").exportDataAsExcel();
#         }
#         return dash_clientside.no_update
#     }""",
#     Output(id("btn1-excel-export"), "n_clicks"),
#     Input(id("btn1-excel-export"), "n_clicks"),
#     prevent_initial_call=True,
# )
