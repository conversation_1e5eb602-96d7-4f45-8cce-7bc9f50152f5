# -*- coding: utf-8 -*-
from datetime import datetime, timedelta

import dash_ag_grid as dag
import dash_mantine_components as dmc
import feffery_antd_components.alias as fac
import numpy as np
import pandas as pd
from dash import no_update
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    Input,
    Output,
    State,
    callback,
    clientside_callback,
    html,
)

from common import get_db, id_factory, parse_search, read_sql
from tasks import bg_label_print, bg_mail, task_import_uuid_to_my300

id = id_factory(__name__)


def get_bulk_data(prtno):
    sql = "select prt_id,bom_id,prtno,designno,deltapn,checkcode,\
        packaging,des,mfgname,mfgpn from smbom where prtno=%s"
    params = [prtno]
    bom = read_sql(sql, params=params)

    params = [prtno]
    sql = "select dept_id,prtno,dept,proj,qty as sets,ee as rd from prt where prtno=%s"
    prt = read_sql(sql, params=params)

    params = [prtno, "sm"]
    sql = "select stock_id,id as stockout_id,qty,checkcode,lable as label,\
        stockno,source,area from stockout where prtno=%s and type=%s"

    stockout = read_sql(sql, params=params)
    stockout["stockout_id"] = stockout["stockout_id"].astype(str)
    stockout["source"] = stockout["source"].replace(
        {"short": "缺料", "stockin": "缺料", "bom": "库存"}
    )
    # ----磁件都出上海库-------
    stockout["area"] = np.where(
        stockout["checkcode"].str.startswith("287"), "SH", stockout["area"]
    )
    c1 = stockout["stockno"].str.match(r"^[A-Z]\d{4}.*", na=False)
    c2 = stockout["stockno"].str.match(r"^\d{7}$", na=False)
    # *卷料
    stockout_roll = stockout.loc[(c1 | c2)]
    # *散料
    stockout_bulk = stockout.loc[stockout.index.difference(stockout_roll.index)]

    # *不在卷料清单的卷料
    params = [f"%{prtno}%"]
    sql = "select stock_id from stock_roll where prtno like %s"
    roll = read_sql(sql, params=params)
    if roll.empty:
        stockout = stockout_bulk
    else:
        stockout_roll_miss = stockout_roll.loc[
            ~stockout_roll["stock_id"].isin(roll["stock_id"])
        ]
        stockout = pd.concat([stockout_roll_miss, stockout_bulk])

    stockout["checked"] = stockout["label"]
    # *qty1为辅助列，打过标签的数量为0
    stockout["qty1"] = np.where(stockout["label"].isna(), stockout["qty"], 0)
    stockout = stockout.groupby(["area", "checkcode"], as_index=False).agg(
        {
            "qty": sum,
            "qty1": sum,
            "checked": all,
            "label": "first",
            "stock_id": "first",
            "stockout_id": lambda x: x.str.cat(sep=","),
            "stockno": lambda x: x.drop_duplicates().str.cat(sep=","),
            "source": lambda x: x.drop_duplicates().str.cat(sep=","),
        }
    )
    stockout["label"] = np.where(stockout["checked"], stockout["label"], "")
    stockout["qty"] = np.where(stockout["checked"], stockout["qty"], stockout["qty1"])

    bulk = bom.groupby(["prtno", "checkcode"], as_index=False).agg(
        {
            "deltapn": "first",
            "packaging": "first",
            "des": "first",
            "mfgname": "first",
            "mfgpn": "first",
            "bom_id": "first",
            "prt_id": "first",
            "designno": lambda x: x.drop_duplicates().sort_values().str.cat(sep=","),
        }
    )

    bulk = bulk.merge(prt, on="prtno", how="left").merge(
        stockout, on="checkcode", how="inner"
    )
    bulk = bulk.loc[bulk["area"] == "SH"]

    bulk = bulk.fillna("").astype(str)

    bulk["qpa"] = np.where(
        bulk["designno"] == "", 0, bulk["designno"].str.count(",") + 1
    )
    bulk["sets"] = bulk["sets"].astype(int)
    bulk["qty"] = np.where(bulk["qty"] == "", bulk["qpa"] * bulk["sets"], bulk["qty"])
    bulk["qty"] = pd.to_numeric(bulk["qty"]).astype(int)
    bulk["type"] = "散料"
    bulk["type"] = np.where(bulk["deltapn"].str.startswith("287"), "mag", bulk["type"])
    bulk["station"] = bulk["packaging"]
    bulk = bulk.sort_values(by=["stockno"])

    return bulk


def get_dip_data(prtno):
    sql = (
        "select prt_id,bom_id,prtno,designno,deltapn,checkcode,packaging,des,mfgname,mfgpn \
    from smbom where prtno=%s and packaging=%s"
    )
    params = [prtno, "dip"]
    bom = read_sql(sql, params=params)

    params = [prtno]
    sql = "select dept_id,prtno,dept,proj,qty as sets,ee as rd from prt where prtno=%s"
    prt = read_sql(sql, params=params)

    params = [prtno, "sm"]
    sql = "select stock_id,id as stockout_id,qty,checkcode,lable as label,\
        stockno,source,area from stockout where prtno=%s and type=%s"

    stockout = read_sql(sql, params=params)
    stockout["stockout_id"] = stockout["stockout_id"].astype(str)
    stockout["source"] = stockout["source"].replace(
        {"short": "缺料", "stockin": "缺料", "bom": "库存"}
    )
    # c1 = stockout["stockno"].str.match(r"^[A-Z]\d{4}.*", na=False)
    # c2 = stockout["stockno"].str.match(r"^\d{7}$", na=False)
    # stockout = stockout.loc[~(c1 | c2)]
    stockout["checked"] = stockout["label"]
    stockout["qty1"] = np.where(stockout["label"].isna(), stockout["qty"], 0)
    stockout = stockout.groupby(["area", "checkcode"], as_index=False).agg(
        {
            "qty": sum,
            "qty1": sum,
            "checked": all,
            "label": "first",
            "stock_id": "first",
            "stockout_id": lambda x: x.str.cat(sep=","),
            "stockno": lambda x: x.drop_duplicates().str.cat(sep=","),
            "source": lambda x: x.drop_duplicates().str.cat(sep=","),
        }
    )
    stockout["label"] = np.where(stockout["checked"], stockout["label"], "")
    stockout["qty"] = np.where(stockout["checked"], stockout["qty"], stockout["qty1"])

    dip = bom.groupby(["prtno", "checkcode"], as_index=False).agg(
        {
            "deltapn": "first",
            "packaging": "first",
            "des": "first",
            "mfgname": "first",
            "mfgpn": "first",
            "bom_id": "first",
            "prt_id": "first",
            "designno": lambda x: x.drop_duplicates().sort_values().str.cat(sep=","),
        }
    )

    dip = dip.merge(prt, on="prtno", how="left").merge(
        stockout, on="checkcode", how="left"
    )

    dip = dip.fillna("").astype(str)

    dip["qpa"] = np.where(dip["designno"] == "", 0, dip["designno"].str.count(",") + 1)
    dip["sets"] = dip["sets"].astype(int)
    dip["qty"] = np.where(dip["qty"] == "", dip["qpa"] * dip["sets"], dip["qty"])
    dip["qty"] = pd.to_numeric(dip["qty"]).astype(int)
    dip["type"] = "插件"
    dip["station"] = "插件"
    dip = dip.sort_values(by=["stockno"])
    return dip


def layout(user: dict, prtno: str, mat: str = "", **kwargs):
    if mat == "dip":
        df = get_dip_data(prtno)
        selectedRows = {"function": "params.data.checkcode != ''"}
        checkboxSelection = True
    else:
        df = get_bulk_data(prtno)
        selectedRows = {"function": "params.data.label == ''"}
        checkboxSelection = {"function": "params.data.stockno !=''"}

    x = (df["label"] == "").sum()
    y = df.shape[0]
    table = dag.AgGrid(
        id=id("table"),
        className="ag-theme-quartz",
        rowStyle={"border": "1px solid black"},
        # selectedRows={"function": "params.data.label == ''"},
        selectedRows=selectedRows,
        columnDefs=[
            {
                "field": "stockno",
                "headerName": "库位号",
                "checkboxSelection": checkboxSelection,
                "headerCheckboxSelection": True,
                "headerCheckboxSelectionFilteredOnly": True,
            },
            {"field": "checkcode", "headerName": "料号"},
            {"field": "designno", "headerName": "位置号"},
            {"field": "qty", "headerName": "数量"},
            {"field": "source", "headerName": "类型"},
            {"field": "label", "headerName": "备料日期"},
        ],
        rowData=df.to_dict("records"),
        # columnSize="sizeToFit",
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        dashGridOptions={
            "rowSelection": "multiple",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
            "domLayout": "print",
            "animateRows": False,
        },
        # style={"page-break-after": "always"},
        # getRowId="params.data.id",
        # style={"height": 500},
    )
    return dmc.Container(
        dmc.Stack(
            [
                dmc.Group(
                    [
                        dmc.Button(
                            "打印标签",
                            id=id("label"),
                            size="xs",
                            variant="outline",
                            color="orange",
                            # disabled=label_disabled,
                        ),
                        dmc.Button(
                            "备料完成",
                            id=id("finish"),
                            size="xs",
                            variant="outline",
                            color="green",
                            # disabled=label_disabled,
                        ),
                        dmc.Button(
                            "库存不足申请",
                            id=id("apply"),
                            size="xs",
                            variant="outline",
                            color="red",
                            # disabled=label_disabled,
                        ),
                        dmc.Button(
                            "打印清单",
                            id=id("print"),
                            size="xs",
                            variant="outline",
                            # disabled=True,
                        ),
                    ],
                    position="apart",
                    align="end",
                ),
                dmc.Divider(),
                fac.Fragment(id=id("fragment")),
                fac.Modal(
                    dmc.NumberInput(id=id("qty")),
                    id=id("modal"),
                    title="请购数量",
                    renderFooter=True,
                    okClickClose=False,
                    centered=True,
                ),
                html.Div(
                    [
                        dmc.Center(
                            f"{prtno}散料清单({x}/{y})",
                            id=id("title"),
                            style={"font-weight": "bold", "font-size": "20px"},
                        ),
                        dmc.LoadingOverlay(table),
                    ],
                    id=id("print-content"),
                ),
            ]
        ),
        fluid=True,
    )


@callback(
    Output(id("fragment"), "children"),
    Output(id("label"), "disabled"),
    Input(id("label"), "n_clicks"),
    State(id("table"), "selectedRows"),
    State("user", "data"),
    State("url", "search"),
)
def print_label(n_clicks, data, user, url):
    if not n_clicks:
        raise PreventUpdate

    if not data:
        return fac.Message(content="请选择需打印数据", type="info"), False

    url = parse_search(url)
    mat = url.get("mat")
    nt_name = user.get("nt_name")

    df = pd.DataFrame(data)
    stockout_id = df["stockout_id"].str.cat(sep=",")
    stockout_id = stockout_id.split(",")

    db = get_db()
    sql = "update ssp.stockout set lable=now(),stockoutdate2=now(),owner2=%s where id in %s"
    params = [nt_name, stockout_id]
    db.execute(sql, params)

    # 打印散料时,磁件出库发邮件通知
    if mat == "bulk":
        dfm = df.loc[df["type"] == "mag"]
        if not dfm.empty:
            to = f"<EMAIL>;<EMAIL>;{nt_name}@deltaww.com"
            subject = f"{dfm['prtno'].iloc[0]}磁件出库通知"
            bg_mail(to, subject, dfm.to_html())
            dfm["type"] = "散料"
            df = pd.concat([df, dfm], ignore_index=True)

    df["label_template"] = "stock_out"
    df["owner"] = nt_name
    df = df.reset_index(drop=True)
    df["id"] = df.index + int(f"{datetime.now():%y%m%d%H%M%S%f}")
    df["id"] = "b" + df["id"].astype(str)
    bg_label_print(df.to_json(orient="records"))
    task_import_uuid_to_my300(df)

    return fac.Message(content="打印提交成功", type="success"), True


@callback(
    Output(id("fragment"), "children"),
    Output(id("finish"), "disabled"),
    Input(id("finish"), "n_clicks"),
    State("url", "search"),
    State("user", "data"),
)
def finish(n_clicks, url, user):
    if not n_clicks:
        raise PreventUpdate
    url = parse_search(url)
    prtno = url.get("prtno")
    mat = url.get("mat")
    nt_name = user.get("nt_name")

    db = get_db()
    sql = "update ssp.stockout set lable=now(),stockoutdate2=now(),owner2=%s \
        where prtno=%s and stockoutdate2 is null"
    params = [nt_name, prtno]
    db.execute(sql, params)

    if mat == "dip":
        sql = "update ssp.prt set mat_dip_date=now() where prtno=%s"
        params = [prtno]
    else:
        sql = "update ssp.prt set matstatus='Mat1OK',mat1_date=now(),mat_dip_date=now() where prtno=%s"
        params = [prtno]
    db.execute(sql, params)

    return fac.Message(content="提交成功", type="success"), True


@callback(
    Output(id("fragment"), "children"),
    Output(id("modal"), "visible"),
    Input(id("apply"), "n_clicks"),
    State(id("table"), "selectedRows"),
)
def modal_visible(n_clicks, data):
    if not n_clicks:
        raise PreventUpdate
    if not data:
        return fac.Message(content="请先选择记录", type="error"), False
    if len(data) > 1:
        return fac.Message(content="一次只能操作一条记录", type="error"), False
    return no_update, True


@callback(
    Output(id("fragment"), "children"),
    Output(id("modal"), "visible"),
    Output(id("apply"), "disabled"),
    Input(id("modal"), "okCounts"),
    State(id("table"), "selectedRows"),
    State(id("qty"), "value"),
)
def apply_purchase(ok, data, qty):
    if not ok:
        raise PreventUpdate
    if not qty:
        return fac.Message(content="请填写数量", type="error"), True, no_update
    data = data[0]

    db = get_db()
    # !--------------要修改，按照checkcode group by------------
    sql = "select a.*,b.des,b.mfgname,b.mfgpn,c.proj,c.ee from ssp.stockout a \
        left join ssp.stock b on a.stock_id=b.id \
        left join ssp.prt c on a.prtno=c.prtno \
        where a.checkcode=%s and a.prtno!=%s and a.qty>%s and lable is null"
    params = [data["checkcode"], data["prtno"], 0]
    others = db.execute(sql, params=params)
    if others:
        for i in others:
            now = datetime.now()
            item_pur = f"{now:%y%m%d%H%M%S%f}"
            db.insert(
                "ssp.pur",
                {
                    "prtno": i["prtno"],
                    "deltapn": i["deltapn"],
                    "checkcode": i["checkcode"],
                    "qty": i["qty"],
                    "application": "project",
                    "item_pur": item_pur,
                    "dept": i["dept"],
                    "des": i["des"],
                    "mfgname": i["mfgname"],
                    "mfgpn": i["mfgpn"],
                    "start_date": now,
                    "pur_status": "SA",
                    "mat_remark": "库存不足",
                    "req_date": now + timedelta(days=30),
                    "dept_id": i["dept_id"],
                    "dest_area": i["area"],
                    "prt_id": i["prt_id"],
                    "bom_id": i["bom_id"],
                    "proj": i["proj"],
                    "rd": i["ee"],
                },
            )
            db.update("ssp.stockout", {"qty": 0, "id": i["id"]})
            db.commit()

    now = datetime.now()
    db.insert(
        "ssp.pur",
        {
            "prtno": data["prtno"],
            "deltapn": data["deltapn"],
            "checkcode": data["checkcode"],
            "qty": qty,
            "application": "project",
            "item_pur": f"{now:%y%m%d%H%M%S%f}",
            "dept": data["dept"],
            "des": data["des"],
            "mfgname": data["mfgname"],
            "mfgpn": data["mfgpn"],
            "start_date": now,
            "pur_status": "SA",
            "mat_remark": "库存不足",
            "req_date": now + timedelta(days=30),
            "dept_id": data["dept_id"],
            "dest_area": data["area"],
            "prt_id": data["prt_id"],
            "bom_id": data["bom_id"],
            "proj": data["proj"],
            "rd": data["rd"],
        },
    )
    sql = "update ssp.stockout set qty=qty-%s where id=%s"
    params = [qty, data["stockout_id"]]
    db.execute(sql, params)

    db.update("ssp.stock", {"qty": 0, "id": data["stock_id"]})
    return fac.Message(content="缺料已申请", type="success"), False, True


clientside_callback(
    """
    function (n) {
    if (n>0) {
         var printContents = document.getElementById('pages-stock-bulk-layout-print-content').innerHTML;
         var originalContents = document.body.innerHTML;

         document.body.innerHTML = printContents;

         window.print();

         document.body.innerHTML = originalContents;
         location.reload()

        return window.dash_clientside.no_update
        }
    }
    """,
    Input(id("print"), "n_clicks"),
    # prevent_initial_call=True,
)
# @callback(
#     Output(id("fragment"), "children"),
#     Input(id("apply"), "n_clicks"),
#     State(id("table"), "selectedRows"),
# )
# def apply(n_clicks, rows):
#     if not n_clicks:
#         raise PreventUpdate
#     if not rows:
#         return fac.Message(content="请先选择记录", type="error")
#     print(rows)

#     db = get_db()
#     sql = "update ssp.prt set mat_dip_date=now() where prtno=%s"
#     params = [prtno]
#     db.execute(sql, params)
#     return fac.Message(content="提交成功", type="success")
