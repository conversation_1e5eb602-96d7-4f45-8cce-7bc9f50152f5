# -*- coding: utf-8 -*-
import dash_bootstrap_components as dbc
from dash import dcc, html, dash_table
from dash_extensions.javascript import Namespace  # 占位符1
from dash_tabulator import DashTabulator
import dash_mantine_components as dmc
import dash_bootstrap_components as dbc
from common import id_factory
from components.file_browser import file_browser
from config import SSP_DIR
import feffery_antd_components as fac

id = id_factory(__name__)

ns = Namespace("myNamespace", "tabulator")

# style = {
#     "border": f"1px solid {dmc.theme.DEFAULT_COLORS['indigo'][4]}",
#     "textAlign": "center",
# }


tab1_submit_btn = dbc.<PERSON><PERSON>(
    "提交",
    color="success",
    size="sm",
    style={"width": "70px"},
    id=id("tab1_submit_btn"),
)
tab1_dowload_btn = dbc.<PERSON><PERSON>(
    "下载",
    color="danger",
    size="sm",
    style={"width": "70px"},
    id=id("tab1_dowload_btn"),
)
tab2_dowload_btn = dbc.<PERSON><PERSON>(
    "下载",
    color="danger",
    size="sm",
    style={"width": "70px"},
    id=id("tab2_dowload_btn"),
)
tab1_select1 = fac.AntdSelect(
    placeholder="请选择BOM类型",
    id=id("tab1_select1"),
    options=[
        {"label": "Single BOM", "value": "single"},
        {"label": "Multiple BOM", "value": "multiple"},
    ],
    style={
        # 使用css样式固定宽度
        "width": "300px",
        "color": "red",
    },
)
tab1_select2 = fac.AntdSelect(
    placeholder="请选择需忽略的差异：",
    id=id("tab1_select2"),
    mode="multiple",
    value=[],
    options=[
        {"label": "忽略温度特性差异", "value": "temperature"},
        {"label": "忽略抗硫和非抗硫差异", "value": "antis"},
        {"label": "忽略电容高度差异", "value": "height"},
    ],
    style={
        # 使用css样式固定宽度
        "width": "500px",
        "color": "red",
    },
)
tab2_submit_btn = dbc.Button(
    "提交",
    color="success",
    size="sm",
    style={"width": "70px"},
    id=id("tab2_submit_btn"),
)
tab2_select = fac.AntdSelect(
    placeholder="请选择需忽略的差异：",
    id=id("tab2_select"),
    options=[
        {"label": "忽略温度特性差异", "value": "temperature"},
        {"label": "忽略抗硫和非抗硫差异", "value": "antis"},
        {"label": "忽略电容高度差异", "value": "height"},
    ],
    style={
        # 使用css样式固定宽度
        "width": "300px",
        "color": "red",
    },
)
tab1_table = DashTabulator(
    id=id("tab1_table"),
    theme="tabulator_site",
    downloadButtonType={
        "css": "btn btn-sm btn-outline-dark",
        "text": "Export",
        "type": "xlsx",
    },
    clearFilterButtonType={
        "css": "btn btn-sm btn-outline-dark",
        "text": "Clear Filters",
    },
    options={
        "layout": "fitDataStretch",
        "height": "390px",
        "selectable": True,
        "groupBy": ["error"],
        "groupStartOpen": False,
        "dataTreeElementColumn": "design_no",
        "dataTreeStartExpanded": True,
    },
    columns=[
        {
            "title": "ERROR",
            "editor": "input",
            "field": "error",
            "headerFilter": "input",
        },
        {
            "title": "BOM NAME",
            "editor": "input",
            "field": "bom_name",
            "headerFilter": "input",
        },
        {
            "title": "PARENT LEVEL",
            "editor": "input",
            "field": "parent_level",
            "headerFilter": "input",
        },
        {
            "title": "DESIGN NO",
            "field": "design_no",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "DELTAPN",
            "field": "deltapn",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "DES",
            "field": "des",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "MFGNAME",
            "field": "mfgname",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "MFGPN",
            "field": "mfgpn",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "ALT",
            "field": "alt",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "GRP",
            "field": "grp",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "QPA",
            "field": "qpa",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "UM",
            "field": "um",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "ITEM TEXT",
            "field": "item_text",
            "editor": "input",
            "headerFilter": "input",
        },
    ],
)
tab2_table = DashTabulator(
    id=id("tab2_table"),
    theme="tabulator_site",
    downloadButtonType={
        "css": "btn btn-sm btn-outline-dark",
        "text": "Export",
        "type": "xlsx",
    },
    clearFilterButtonType={
        "css": "btn btn-sm btn-outline-dark",
        "text": "Clear Filters",
    },
    options={
        "layout": "fitDataStretch",
        "height": "390px",
        "selectable": True,
        "groupBy": ["error"],
        "groupStartOpen": False,
        "dataTree": True,
        "dataTreeElementColumn": "design_no",
        "dataTreeStartExpanded": True,
    },
    columns=[
        {
            "title": "ERROR",
            "field": "error",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "DESIGN NO",
            "field": "design_no",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "BOM NAME",
            "field": "bom_name",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "PARENT LEVEL",
            "field": "parent_level",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "DELTAPN",
            "field": "deltapn",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "DES",
            "field": "des",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "MFGNAME",
            "field": "mfgname",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "MFGPN",
            "field": "mfgpn",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "ALT",
            "field": "alt",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "GRP",
            "field": "grp",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "QPA",
            "field": "qpa",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "UM",
            "field": "um",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "ITEM TEXT",
            "field": "item_text",
            "editor": "input",
            "headerFilter": "input",
        },
    ],
)

tab1_Form = html.Div(
    [
        dmc.SimpleGrid(
            cols=4,
            spacing="xs",
            children=[
                html.Div(
                    dmc.TextInput(
                        placeholder="PARENT LEVEL", size="xs", style={"width": 100}
                    ),
                ),
                html.Div(
                    dmc.TextInput(
                        placeholder="DELTAPN", size="xs", style={"width": 100}
                    ),
                ),
                html.Div(
                    dmc.TextInput(placeholder="DES", size="xs", style={"width": 100}),
                ),
                html.Div(
                    dmc.TextInput(
                        placeholder="MFGNAME", size="xs", style={"width": 105}
                    ),
                ),
                html.Div(
                    dmc.TextInput(placeholder="MFGPN", size="xs", style={"width": 100}),
                ),
                html.Div(
                    dmc.TextInput(placeholder="ALT", size="xs", style={"width": 100}),
                ),
                html.Div(
                    dmc.TextInput(placeholder="GRP", size="xs", style={"width": 100}),
                ),
                html.Div(
                    dmc.TextInput(placeholder="QPA", size="xs", style={"width": 100}),
                ),
                html.Div(
                    dmc.TextInput(placeholder="UM", size="xs", style={"width": 100}),
                ),
                html.Div(
                    dmc.TextInput(
                        placeholder="DESIGN NO", size="xs", style={"width": 100}
                    ),
                ),
                html.Div(
                    dmc.TextInput(
                        placeholder="ITEM TEXT", size="xs", style={"width": 100}
                    ),
                ),
            ],
        )
    ],
    style={
        "border-width": "10x",
        "border-color": "rgb(233, 236, 239)",
        "border-style": "outset",
        "width": "600px",
        "padding": "2px",
        "margin-top": "10px",
    },
)
tab2_Form1 = html.Div(
    [
        dmc.SimpleGrid(
            cols=4,
            spacing="xs",
            children=[
                html.Div(
                    dmc.TextInput(
                        placeholder="PARENT LEVEL", size="xs", style={"width": 100}
                    ),
                ),
                html.Div(
                    dmc.TextInput(
                        placeholder="DELTAPN", size="xs", style={"width": 100}
                    ),
                ),
                html.Div(
                    dmc.TextInput(placeholder="DES", size="xs", style={"width": 100}),
                ),
                html.Div(
                    dmc.TextInput(
                        placeholder="MFGNAME", size="xs", style={"width": 105}
                    ),
                ),
                html.Div(
                    dmc.TextInput(placeholder="MFGPN", size="xs", style={"width": 100}),
                ),
                html.Div(
                    dmc.TextInput(placeholder="ALT", size="xs", style={"width": 100}),
                ),
                html.Div(
                    dmc.TextInput(placeholder="GRP", size="xs", style={"width": 100}),
                ),
                html.Div(
                    dmc.TextInput(placeholder="QPA", size="xs", style={"width": 100}),
                ),
                html.Div(
                    dmc.TextInput(placeholder="UM", size="xs", style={"width": 100}),
                ),
                html.Div(
                    dmc.TextInput(
                        placeholder="DESIGN NO", size="xs", style={"width": 100}
                    ),
                ),
                html.Div(
                    dmc.TextInput(
                        placeholder="ITEM TEXT", size="xs", style={"width": 100}
                    ),
                ),
            ],
        )
    ],
    style={
        "border-width": "10x",
        "border-color": "rgb(233, 236, 239)",
        "border-style": "outset",
        "width": "600px",
        "padding": "2px",
        "margin-top": "10px",
    },
)
tab2_Form2 = html.Div(
    [
        dmc.SimpleGrid(
            cols=4,
            spacing="xs",
            children=[
                html.Div(
                    dmc.TextInput(
                        placeholder="PARENT LEVEL", size="xs", style={"width": 100}
                    ),
                ),
                html.Div(
                    dmc.TextInput(
                        placeholder="DELTAPN", size="xs", style={"width": 100}
                    ),
                ),
                html.Div(
                    dmc.TextInput(placeholder="DES", size="xs", style={"width": 100}),
                ),
                html.Div(
                    dmc.TextInput(
                        placeholder="MFGNAME", size="xs", style={"width": 105}
                    ),
                ),
                html.Div(
                    dmc.TextInput(placeholder="MFGPN", size="xs", style={"width": 100}),
                ),
                html.Div(
                    dmc.TextInput(placeholder="ALT", size="xs", style={"width": 100}),
                ),
                html.Div(
                    dmc.TextInput(placeholder="GRP", size="xs", style={"width": 100}),
                ),
                html.Div(
                    dmc.TextInput(placeholder="QPA", size="xs", style={"width": 100}),
                ),
                html.Div(
                    dmc.TextInput(placeholder="UM", size="xs", style={"width": 100}),
                ),
                html.Div(
                    dmc.TextInput(
                        placeholder="DESIGN NO", size="xs", style={"width": 100}
                    ),
                ),
                html.Div(
                    dmc.TextInput(
                        placeholder="ITEM TEXT", size="xs", style={"width": 100}
                    ),
                ),
            ],
        )
    ],
    style={
        "border-width": "10x",
        "border-color": "rgb(233, 236, 239)",
        "border-style": "outset",
        "width": "600px",
        "padding": "2px",
        "margin-top": "10px",
    },
)

tab1_content = html.Div(
    [
        dbc.Row(
            [
                dbc.Col(tab1_submit_btn, width=1),
                dbc.Col(tab1_dowload_btn, width=1),
                dbc.Col(tab1_select1, width=3),
                dbc.Col(tab1_select2, width=3),
            ]
        ),
        dbc.Row(
            [
                dbc.Col(
                    dbc.Row(
                        dmc.Skeleton(
                            fac.AntdDraggerUpload(
                                id=id("file1"),
                                apiUrl="/upload/",
                                # fileMaxSize=1,
                                fileListMaxLength=1,
                                text="上传BOM",
                                showUploadList=False,
                                # hint="点击或拖拽文件至此处进行上传",
                            ),
                            id=id({"type": "skeleton", "index": 0}),
                            animate=False,
                        ),
                        style={
                            "width": 600,
                            "height": 120,
                            "margin-top": "10px",
                        },
                    ),
                ),
                dbc.Col(
                    dmc.Skeleton(
                        tab1_Form,
                        animate=False,
                        id=id({"type": "skeleton", "index": 1}),
                    )
                ),
            ]
        ),
        html.Br(),
        dcc.Loading(dmc.Skeleton(
            tab1_table, animate=False, id=id({"type": "skeleton", "index": 2})
        )),
    ]
)
tab2_content = html.Div(
    [
        dbc.Row(
            [
                dbc.Col(tab2_submit_btn, width=1),
                dbc.Col(tab2_dowload_btn, width=1),
                dbc.Col(tab2_select),
            ]
        ),
        dbc.Row(
            [
                dbc.Col(
                    fac.AntdDraggerUpload(
                        id=id("file2"),
                        apiUrl="/upload/",
                        fileMaxSize=1,
                        fileListMaxLength=1,
                        text="上传BOM1",
                        showUploadList=False,
                        # hint="点击或拖拽文件至此处进行上传",
                        style={
                            "width": 600,
                            "height": 120,
                            "margin-top": "10px",
                        },
                    ),
                ),
                dbc.Col(
                    fac.AntdDraggerUpload(
                        id=id("file3"),
                        apiUrl="/upload/",
                        fileMaxSize=1,
                        fileListMaxLength=1,
                        showUploadList=False,
                        text="上传BOM2或PCAD",
                        # hint="点击或拖拽文件至此处进行上传",
                        style={
                            "width": 600,
                            "height": 120,
                            "margin-top": "10px",
                        },
                    ),
                ),
            ],
        ),
        dbc.Row(
            [
                dbc.Col(tab2_Form1),
                dbc.Col(tab2_Form2),
            ]
        ),
        tab2_table,
    ],
)
tab3_content = file_browser(SSP_DIR / "program" / "DOC" / "BOM", __name__)


def layout(**kwargs):
    layout = dbc.Container(
        [
            fac.AntdTabs(
                [
                    fac.AntdTabPane(tab1_content, tab="BOM自检", key="1"),
                    fac.AntdTabPane(tab2_content, tab="BOM比对", key="2"),
                    fac.AntdTabPane(tab3_content, tab="文档", key="文档"),
                ],
                id=id("tabs"),
                activeKey="1",
            ),
            html.Div(id=id("notice")),
        ],
        fluid=True,
        # className="ml-3 pr-5",
    )
    return layout
