# -*- coding: utf-8 -*-
import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
import feffery_antd_components as fac
import numpy as np
import pandas as pd
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import Input, Output, State, callback, dash_table, html
from datetime import datetime

from common import (
    add_mat_category,
    df_add_ce_owner,
    dropdown_conditional,
    get_nt_name,
    get_ssp_user,
    id_factory,
    material_information_complete,
    df_to_html,
)
from components.notice import notice
from utils import db

# from dbtool import db
from tasks import bg_mail

id = id_factory(__name__)

tab_add_row = dbc.Button(
    className="fa fa-plus",
    size="sm",
    color="light",
    id=id("tab_add_row"),
)

columns = [
    {"name": "台达料号", "id": "deltapn", "presentation": "input"},
    {"name": "描述", "id": "des", "presentation": "input"},
    {"name": "厂商", "id": "mfgname", "presentation": "input"},
    {"name": "厂商型号", "id": "mfgpn", "presentation": "input"},
    {"name": "材料类型1", "id": "cat1", "presentation": "dropdown"},
    {"name": "材料类型2", "id": "cat2", "presentation": "dropdown"},
    {"name": "材料类型3", "id": "cat3", "presentation": "dropdown"},
    {"name": "变更内容", "id": "change_content", "presentation": "input"},
    # {"name": "最新的规格文件(附件)", "id": "attachment", "presentation": "input"},
]

table = dash_table.DataTable(
    data=[{}],
    columns=columns,
    editable=True,
    row_deletable=True,
    is_focused=True,
    id=id("table"),
    style_cell={
        "whiteSpace": "normal",
        "height": "auto",
        "textAlign": "left",
        "font-family": "Helvetica",
        "font-size": "10px",
    },
    style_cell_conditional=[
        {"if": {"column_id": "change_content"}, "width": "150px"},
        {"if": {"column_id": "cat1"}, "width": "150px"},
        {"if": {"column_id": "cat2"}, "width": "150px"},
        {"if": {"column_id": "cat3"}, "width": "150px"},
    ],
    css=[{"selector": ".dash-spreadsheet-menu-item", "rule": "display:none"}],
    dropdown_conditional=dropdown_conditional(),
)

title = dmc.Center(dmc.Text("规格书更新申请单",fw=700,id=id("title")))

attachment = fac.AntdDraggerUpload(
    apiUrl="/upload/",
    text="最新的规格文件",
    id=id("attachment"),
    lastUploadTaskRecord={},
    # style={"width": "455px"},
)


submit = dmc.Button("提交", id=id("rd-submit"))


def layout(**kwargs):
    nt_name = get_nt_name()
    users = get_ssp_user()
    user = users.loc[users["nt_name"] == nt_name]
    role_group = user["role_group"].iloc[0]
    if role_group == "CE":
        applicant_disabled = False
    else:
        applicant_disabled = True
    applicant = dmc.Select(
        label="申请人(Applicant)",
        withAsterisk=True,
        size="xs",
        id=id("applicant"),
        placeholder="申请人(Applicant)",
        value=nt_name,
        data=users["nt_name"].tolist(),
        disabled=applicant_disabled,
        searchable=True,
        style={"width": 150},
    )
    return dmc.Container(
        dmc.Stack(
            [
                title,
                dmc.Divider(),
                applicant,
                html.Div([table, tab_add_row]),
                attachment,
                submit,
            ]
        )
    )


@callback(
    Output(id("table"), "data"),
    Input(id("tab_add_row"), "n_clicks"),
    State(id("table"), "data"),
)
def tab_table_add_row(n_clicks, data):
    """create_meeting_table插入行"""
    if n_clicks:
        data.append({})
        return data
    else:
        raise PreventUpdate


@callback(
    Output(id("table"), "data"),
    Input(id("table"), "data_timestamp"),
    State(id("table"), "active_cell"),
    State(id("table"), "data"),
    State(id("table"), "columns"),
)
def update_table_when_pasting_data(data_timestamp, active_cell, data, columns):
    if not data_timestamp:
        raise PreventUpdate

    column_id = active_cell.get("column_id") if active_cell else None
    if column_id not in ("deltapn", "mfgpn", "des"):
        raise PreventUpdate
    column_id = "mfgpn" if column_id == "des" else column_id

    df = pd.DataFrame(data)
    cols = [i.get("id") for i in columns]
    df = df.reindex(columns=cols)

    df = material_information_complete(df, column_id)
    df["deltapn"] = np.where(df["deltapn"] == "", "NEWPART", df["deltapn"])
    df["mfgpn"] = np.where(df["mfgpn"] == "", "NA", df["mfgpn"])
    df = add_mat_category(df)
    return df.to_dict(orient="records")


@callback(
    Output("global-notice", "children"),
    Output(id("rd-submit"), "disabled"),
    Input(id("rd-submit"), "n_clicks"),
    State(id("table"), "data"),
    State(id("attachment"), "lastUploadTaskRecord"),
    State(id("applicant"), "value"),
    State(id("title"), "children"),
)
def rd_submit(n_clicks, data, attachment, applicant, title):
    if not n_clicks:
        raise PreventUpdate

    df = pd.DataFrame(data)
    df = df.replace({"": None})
    # --------材料类型必填---------

    df = df.dropna(how="all")
    if df.empty:
        return notice("请填写数据", "error"), False

    if df[["cat1", "cat2", "cat3", "change_content"]].isna().any().any():
        return notice("材料类型和变更内容必填", "error"), False

    if not attachment:
        return notice("请上传附件", "warning"), False

    user = db.find_one("ssp.user", {"nt_name": applicant})
    dept = user.get("dept")
    dept_id = user.get("dept_id")
    sub_type = title[:-3]

    attach = attachment.get("taskId")
    cc = ["Ru.Xue", "Ying.Gao"]
    if dept_id in (4, 5, 22):
        cc += ["Yuhan.Wang"]

    df = df_add_ce_owner(df)
    for item in df.itertuples():
        ce = item.owner_ce
        task = {
            "type": "料号申请",
            "sub_type": sub_type,
            "applicant": applicant,
            "ce": item.owner_ce,
            "status": "open",
            "urgent": "一般",
            "dept": dept,
            "cat1": item.cat1,
            "cat2": item.cat2,
            "cat3": item.cat3,
            "deltapn": item.deltapn,
            "des": item.des,
            "mfgpn": item.mfgpn,
            "mfgname": item.mfgname,
            "dept_id": dept_id,
            "cc": ",".join(cc),
            "start_date": datetime.now().date(),
        }
        task_id = db.insert("ce.task", task)

        x = {
            "task_id": task_id,
            "deltapn": item.deltapn,
            "des": item.des,
            "mfgname": item.mfgname,
            "mfgpn": item.mfgpn,
            "cat1": item.cat1,
            "cat2": item.cat2,
            "cat3": item.cat3,
            "change_content": item.change_content,
            "attachment": attach,
        }
        db.insert("ce.pn_spec", x)

        # *---------邮件通知开始-----------*
        to = [applicant, ce] + cc
        to = ";".join(f"{i}@deltaww.com" for i in to)
        subject = f"【料号申请】{sub_type}"
        df = pd.DataFrame([task])
        columns = [
            "type",
            "dept",
            "applicant",
            "deltapn",
            "mfgname",
            "mfgpn",
            "start_date",
            "ce",
        ]
        df = df.reindex(columns=columns)
        content = df_to_html(
            df,
            f"您的{sub_type}已提交,{ce}将会处理,请知悉！",
            href="http://sup.deltaww.com/info/rd?page=ongoing",
            link_text="工作进展可至个人中心查询",
        )
        bg_mail(to, subject, content)
        # *---------邮件通知结束-----------*

    return notice("提交成功"), True
