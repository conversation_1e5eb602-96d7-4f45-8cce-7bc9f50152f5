# -*- coding: utf-8 -*-
from urllib.parse import parse_qsl, urlsplit

import dash_bootstrap_components as dbc
import numpy as np
import pandas as pd
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    Input,
    Output,
    callback,
    dash,
    dash_table,
    html,
)

from common import read_sql

dash.register_page(__name__, title="个人中心")

page_shortage = html.Div(
    [
        html.Div(
            [
                html.Span(
                    [
                        html.B([html.B()]),
                        # html.P(),
                        html.Em("", id="my-step-a"),
                        html.Div("PWB状态"),
                    ],
                    className="s-step",
                    id="my-step-1",
                ),
                html.Span(
                    [
                        html.B([html.B()]),
                        # html.P(),
                        html.Em("", id="my-step-b"),
                        html.Div("缺料采购"),
                    ],
                    className="s-step",
                    id="my-step-2",
                ),
                html.Span(
                    [
                        html.B([html.B()]),
                        html.P(className="active"),
                        html.Em("", id="my-step-c"),
                        html.Div("计划阶段"),
                    ],
                    className="s-done",
                    id="my-step-3",
                ),
                html.Span(
                    [
                        html.B([html.B()]),
                        html.P(),
                        html.Em("", id="my-step-d"),
                        html.Div("贴片制作"),
                    ],
                    className="s-done",
                    id="my-step-4",
                ),
                html.Span(
                    [
                        html.B([html.B()]),
                        html.P(),
                        html.Em("", id="my-step-e"),
                        html.Div("插件制作"),
                    ],
                    className="s-process",
                    id="my-step-5",
                ),
                html.Span(
                    [
                        html.B([html.B()]),
                        html.P(),
                        html.Em("", id="my-step-f"),
                        html.Div("首样提供"),
                    ],
                    className="s-step",
                    id="my-step-6",
                ),
                html.Span(
                    [
                        html.B([html.B()]),
                        html.Em("", id="my-step-g"),
                        html.Div("样品完成"),
                    ],
                    className="s-step",
                    id="my-step-7",
                ),
            ],
            className="order_status",
            id="my-project-status",
        ),
        html.Div(
            dash_table.DataTable(
                id="my-tablea",
                columns=[
                    {"name": ["项目名"], "id": "proj", "presentation": "markdown"},
                    {"name": ["阶段"], "id": "stage"},
                    {"name": ["项目号"], "id": "prtno"},
                    {"name": ["PCB料号"], "id": "pcbpn"},
                    {"name": ["首样计划时间"], "id": "fsdate_sch"},
                    {"name": ["开始时间"], "id": "smtstadate"},
                    {"name": ["数量"], "id": "qty"},
                    {"name": ["PCB状况"], "id": "pcbstatus"},
                    {"name": ["id"], "id": "id"},
                    {"name": ["smstatus"], "id": "smstatus"},
                ],
                merge_duplicate_headers=True,
                hidden_columns=["id", "smtstadate", "fsdate_sch"],
                markdown_options={"link_target": "_blank"},
                style_cell={
                    "font-family": "Helvetica",
                    "font-size": "12px",
                    "textAlign": "left",
                },
                # style_data={
                #     "whiteSpace": "normal",
                #     # "height": "auto",
                #     # "lineHeight": "15px",
                # },
                style_header={
                    "backgroundColor": "rgb(52, 73, 94)",
                    "color": "white",
                    "fontWeight": "bold",
                    "overflow": "hidden",
                },
                css=[
                    {"selector": ".dash-spreadsheet-menu-item", "rule": "display:none"},
                    {"selector": ".cell-markdown p", "rule": "margin:0"},
                    {"selector": ".cell-markdown", "rule": "padding-bottom:2px"},
                    {"selector": ".cell-markdown p a", "rule": "padding:0"},
                ],
            ),
            className="my-width",
        ),
        html.Div(
            dash_table.DataTable(
                id="my-tableb",
                columns=[
                    {"name": ["PM"], "id": "pm", "presentation": "markdown"},
                    {"name": ["EE"], "id": "ee", "presentation": "markdown"},
                    {"name": ["ME"], "id": "me", "presentation": "markdown"},
                    {"name": ["MAG"], "id": "mag", "presentation": "markdown"},
                    {"name": ["Layout"], "id": "layout", "presentation": "markdown"},
                    {"name": ["FCE"], "id": "fce", "presentation": "markdown"},
                    {"name": ["id"], "id": "id", "presentation": "markdown"},
                ],
                merge_duplicate_headers=True,
                hidden_columns=["id"],
                markdown_options={"link_target": "_blank"},
                style_cell={
                    "font-family": "Helvetica",
                    "font-size": "12px",
                    "textAlign": "left",
                },
                style_header={
                    "backgroundColor": "rgb(52, 73, 94)",
                    "color": "white",
                    "fontWeight": "bold",
                    "overflow": "hidden",
                },
                css=[
                    {"selector": ".dash-spreadsheet-menu-item", "rule": "display:none"},
                    {"selector": ".cell-markdown p", "rule": "margin:0"},
                    {"selector": ".cell-markdown", "rule": "padding-bottom:2px"},
                    {"selector": ".cell-markdown p a", "rule": "padding:0"},
                ],
                # cell-markdown
            ),
            className="my-width pt-2",
        ),
        html.Div(
            [
                dash_table.DataTable(
                    id="my-tablec",
                    columns=[
                        {"name": ["料号"], "id": "deltapn"},
                        {"name": ["描述"], "id": "des"},
                        {"name": ["厂商"], "id": "mfgname"},
                        {"name": ["厂商料号"], "id": "mfgpn"},
                        {"name": ["缺料数量"], "id": "qty"},
                        {"name": ["需求日"], "id": "req_date"},
                        {"name": ["预计交期"], "id": "es_date_merge"},
                        {"name": ["状态"], "id": "pur_status"},
                        {"name": ["采购备注"], "id": "pur_remark"},
                        {"name": ["物料备注"], "id": "mat_remark"},
                        {"name": ["采购"], "id": "pur"},
                        {"name": ["id"], "id": "id"},
                        {"name": ["id"], "id": "es_date"},
                        {"name": ["id"], "id": "es_date_lasttime"},
                    ],
                    merge_duplicate_headers=True,
                    export_format="xlsx",
                    hidden_columns=["id", "es_date", "es_date_lasttime"],
                    markdown_options={"link_target": "_blank"},
                    style_cell={
                        "font-family": "Helvetica",
                        "font-size": "12px",
                        "textAlign": "left",
                    },
                    style_header={
                        "backgroundColor": "rgb(52, 73, 94)",
                        "color": "white",
                        "fontWeight": "bold",
                        "overflow": "hidden",
                    },
                    style_cell_conditional=[
                        {
                            "if": {"column_id": "pur_remark"},
                            "max-width": "100px",
                            "white-space": "nowrap",
                            "overflow": "hidden",
                            "color": "#1abc9c",
                        },
                        {
                            "if": {"column_id": "mat_remark"},
                            "max-width": "100px",
                            "white-space": "nowrap",
                            "overflow": "hidden",
                            "color": "#1abc9c",
                        },
                        {
                            "if": {"column_id": "des"},
                            "max-width": "200px",
                            "white-space": "nowrap",
                            "overflow": "hidden",
                            "color": "#1abc9c",
                        },
                        {"if": {"column_id": "es_date_merge"}, "color": "#1abc9c"},
                    ],
                    css=[
                        {
                            "selector": ".dash-spreadsheet-menu-item",
                            "rule": "display:none",
                        },
                        {"selector": ".cell-markdown p", "rule": "margin:0"},
                        {"selector": ".cell-markdown", "rule": "padding-bottom:2px"},
                        {"selector": ".cell-markdown p a", "rule": "padding:0"},
                        {
                            "selector": ".export",
                            "rule": "position:absolute;bottom:-30px;border:none;background-color:#34495e;color:white;left:20px;font-size: 17px;border-radius:4px;",
                        },
                    ],
                    # cell-markdown
                ),
                dbc.Toast(
                    id="remark-record-1",
                    icon="success",
                    is_open=False,
                    dismissable=True,
                    duration=5000,
                    style={"position": "fixed", "top": 270, "right": 450, "width": 350},
                ),
            ],
            className="my-width pt-2",
        ),
    ],
    style={"width": "100%"},
)


def layout(**query):
    content = html.Div(
        page_shortage, id="page-content-a", className="my-right mr-3 ml-1"
    )
    layout = dbc.Container(
        [content],
        className="my-parent",
        fluid=True,
    )
    return layout


def prtno_active(prtno):
    if not prtno:
        raise PreventUpdate
    else:
        # conn = pool.connection()
        sql = "select prtno,pm,ee,me,mag,layout from ssp.prt where (prtno=%s)"
        sql1 = "select owner1 from ssp.bom_record where (prtno=%s)"
        sql2 = "select proj,stage,prtno,pcbpn,smtstadate,qty,pcbstatus,smstatus,\
            smtfin_date,fsdate_act,findate_act,fsdate_sch from ssp.prt where (prtno=%s)"
        sql3 = "select deltapn,des,mfgname,mfgpn,qty,req_date,es_date,es_date_lasttime,\
            pur_status,pur_remark,mat_remark,pur from ssp.pur \
            where (prtno=%s) and (application=%s)"
        params = [prtno]
        params3 = [prtno, "Project"]
        df = read_sql(sql, params=params)
        df1 = read_sql(sql1, params=params)
        df2 = read_sql(sql2, params=params)
        df3 = read_sql(sql3, params=params3)
        # conn.close()
        df["id"] = range(df.shape[0])
        df["fce"] = df1["owner1"]
        df.iloc[0] = df.iloc[0].apply(lambda x: f"[{x}](mailto:{x}@deltaww.com)")
        df = df[["pm", "ee", "me", "mag", "layout", "fce", "id"]]
        df = df.reindex(columns=["pm", "ee", "me", "mag", "layout", "fce", "id"])
        df = df.to_dict("records")
        df2["id"] = range(df2.shape[0])
        for i in [
            "smtstadate",
            "smtfin_date",
            "fsdate_act",
            "findate_act",
            "fsdate_sch",
        ]:
            df2[i] = pd.to_datetime(df2[i], errors="coerce")
            df2[i] = np.where(
                df2[i].notnull(), df2[i].dt.strftime("%Y-%m-%d %H:%M:%S"), ""
            )
        step_c = df2.at[0, "smtstadate"]
        step_d = df2.at[0, "smtfin_date"]
        step_e = df2.at[0, "fsdate_act"]
        step_f = df2.at[0, "fsdate_act"]
        step_g = df2.at[0, "findate_act"]
        if df2.at[0, "smstatus"].lower() == "planning":
            step_3 = "s-process"
            step_4 = "s-step"
            step_5 = "s-step"
            step_6 = "s-step"
            step_7 = "s-step"
        elif df2.at[0, "smstatus"].lower() == "planok":
            step_3 = "s-done"
            step_4 = "s-step"
            step_5 = "s-step"
            step_6 = "s-step"
            step_7 = "s-step"
        elif (
            df2.at[0, "smstatus"].lower() == "smtstart"
            or df2.at[0, "smstatus"].lower() == "progok"
        ):
            step_3 = "s-done"
            step_4 = "s-process"
            step_5 = "s-step"
            step_6 = "s-step"
            step_7 = "s-step"
        elif df2.at[0, "smstatus"].lower() == "smtfinish":
            step_3 = "s-done"
            step_4 = "s-done"
            step_5 = "s-process"
            step_6 = "s-step"
            step_7 = "s-step"
        elif df2.at[0, "smstatus"].lower() == "fsfinish":
            step_3 = "s-done"
            step_4 = "s-done"
            step_5 = "s-done"
            step_6 = "s-done"
            step_7 = "s-step"
        elif df2.at[0, "smstatus"].lower() == "close":
            step_3 = "s-done"
            step_4 = "s-done"
            step_5 = "s-done"
            step_6 = "s-done"
            step_7 = "s-done"
        else:
            step_3 = "s-step"
            step_4 = "s-step"
            step_5 = "s-step"
            step_6 = "s-step"
            step_7 = "s-step"
        # df2=df2[['project_id','stage','prtno','pcbpn','smtstadate','qty','pcbstatus','id']]
        df2 = df2.reindex(
            columns=[
                "proj",
                "stage",
                "prtno",
                "pcbpn",
                "fsdate_sch",
                "smtstadate",
                "qty",
                "pcbstatus",
                "id",
                "smstatus",
            ]
        )
        if df2.at[0, "pcbstatus"] == "需采购" or df2.at[0, "pcbstatus"] == "工厂调":
            step_1 = "s-process"
        else:
            step_1 = "s-done"
        step_a = df2.at[0, "pcbstatus"]
        df2 = df2.to_dict("records")
        df3["es_date_lasttime"] = pd.to_datetime(
            df3["es_date_lasttime"], errors="coerce"
        )
        df3["es_date"] = pd.to_datetime(df3["es_date"], errors="coerce")
        df3["req_date"] = pd.to_datetime(df3["req_date"], errors="coerce")
        df3["es_date_merge"] = df3["es_date_lasttime"]
        df3["es_date_merge"] = df3["es_date_merge"].fillna(df3["es_date"])
        df3["es_date_merge"] = df3["es_date_merge"].dt.date
        df3["es_date"] = df3["es_date"].dt.date
        df3["es_date_lasttime"] = df3["es_date_lasttime"].dt.date
        df3["req_date"] = df3["req_date"].dt.date
        df3["es_date_merge"] = df3["es_date_merge"].fillna("暂无交期")
        df3["id"] = range(df3.shape[0])
        df3["pur_status"] = df3.pur_status.str.lower()
        df3 = df3[
            [
                "deltapn",
                "des",
                "mfgname",
                "mfgpn",
                "qty",
                "req_date",
                "es_date_merge",
                "pur_status",
                "pur_remark",
                "mat_remark",
                "pur",
                "id",
                "es_date",
                "es_date_lasttime",
            ]
        ]
        df3 = df3.reindex(
            columns=[
                "deltapn",
                "des",
                "mfgname",
                "mfgpn",
                "qty",
                "req_date",
                "es_date_merge",
                "pur_status",
                "pur_remark",
                "mat_remark",
                "pur",
                "id",
                "es_date",
                "es_date_lasttime",
            ]
        )
        if df3[df3["pur_status"].isin(["closed"])].shape[0] == df3.shape[0]:
            step_2 = "s-done"
        else:
            step_2 = "s-process"
        df3 = df3.to_dict("records")
        return (
            df2,
            df,
            df3,
            step_1,
            step_2,
            step_3,
            step_4,
            step_5,
            step_6,
            step_7,
            step_a,
            step_c,
            step_d,
            step_e,
            step_f,
            step_g,
        )


def shortage(search):
    if not search:
        raise PreventUpdate
    d = dict(parse_qsl(urlsplit(search).query))
    prtno = d.get("prtno")
    if not prtno:
        raise PreventUpdate
    return prtno_active(prtno)


@callback(
    Output("my-tablea", "data"),
    Output("my-tableb", "data"),
    Output("my-tablec", "data"),
    Output("my-step-1", "className"),
    Output("my-step-2", "className"),
    Output("my-step-3", "className"),
    Output("my-step-4", "className"),
    Output("my-step-5", "className"),
    Output("my-step-6", "className"),
    Output("my-step-7", "className"),
    Output("my-step-a", "children"),
    Output("my-step-c", "children"),
    Output("my-step-d", "children"),
    Output("my-step-e", "children"),
    Output("my-step-f", "children"),
    Output("my-step-g", "children"),
    Input("url", "search"),
    prevent_initial_call=False,
)
def shortage_content(search):
    return shortage(search)
