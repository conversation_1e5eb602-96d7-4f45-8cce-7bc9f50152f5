# -*- coding: utf-8 -*-
import dash_mantine_components as dmc
import feffery_antd_components.alias as fac
from common import id_factory, ce_cats, get_ssp_user, parse_search
from dash_extensions.enrich import Input, Output, State, callback, no_update, ctx, ALL
from dash.exceptions import PreventUpdate
from datetime import datetime
import feffery_utils_components.alias as fuc
from components import time_line, notice, CeCancelAIO
import orjson
from utils import db

id = id_factory(__name__)


def form1(task, case):
    if case:
        disabled = True
    else:
        disabled = False
    users = get_ssp_user()
    ces = users.query("role_group == 'CE'")["nt_name"].str.title().tolist()
    cats = ce_cats()
    div = dmc.Grid(
        [
            dmc.Col(
                dmc.Select(
                    label="作业类型",
                    data=[
                        "NUDD",
                        "新厂商导入",
                        "品质稽核",
                        "年度稽核",
                        "材料专案",
                        "材料挑战",
                    ],
                    size="xs",
                    id={"type": id("form"), "index": "type"},
                    value=task.get("type"),
                    required=True,
                    disabled=disabled,
                ),
                span=2,
            ),
            dmc.Col(
                dmc.Select(
                    label="紧急程度",
                    data=["一般", "紧急"],
                    size="xs",
                    id={"type": id("form"), "index": "urgent"},
                    value=task.get("urgent"),
                    required=True,
                    disabled=disabled,
                ),
                span=2,
            ),
            dmc.Col(
                dmc.Textarea(
                    label="需求说明",
                    size="xs",
                    id={"type": id("form"), "index": "des"},
                    value=task.get("des"),
                    required=True,
                    disabled=disabled,
                    autosize=True,
                ),
                span=8,
            ),
            dmc.Col(
                dmc.MultiSelect(
                    label="申请人",
                    data=users["nt_name"].str.title().tolist(),
                    size="xs",
                    id={"type": id("form"), "index": "applicant"},
                    value=task.get("applicant", "").split(","),
                    searchable=True,
                    required=True,
                    disabled=disabled,
                ),
                span=4,
            ),
            dmc.Col(
                dmc.MultiSelect(
                    label="CE负责人",
                    data=ces,
                    size="xs",
                    id={"type": id("form"), "index": "ce"},
                    value=task.get("ce", "").split(","),
                    searchable=True,
                    required=True,
                    disabled=disabled,
                ),
                span=4,
            ),
            dmc.Col(
                dmc.MultiSelect(
                    label="抄送",
                    data=users["nt_name"].str.title().tolist(),
                    size="xs",
                    id={"type": id("form"), "index": "cc"},
                    value=task.get("cc", "").split(","),
                    searchable=True,
                    disabled=disabled,
                ),
                span=4,
            ),
            dmc.Col(
                dmc.TextInput(
                    label="料号",
                    size="xs",
                    id={"type": id("form"), "index": "deltapn"},
                    value=task.get("deltapn"),
                    required=True,
                    disabled=disabled,
                ),
                span=2,
            ),
            dmc.Col(
                dmc.TextInput(
                    label="厂商",
                    size="xs",
                    id={"type": id("form"), "index": "mfgname"},
                    value=task.get("mfgname"),
                    required=True,
                    disabled=disabled,
                ),
                span=2,
            ),
            dmc.Col(
                dmc.TextInput(
                    label="厂商料号",
                    size="xs",
                    id={"type": id("form"), "index": "mfgpn"},
                    value=task.get("mfgpn"),
                    required=True,
                    disabled=disabled,
                ),
                span=2,
            ),
            dmc.Col(
                dmc.Select(
                    label="材料类别1",
                    data=cats["cat1"].unique(),
                    size="xs",
                    id={"type": id("form"), "index": "cat1"},
                    value=task.get("cat1"),
                    required=True,
                    disabled=disabled,
                ),
                span=2,
            ),
            dmc.Col(
                dmc.Select(
                    label="材料类别2",
                    data=cats["cat2"].unique(),
                    size="xs",
                    id={"type": id("form"), "index": "cat2"},
                    value=task.get("cat2"),
                    required=True,
                    disabled=disabled,
                ),
                span=2,
            ),
            dmc.Col(
                dmc.MultiSelect(
                    label="材料类别3",
                    data=cats["cat3"].unique(),
                    size="xs",
                    id={"type": id("form"), "index": "cat3"},
                    value=task.get("cat3", "").split(","),
                    required=True,
                    disabled=disabled,
                ),
                span=2,
            ),
            dmc.Col(
                dmc.Stack(
                    [
                        dmc.Text("文件评审计划日期", size="xs"),
                        fac.DatePicker(
                            id={
                                "type": id("form"),
                                "index": "doc_review_date_sch",
                            },
                            style={"display": "flex"},
                            value=case.get("doc_review_date_sch"),
                        ),
                    ],
                    spacing=0,
                ),
                span=4,
            ),
            dmc.Col(
                dmc.Stack(
                    [
                        dmc.Text("现场稽核计划日期", size="xs"),
                        fac.DatePicker(
                            id={
                                "type": id("form"),
                                "index": "case_closed_date_sch",
                            },
                            style={"display": "flex"},
                            value=case.get("case_closed_date_sch"),
                        ),
                    ],
                    spacing=0,
                ),
                span=4,
            ),
            dmc.Col(
                dmc.Stack(
                    [
                        dmc.Text("计划结案日期", size="xs"),
                        fac.DatePicker(
                            id={"type": id("form"), "index": "demand_date"},
                            style={"display": "flex"},
                            value=case.get("demand_date"),
                        ),
                    ],
                    spacing=0,
                ),
                span=4,
            ),
        ],
        style={"display": "flex"},
    )

    return div


def form2(task, case, display):
    ce_remark = case.get("ce_remark") or "[]"
    ce_remark = orjson.loads(ce_remark)
    audit = case.get("case_closed_date")
    review = case.get("doc_review_date")
    process_value = []
    if review:
        process_value.append("doc_review_date")
    if audit:
        process_value.append("case_closed_date")

    type = task.get("type")
    if type in ("材料专案", "材料挑战"):
        display1 = "none"
    else:
        display1 = "flex"

    div = dmc.Grid(
        [
            dmc.Col(time_line(ce_remark)),
            dmc.Col(
                dmc.CheckboxGroup(
                    [
                        dmc.Checkbox(
                            label="文件评审已完成",
                            value="doc_review_date",
                            disabled=review,
                        ),
                        dmc.Checkbox(
                            label="现场稽核已完成",
                            value="case_closed_date",
                            disabled=audit,
                        ),
                    ],
                    id=id("process"),
                    value=process_value,
                    orientation="horizontal",
                    label="任务进展",
                    spacing=200,
                    # id={"type": id("form"), "index": "review"},
                ),
                display=display1,
            ),
            dmc.Col(
                dmc.RadioGroup(
                    [
                        dmc.Radio(label="Pass", value="Pass"),
                        dmc.Radio(label="Conditional Pass", value="Conditional Pass"),
                        dmc.Radio(label="Fail", value="Fail"),
                    ],
                    id=id("conclusion"),
                    orientation="horizontal",
                    label="结论",
                    spacing=150,
                    value=case.get("conclusion"),
                ),
                span=12,
                display=display1,
            ),
            dmc.Col(
                fac.DraggerUpload(
                    apiUrl="/upload/",
                    text="上传附件",
                    # id=id("ce-attachment"),
                    lastUploadTaskRecord={},
                    id=id("attachment"),
                    # uploadId=f"fa_ce_attachment_{task_id}",
                    # defaultFileList=fl3,
                    # disabled=disabled_upload,
                )
            ),
            dmc.Col(
                dmc.Anchor(
                    "厂商评鉴资料",
                    href="https://ideltacn.deltaww.com/desh/desh/SDCHDC/SUP/材料工程_CE/02 CE工作内容/05 Component Challenge",
                    target="_blank",
                ),
            ),
            dmc.Col(
                dmc.Textarea(
                    label="CE备注",
                    autosize=True,
                    id=id("ce_remark"),
                    # id={"type": id("form"), "index": "ce_remark"},
                ),
                span=10,
            ),
            dmc.Col(
                dmc.NumberInput(
                    label="用时(按照分钟维护)",
                    id=id("work_minute"),
                    value=case.get("work_minute"),
                    # id={"type": id("form"), "index": "work_minute"},
                ),
                span=2,
            ),
        ],
        style={"display": display},
        align="end",
    )
    return div


def layout(tid=None, **kwargs):
    if tid:
        task = db.find_one("ce.task", {"id": tid}) or {}
        case = db.find_one("ce.special_case", {"task_id": tid}) or {}
        submit_display = "none"
        display = "flex"
    else:
        task = {}
        case = {}
        submit_display = "flex"
        display = "none"

    if task.get("status") == "close":
        disabled = True
        close_child = "已结案"
    else:
        disabled = False
        close_child = "结案"
    form = dmc.Stack(
        [
            dmc.Center(dmc.Text("专案", weight=800)),
            dmc.Divider(),
            form1(task, case),
            dmc.Divider(),
            form2(task, case, display),
            dmc.Group(
                [
                    dmc.Button(
                        "提交",
                        id=id("submit"),
                        display=submit_display,
                        disabled=disabled,
                    ),
                    dmc.Button(
                        "更新",
                        color="orange",
                        id=id("update"),
                        display=display,
                        disabled=disabled,
                    ),
                    dmc.Button(
                        close_child,
                        color="green",
                        id=id("close"),
                        display=display,
                        disabled=disabled,
                    ),
                    CeCancelAIO(__name__, disabled=disabled),
                ],
                position="apart",
            ),
            dmc.Space(),
            fac.Modal(
                id=id("modal"),
                title="提醒",
                renderFooter=True,
                maskClosable=False,
                centered=True,
                closable=False,
                cancelButtonProps={"style": {"display": "none"}},
                width=300,
            ),
            fuc.ExecuteJs(id=id("js")),
        ],
    )
    return dmc.Container(form)


@callback(
    Output({"type": id("form"), "index": "cat1"}, "value"),
    Output({"type": id("form"), "index": "cat2"}, "value"),
    Output({"type": id("form"), "index": "cat3"}, "value"),
    Output({"type": id("form"), "index": "cat2"}, "data"),
    Output({"type": id("form"), "index": "cat3"}, "data"),
    Input({"type": id("form"), "index": "cat1"}, "value"),
    Input({"type": id("form"), "index": "cat2"}, "value"),
    Input({"type": id("form"), "index": "cat3"}, "value"),
)
def cats_value(cat1, cat2, cat3):
    """材料类别下拉框的回调"""
    tid = ctx.triggered_id
    df = ce_cats()
    c1 = df["cat1"] == cat1
    c2 = df["cat2"] == cat2
    cat2_data = df.loc[c1]["cat2"].unique()
    cat3_data = df.loc[c1 & c2]["cat3"].unique()
    idx = tid.get("index")
    if idx == "cat1":
        return cat1, None, None, cat2_data, cat3_data
    elif idx == "cat2":
        return no_update, cat2, None, no_update, cat3_data
    elif idx == "cat3":
        return no_update, no_update, cat3, no_update, no_update
    raise PreventUpdate


@callback(
    Output({"type": id("form"), "index": "doc_review_date_sch"}, "disabled"),
    Output({"type": id("form"), "index": "case_closed_date_sch"}, "disabled"),
    Input({"type": id("form"), "index": "type"}, "value"),
    prevent_initial_call=False,
)
def disabled_by_type(value):
    if value in ("材料专案", "材料挑战"):
        return True, True
    elif value in ("品质稽核", "年度稽核"):
        return True, False
    else:
        return False, False


@callback(
    Output("global-notice", "children"),
    Output(id("submit"), "disabled"),
    Input(id("submit"), "n_clicks"),
    State({"type": id("form"), "index": ALL}, "value"),
    State({"type": id("form"), "index": ALL}, "disabled"),
)
def submit(n_clicks, value, disabled):
    """提交按钮的回调"""
    if not n_clicks:
        raise PreventUpdate

    data = {i.get("id").get("index"): i.get("value") for i in ctx.states_list[0]}

    if not all(j for i, j in enumerate(data.values()) if not disabled[i]):
        return notice("请完整填写表单", "error"), False
    k1 = [
        "type",
        "urgent",
        "applicant",
        "ce",
        "cc",
        "deltapn",
        "des",
        "mfgname",
        "mfgpn",
        "cat1",
        "cat2",
        "cat3",
    ]
    k2 = [
        "task_id",
        "deltapn",
        "des",
        "mfgname",
        "mfgpn",
        "cat1",
        "cat2",
        "cat3",
        "doc_review_date_sch",
        "case_closed_date_sch",
        "demand_date",
    ]

    task = {i: data.get(i) for i in k1}
    task["cc"] = set(data.get("cc")) | set(["Ru.Xue", "Ying.Gao"])
    task["cc"] = ",".join(task["cc"])
    task["ce"] = ",".join(task["ce"])
    task["cat3"] = ",".join(task["cat3"])
    task["applicant"] = ",".join(task["applicant"])

    applicant = db.find_one("ssp.user", {"nt_name": data.get("applicant")[0]})

    task["dept_id"] = applicant.get("dept_id")
    task["dept"] = applicant.get("dept")
    task["sub_type"] = data.get("type")
    task["start_date"] = datetime.now()
    task_id = db.insert("ce.task", task)

    data2 = {i: data.get(i) for i in k2}
    data2["task_id"] = task_id
    data2["cat3"] = ",".join(data2["cat3"])
    db.insert("ce.special_case", data2)
    return notice("提交成功"), True


@callback(
    # Output(id("modal"), "visible"),
    Output("global-notice", "children"),
    Output(id("update"), "disabled"),
    Output(id("close"), "disabled"),
    Input(id("update"), "n_clicks"),
    Input(id("close"), "n_clicks"),
    State({"type": id("form"), "index": ALL}, "value"),
    State(id("process"), "value"),
    State(id("conclusion"), "value"),
    State(id("attachment"), "lastUploadTaskRecord"),
    State(id("ce_remark"), "value"),
    State(id("work_minute"), "value"),
    State("user", "data"),
    State("url", "search"),
)
def update(
    n1, n2, value, process, conclusion, attachment, remark, work_minute, user, url
):
    """更新"""
    if not n1 and not n2:
        raise PreventUpdate

    url = parse_search(url)
    task_id = url.get("tid")

    if "close" in ctx.triggered_id:
        work_type = value[0]
        if work_type in ("材料专案", "材料挑战"):
            if not attachment:
                return notice("无法结案(请上传附件)", "error"), False, False
        else:
            if not all([conclusion, work_minute, process, attachment, work_minute]):
                return (
                    notice("无法结案(任务进展,结论,附件,用时必填)", "error"),
                    False,
                    False,
                )
        task_data = {"id": task_id, "status": "close", "end_date": datetime.now()}
    else:
        if not remark:
            return notice("请填写备注", "error"), False, False
        task_data = {"id": task_id, "status": "ongoing"}

    new_data = {
        i.get("id").get("index"): i.get("value")
        for i in ctx.states_list[0]
        if i.get("id").get("index")
        in ("doc_review_date_sch", "case_closed_date_sch", "demand_date")
    }

    # new_data = {i.get("id").get("index"): i.get("value") for i in ctx.states_list[0]}

    nt_name = user.get("nt_name").title()
    case = db.find_one("ce.special_case", {"task_id": task_id})
    data = case | new_data
    ce_remark = orjson.loads(case.get("ce_remark") or "[]")

    now = datetime.now()
    dd = {"time": f"{now:%Y-%m-%d %H:%M:%S}", "user": nt_name}

    if remark:
        dd.update({"comment": remark})
    if attachment:
        dd.update({"attachment": attachment.get("taskId")})
        data.update(
            {"attachment": f'{attachment.get("taskId")}/{attachment.get("fileName")}'}
        )

    if conclusion:
        data.update({"conclusion": conclusion})
    if work_minute:
        data.update({"work_minute": work_minute})
    if process:
        process1 = {i: now for i in process if not case.get(i)}
        dd.update(process1)
        data.update(process1)

    ce_remark.append(dd)
    data.update({"ce_remark": orjson.dumps(ce_remark)})

    db.update("ce.task", task_data)
    db.update("ce.special_case", data)

    return notice("更新成功"), True, True


@callback(
    Output(id("js"), "jsString"),
    Input(id("modal"), "okCounts"),
)
def close_modal(ok):
    if not ok:
        raise PreventUpdate
    return "close()"
