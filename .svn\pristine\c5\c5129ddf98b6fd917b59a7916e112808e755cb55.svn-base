import base64
import io

import dash_bootstrap_components as dbc
import dash_keyed_file_browser as kfb
from config import SSP_DIR
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import Input, Output, State, callback, dash, dcc

dash.register_page(__name__, title="表单")

ssp_form_alert = dbc.Alert(
    id="ssp_form_alert",
    is_open=False,
    duration=3000,
    color="warning",
    class_name="d-inline-flex p-1",
)

ssp_form_upload = dcc.Upload(
    dbc.Button("上传更新", id="ssp_form_upload_btn", color="warning", size="lg"),
    id="ssp_form_upload",
    style={"display": "none"},
)


layout = dbc.Container(
    [
        dbc.Row(
            [
                dbc.Col(ssp_form_upload, width=2),
                dbc.Col(ssp_form_alert, width=10),
            ],
        ),
        kfb.KeyedFileBrowser(
            files=[{"key": "", "size": 0}],
            id="kfb",
            showActionBar=False,
            canFilter=False,
            # nestChildren=True,
        ),
        dcc.Download(id="download-file"),
    ],
    fluid=True,
    # className="ml-3 pr-5",
)


@callback(
    Output("kfb", "files"),
    Output("ssp_form_upload", "style"),
    Input("ssp_form_alert", "color"),
    State("user", "data"),
    prevent_initial_call=False,
)
def create_card(color, user):
    if color == "danger":
        raise PreventUpdate

    dept = user.get("dept")
    dept_id = user.get("dept_id")
    role_group = user.get("role_group")
    nt_name = user.get("nt_name").lower()

    if dept_id == 10:
        file_all = SSP_DIR / "program" / "PUBLIC"
        file_owner = SSP_DIR / "program" / role_group
    else:
        file_all = SSP_DIR / "program" / "EXTERNAL" / "PUBLIC"
        file_owner = SSP_DIR / "program" / "EXTERNAL" / dept

    file0 = [
        i
        for i in file_all.rglob("*")
        if not i.stem.startswith("~") and i.name != "Thumbs.db"
    ]
    file1 = [
        i
        for i in file_owner.rglob("*")
        if not i.stem.startswith("~") and i.name != "Thumbs.db"
    ]
    if role_group == "CE":  # "LGT_LGT" / "PanasonicCheckList"
        check_list_dir = SSP_DIR / "program" / "EXTERNAL"
        check_list_files = check_list_dir.rglob("*.*")
        # breakpoint()
        file1.extend(check_list_files)

    file0 = [
        {
            "key": i.relative_to(file_all.parent).as_posix(),
            "size": i.stat().st_size,
            "modified": i.stat().st_mtime * 1000,
        }
        for i in file0
    ]
    file1 = [
        {
            "key": i.relative_to(file_owner.parent).as_posix(),
            "size": i.stat().st_size,
            "modified": i.stat().st_mtime * 1000,
        }
        for i in file1
    ]

    # child = kfb.KeyedFileBrowser(
    #     file0 + file1,
    #     id="kfb",
    #     # showActionBar=True,
    #     canFilter=True,
    # )
    if nt_name in (
        "weiming.li",
        "ying.gao",
        "ru.xue",
        "xiangfeng.cao",
        "mona.zhang",
        "bo.sm.wang",
        "kuangyi.gu",
    ):
        style = {"display": "block"}
    else:
        style = {"display": "none"}
    return file0 + file1, style


@callback(
    Output("download-file", "data"),
    Input("kfb", "openFile"),
    State("user", "data"),
)
def display_open(value, user):
    if value is None:
        raise PreventUpdate
    fn = value.get("key")
    if not fn:
        raise PreventUpdate

    dept_id = user.get("dept_id")
    if dept_id == 10:
        file_path = SSP_DIR / "program" / fn
    else:
        file_path = SSP_DIR / "program" / "EXTERNAL" / fn
    return dcc.send_file(file_path)


@callback(
    Output("ssp_form_upload_btn", "children"),
    Input("kfb", "selectedFile"),
)
def update_selected_file(selected_file):
    fn = selected_file.get("key")
    return f"上传更新{fn}"


@callback(
    Output("ssp_form_alert", "is_open"),
    Output("ssp_form_alert", "children"),
    Output("ssp_form_alert", "color"),
    Input("ssp_form_upload", "contents"),
    Input("ssp_form_upload", "filename"),
    State("kfb", "selectedFile"),
)
def upload_file(contents, fn, sf):
    if not contents:
        raise PreventUpdate
    if not sf:
        return True, "请先选择待更新的对应文件", "danger"

    content_type, content_string = contents.split(",")
    decoded = base64.b64decode(content_string)
    fio = io.BytesIO(decoded)

    sfn = SSP_DIR / "program" / sf.get("key")
    if fn == sfn.name:
        file_path = sfn
    else:
        file_path = sfn.parent / fn
    with open(file_path, "wb") as f:
        f.write(fio.getvalue())
    return True, "更新成功", "success"
