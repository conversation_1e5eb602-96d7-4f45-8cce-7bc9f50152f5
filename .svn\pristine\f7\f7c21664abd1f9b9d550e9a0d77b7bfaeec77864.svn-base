# -*- coding: utf-8 -*-
import re
from datetime import datetime, timedelta
from uuid import uuid4

import dash_ag_grid as dag
import dash_mantine_components as dmc
import feffery_antd_components.alias as fac
import numpy as np
import pandas as pd
from dash import MATCH, Patch, ctx, no_update, set_props
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import Input, Output, State, callback, dcc, html
from dash_player import DashPlayer

from common import id_factory, read_sql
from components import create_sidebar, notice
from config import UPLOAD_FOLDER_ROOT, pool
from tasks import (
    bg_label_print,
    bg_mail,
    df_to_html,
    task_import_uuid_to_my300,
    task_take_off_shelf,
)
from utils import db

id = id_factory(__name__)
menu_items = [
    {
        "key": "0",
        "title": "仓库",
        "label": "仓库",
        "icon": "material-symbols:home",
        "href": "/stock?page=home",
        "page": "home",
        "font-weight": "bolder",
        "color": "rgb(0, 159, 232)",
    },
    {
        "key": "1",
        "title": "入库",
        "label": "入库",
        "icon": "ic:round-log-in",
        "href": "/stock?page=in",
        "page": "in",
        "status": "in",
    },
    {
        "key": "2",
        "title": "出库",
        "label": "出库",
        "icon": "mdi:logout",
        "href": "/stock?page=out",
        "page": "out",
        "status": "out",
    },
    {
        "key": "3",
        "title": "管理",
        "label": "管理",
        "icon": "lets-icons:setting-fill",
        "href": "/stock?page=setting",
        "page": "setting",
        "status": "setting",
    },
    # {
    #     "key": "5",
    #     "title": "收料",
    #     "label": "收料",
    #     "icon": "mdi:account-cog",
    #     "href": "/stock?page=receive",
    #     "page": "receive",
    # },
    # {
    #     "key": "6",
    #     "title": "采购记录",
    #     "label": "采购记录",
    #     "icon": "material-symbols:search",
    #     "href": "/stock?page=pur",
    #     "page": "pur",
    # },
    # {
    #     "key": "7",
    #     "title": "出库确认",
    #     "label": "出库确认",
    #     "icon": "mdi:account-cog",
    #     "href": "/stock?page=stockout",
    #     "page": "stockout",
    # },
    {
        "key": "8",
        "title": "查询",
        "label": "查询",
        "icon": "mdi:sql-query",
        "href": "/stock?page=query",
        "page": "query",
    },
]


def pur_ongoing_data(area: str) -> pd.DataFrame:
    sql = "select id as pur_id,prt_id,bom_id,item_pur,pur_status,application,\
        deltapn,des,mfgname,mfgpn,prtno,b.name,pur_remark,dept,pur,qty,\
        received_qty,po_no,pr_no,inv_no,price,mat_remark,rd,start_date,checkcode,\
        dest_area as area,dept_id,mat_group \
        from ssp.pur \
        left join (select nt_name,name from user)b \
        on b.nt_name=pur.rd \
        where pur_status not in %s and pur!=%s"
    params = [["closed", "cancel", "received"], "bo.sm.wang"]
    df = read_sql(sql, params=params)

    df["application"] = df["application"].fillna("").str.lower()
    df["checkcode"] = df["checkcode"].str.strip()
    df["deltapn"] = df["deltapn"].str.strip()
    df["type"] = "入库"
    c1 = df["application"] == "debug"
    c2 = df["prtno"].fillna("") == ""
    c3 = df["mat_group"].isin(["鋼網", "PCB"])
    c4 = df["deltapn"].str.startswith("287")
    df["type"] = np.where((c1 & c2) | c3 | c4, "收料", df["type"])
    c1 = df["area"] != area
    df["type"] = np.where(c1, "转寄", df["type"])
    df["id"] = range(df.shape[0])
    df = df.sort_values("area")

    # df["count"] = 1
    # grp = df.groupby("checkcode").agg(
    #     {"area": set, "application": set, "count": sum, "type": set}
    # )
    # print(grp.loc[grp["count"] > 2, ["application", "type", "count", "area"]])
    return df


def stockout_confirm_table():
    # TODO:仅本地区的人员可以操作本地区的数据
    sql = "select * from (select * from ssp.bom_stockout where action is null)a \
        left join (select area,checkcode,stockno,qty as stock_qty \
            from ssp.stock_union_mag) b \
            on a.area=b.area and a.checkcode=b.checkcode"
    df = read_sql(sql)
    table = dag.AgGrid(
        id=id("stockout-table"),
        className="ag-theme-quartz",
        columnDefs=[
            {
                "field": "action",
                "headerName": "ACTION",
                "width": 90,
                "editable": True,
                "cellEditor": {"function": "DMC_Select"},
                "cellEditorParams": {
                    "clearable": True,
                    "shadow": "xl",
                    "function": "dynamicOptions(params)",
                },
                "cellEditorPopup": True,
                "singleClickEdit": True,
            },
            {
                "field": "actual_qty",
                "headerName": "实际数量",
                "width": 80,
                "editable": True,
                "cellEditor": {"function": "NumberInput"},
                "singleClickEdit": True,
            },
            {"field": "qty", "headerName": "变更数量", "width": 80},
            {"field": "prtno", "headerName": "项目号", "width": 125},
            {"field": "smstatus", "headerName": "样制状态", "width": 100},
            {"field": "source", "headerName": "来源", "width": 90},
            {
                "field": "stockno",
                "headerName": "库位号",
                "width": 100,
                "editable": True,
            },
            {
                "field": "checkcode",
                "headerName": "CheckCode",
                "width": 130,
                "editable": True,
            },
            {"field": "deltapn", "headerName": "料号", "width": 130, "editable": True},
            {"field": "des", "headerName": "描述", "width": 200},
            {"field": "mfgname", "headerName": "厂商", "width": 100},
            {"field": "mfgpn", "headerName": "厂商料号", "width": 170},
            {"field": "stock_qty", "headerName": "库存数量", "width": 90},
            {"field": "pur_remark", "headerName": "采购备注", "width": 90},
            {"field": "mat_remark", "headerName": "物料备注", "width": 90},
            {"field": "designno", "headerName": "位置号", "width": 90},
            {
                "field": "gmt_create",
                "headerName": "变更时间",
                "width": 80,
                "valueGetter": {
                    "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')\
                        (params.data.gmt_create)"
                },
                "valueFormatter": {"function": "d3.timeFormat('%m/%d')(params.value)"},
            },
            {"field": "bom_owner", "headerName": "变更发起人", "width": 90},
            {"field": "pur", "headerName": "采购职责人", "width": 90},
            {"field": "owner", "headerName": "处理人", "width": 90},
            {"field": "gmt_update", "headerName": "处理时间", "width": 90},
        ],
        rowData=df.to_dict(orient="records"),
        # columnSize="autoSize",
        # rowSelection="single",
        defaultColDef={
            # "initialWidth": 50,
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
            # "editable": True,
        },
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
            # "pinnedBottomRowData": [
            #     {"incoming_qty": "汇总", "deltapn": f"{len(rowData)}"}
            # ],
        },
    )
    filter = dmc.TextInput(id=id("stockout-filter"), placeholder="输入关键字搜索")
    submit = dmc.Button("提交", id=id("stockout-submit"), color="green")

    content = dmc.Stack(
        [
            dmc.Grid(
                [
                    dmc.Col(filter, span=6),
                    dmc.Col(submit, span=2),
                ],
            ),
            table,
        ],
        spacing=5,
    )
    return content


def stock_in_tab1(user: dict):
    area = user.get("area")
    df = pur_ongoing_data(area)
    sql = "select distinct packing from ssp.stockno_list where area=%s"
    dfo = read_sql(sql, params=[area])
    stockno_packing = dfo["packing"].tolist()
    stockno_packing += ["项目库位"]

    table = dag.AgGrid(
        id=id("in-table"),
        columnDefs=[
            {
                "field": "type",
                "headerName": "类型",
                "width": 110,
                "checkboxSelection": True,
                "headerCheckboxSelection": True,
                "headerCheckboxSelectionFilteredOnly": True,
                "pinned": "left",
            },
            {
                "field": "deltapn",
                "headerName": "料号",
                "width": 150,
                "pinned": "left",
            },
            {"field": "po_no", "headerName": "PO单号", "width": 100},
            {"field": "mfgname", "headerName": "厂商", "width": 100},
            {"field": "mfgpn", "headerName": "厂商料号", "width": 170},
            {"field": "area", "headerName": "材料属地", "width": 90},
            {"field": "application", "headerName": "用途", "width": 80},
            {"field": "prtno", "headerName": "项目号", "width": 125},
            # {"field": "stockno", "headerName": "库位", "width": 100},
            {"field": "des", "headerName": "描述", "width": 200},
            # {"field": "pur_status", "headerName": "状态", "width": 100},
            {"field": "pur_remark", "headerName": "采购备注", "width": 120},
            {"field": "mat_remark", "headerName": "物料备注", "width": 120},
            # {"field": "dept", "headerName": "部门", "width": 100},
            # {"field": "name", "headerName": "工程师", "width": 110},
            {"field": "pur", "headerName": "采购人员", "width": 100},
            {"field": "qty", "headerName": "需求数量", "width": 90},
            # {"field": "received_qty", "headerName": "已收数量", "width": 100},
            {"field": "pr_no", "headerName": "PR单号", "width": 100},
            {"field": "checkcode", "headerName": "系列料号", "width": 150},
            {"field": "id", "headerName": "id", "hide": True},
            {"field": "item_pur", "headerName": "item_pur", "hide": True},
            {"field": "price", "headerName": "price", "hide": True},
            {"field": "inv_no", "headerName": "inv_no", "hide": True},
        ],
        rowData=df.to_dict("records"),
        dashGridOptions={
            "rowSelection": "single",
            # "rowSelection": "multiple",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "suppressRowClickSelection": True,
            "suppressMaintainUnsortedOrder": True,
            # "isRowSelectable": {"function": f"params.data.area == '{area}'"},
            # "animateRows": False,
        },
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        className="ag-theme-quartz",
        getRowId="params.data.id",
        style={"height": "70vh"},
        # getRowStyle={
        #     "styleConditions": [
        #         {
        #             "condition": "params.data['prtno'].includes('#')",
        #             "style": {"backgroundColor": "sandybrown"},
        #         }
        #     ]
        # },
    )
    div = dmc.Stack(
        [
            dmc.Group(
                [
                    dmc.TextInput(
                        id=id("in-filter"),
                        label="模糊搜索",
                        type="search",
                        description="跨列关键字,用空格隔开",
                        inputWrapperOrder=["label", "input", "description"],
                        styles={
                            "root": {
                                "display": "flex",
                                "flexDirection": "row",
                                "alignItems": "center",
                            },
                            "description": {
                                "width": 70,
                            },
                            "input": {
                                "width": 300,
                            },
                        },
                        debounce=1000,
                    ),
                    dmc.Button(
                        "收料入库",
                        id=id("in-receive"),
                        color="green",
                        variant="outline",
                        disabled=True,
                    ),
                    dmc.Button(
                        "转寄",
                        id=id("in-forward"),
                        color="orange",
                        variant="outline",
                        disabled=True,
                    ),
                ],
                spacing=50,
            ),
            dcc.Loading(table),
            dmc.Modal(
                dmc.Stack(
                    [
                        fac.Form(
                            [
                                fac.FormItem(
                                    fac.Input(
                                        id=id("in-qty"),
                                        autoFocus=True,
                                        prefix=[],
                                        autoSize=True,
                                    ),
                                    label="来料数量",
                                ),
                                fac.FormItem(
                                    fac.Select(
                                        id=id("stockno"),
                                        emptyContent=fac.Form(
                                            [
                                                fac.FormItem(
                                                    fac.RadioGroup(
                                                        options=stockno_packing,
                                                        id=id("stockno-packing"),
                                                    ),
                                                    label="材料包装",
                                                ),
                                                fac.FormItem(
                                                    fac.RadioGroup(
                                                        options=[],
                                                        # options=stockno_type,
                                                        id=id("stockno-type"),
                                                    ),
                                                    label="库位类型",
                                                ),
                                                fac.FormItem(
                                                    fac.RadioGroup(
                                                        options=[],
                                                        id=id("stockno-category"),
                                                    ),
                                                    label="库位架别",
                                                ),
                                            ],
                                            layout="vertical",
                                        ),
                                        style={"width": "300px"},
                                    ),
                                    label="库位号",
                                ),
                                fac.FormItem(
                                    fac.Button(
                                        "收料入库",
                                        id=id("in-submit"),
                                        type="primary",
                                    )
                                ),
                                fac.FormItem(
                                    dmc.Alert(id=id("alert"), hide=True, duration=5000),
                                ),
                            ],
                            layout="inline",
                            # gutter=50,
                            # align="center",
                            # spacing=100,
                        ),
                        dag.AgGrid(
                            id=id("modal-table"),
                            columnDefs=[
                                {
                                    "field": "new_received_qty",
                                    "headerName": "收料数量",
                                    "width": 80,
                                    "editable": True,
                                    "cellEditor": "agNumberCellEditor",
                                    "cellEditorParams": {"min": 0},
                                    "pinned": "left",
                                },
                                # {
                                #     "field": "cumsum",
                                #     "headerName": "cumsum",
                                #     "width": 80,
                                # },
                                {
                                    "field": "qty",
                                    "headerName": "需求数量",
                                    "width": 90,
                                    "pinned": "left",
                                },
                                {
                                    "field": "received_qty",
                                    "headerName": "已收数量",
                                    "width": 90,
                                    "pinned": "left",
                                },
                                # {
                                #     "field": "area",
                                #     "headerName": "材料属地",
                                #     "width": 80,
                                # },
                                {"field": "type", "headerName": "类型", "width": 80},
                                {
                                    "field": "application",
                                    "headerName": "用途",
                                    "width": 80,
                                },
                                {
                                    "field": "prtno",
                                    "headerName": "项目号",
                                    "width": 100,
                                },
                                {
                                    "field": "deltapn",
                                    "headerName": "料号",
                                    "width": 130,
                                },
                                # {
                                #     "field": "checkcode",
                                #     "headerName": "系列料号",
                                #     "width": 130,
                                # },
                                {"field": "des", "headerName": "描述", "width": 200},
                                {
                                    "field": "mfgname",
                                    "headerName": "厂商",
                                    "width": 100,
                                },
                                {
                                    "field": "mfgpn",
                                    "headerName": "厂商料号",
                                    "width": 170,
                                },
                                {
                                    "field": "pur_status",
                                    "headerName": "状态",
                                    "width": 100,
                                },
                                {
                                    "field": "start_date",
                                    "headerName": "申请日",
                                    "width": 95,
                                    "valueGetter": {
                                        "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')\
                                (params.data.start_date)"
                                    },
                                    "valueFormatter": {
                                        "function": "d3.timeFormat('%m/%d')(params.value)"
                                    },
                                },
                                {
                                    "field": "pur_remark",
                                    "headerName": "采购备注",
                                    "width": 120,
                                },
                                {"field": "dept", "headerName": "部门", "width": 100},
                                {"field": "name", "headerName": "工程师", "width": 110},
                                {
                                    "field": "pur",
                                    "headerName": "采购人员",
                                    "width": 100,
                                },
                                {
                                    "field": "po_no",
                                    "headerName": "PO单号",
                                    "width": 100,
                                },
                                {
                                    "field": "pr_no",
                                    "headerName": "PR单号",
                                    "width": 100,
                                },
                                {
                                    "field": "mat_remark",
                                    "headerName": "物料备注",
                                    "width": 120,
                                },
                                {"field": "id", "headerName": "id", "hide": True},
                                {
                                    "field": "checkcode",
                                    "headerName": "checkcode",
                                    "hide": True,
                                },
                                {
                                    "field": "item_pur",
                                    "headerName": "item_pur",
                                    "hide": True,
                                },
                                {"field": "price", "headerName": "price", "hide": True},
                                {
                                    "field": "inv_no",
                                    "headerName": "inv_no",
                                    "hide": True,
                                },
                            ],
                            rowData=[{}],
                            style={"height": "250px"},
                            dashGridOptions={
                                "rowSelection": "single",
                                "stopEditingWhenCellsLoseFocus": True,
                                "singleClickEdit": True,
                                "rowHeight": 35,
                                "enableCellTextSelection": True,
                            },
                            defaultColDef={
                                "resizable": True,
                                "sortable": True,
                                "filter": True,
                                "wrapHeaderText": True,
                                "autoHeaderHeight": True,
                            },
                            className="ag-theme-quartz",
                            getRowStyle={
                                "styleConditions": [
                                    {
                                        "condition": "(params.data['prtno']+'').includes('#')",
                                        "style": {"backgroundColor": "sandybrown"},
                                    }
                                ]
                            },
                        ),
                    ]
                ),
                id=id("modal"),
                size="100%",
                centered=True,
                closeOnEscape=False,
                closeOnClickOutside=False,
            ),
        ]
    )
    return div


def stock_in_tab2(user: dict):
    # area = user.get("area")
    sql = "select id,pur_status,application,deltapn,des,mfgname,mfgpn,prtno,rd,\
        pur_remark,dept,pur,qty,mat_remark,start_date,checkcode,dest_area,\
        dept_id,sub_deltapn,prt_id,bom_id,received_qty from ssp.pur \
        where application=%s and pur_status in %s and (pur=%s or sub_deltapn!='')"
    params = [
        "project",
        ["sa", "wait for material", "pending", "transferred"],
        "bo.sm.wang",
    ]
    df = read_sql(sql, params=params)
    df["application"] = df["application"].fillna("").str.lower()
    df["incoming_qty"] = None
    df["remark"] = ""
    # df["incoming_qty"] = df["qty"] - df["received_qty"].fillna(0)

    table = dag.AgGrid(
        id=id("in-table-2"),
        columnDefs=[
            # {"field": "dest_area", "headerName": "材料属地", "width": 80},
            {
                "field": "incoming_qty",
                "headerName": "收料数量",
                "width": 80,
                "editable": True,
                "cellEditor": "agNumberCellEditor",
                "cellEditorParams": {"min": "0"},
                "pinned": "left",
            },
            {
                "field": "remark",
                "headerName": "备注",
                "width": 80,
                "editable": True,
                "pinned": "left",
            },
            {
                "field": "deltapn",
                "headerName": "料号",
                "width": 150,
                "checkboxSelection": True,
                "headerCheckboxSelection": True,
                "headerCheckboxSelectionFilteredOnly": True,
                "pinned": "left",
            },
            {"field": "qty", "headerName": "需求数量", "width": 90},
            {"field": "dest_area", "headerName": "材料属地", "width": 80},
            {"field": "prtno", "headerName": "项目号", "width": 125},
            {"field": "des", "headerName": "描述", "width": 200},
            {"field": "mfgname", "headerName": "厂商", "width": 100},
            {"field": "mfgpn", "headerName": "厂商料号", "width": 170},
            # {"field": "application", "headerName": "用途", "width": 80},
            {"field": "pur_remark", "headerName": "采购备注", "width": 120},
            {"field": "mat_remark", "headerName": "物料备注", "width": 120},
            # {"field": "pur_status", "headerName": "状态", "width": 100},
            {"field": "dept", "headerName": "部门", "width": 100},
            {"field": "rd", "headerName": "工程师", "width": 110},
            {"field": "pur", "headerName": "采购人员", "width": 100},
            {"field": "checkcode", "headerName": "系列料号", "width": 150},
            {"field": "sub_deltapn", "headerName": "替代料号", "width": 150},
            {"field": "received_qty", "headerName": "已收数量", "width": 150},
            # {"field": "po_no", "headerName": "PO单号", "width": 100},
            # {"field": "pr_no", "headerName": "PR单号", "width": 100},
            # {"field": "price", "headerName": "price", "hide": True},
            # {"field": "inv_no", "headerName": "inv_no", "hide": True},
            {"field": "id", "headerName": "id", "hide": True},
        ],
        rowData=df.to_dict("records"),
        dashGridOptions={
            # "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "rowSelection": "multiple",
            "suppressRowClickSelection": True,
            # "isRowSelectable": {"function": f"params.data.area == '{area}'"},
            # "animateRows": False,
        },
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        className="ag-theme-quartz",
    )
    div = dmc.Stack(
        [
            dmc.Group(
                [
                    dmc.TextInput(
                        id=id("in-filter-2"),
                        label="模糊搜索",
                        type="search",
                        # style={"width": 500},
                        description="跨列关键字,用空格隔开",
                        inputWrapperOrder=["label", "input", "description"],
                        styles={
                            "root": {
                                "display": "flex",
                                "flexDirection": "row",
                                "alignItems": "center",
                            },
                            "description": {
                                "width": 70,
                            },
                            "input": {
                                "width": 300,
                            },
                        },
                    ),
                    dmc.Tooltip(
                        dmc.Button(
                            "打印标签",
                            id=id("in-label-2"),
                            color="blue",
                            variant="outline",
                        ),
                        label="对勾选的料号打标签",
                    ),
                    dmc.Tooltip(
                        dmc.Button(
                            "更新采购记录",
                            id=id("in-receive-2"),
                            color="orange",
                            variant="outline",
                            # disabled=True,
                        ),
                        label="对有收料数量的，更新采购记录，收料数量为0时，仅更新备注",
                    ),
                ],
                spacing=50,
            ),
            dcc.Loading(table),
        ]
    )
    return div


def page_in():
    tab = dmc.Tabs(
        [
            dmc.TabsList(
                [
                    dmc.Tab("入库收料", value="1", color="red"),
                    dmc.Tab("RD提供收料", value="2", color="green"),
                ]
            ),
            dmc.Space(h=10),
            html.Div(id=id("stock-in-content")),
            # dcc.Store(id=id("store")),
            # fac.Fragment(id=id("notice")),
        ],
        color="red",
        value="1",
        id=id("in-tabs"),
    )
    return tab


def page_out():
    tab = dmc.Tabs(
        [
            dmc.TabsList(
                [
                    dmc.Tab("卷料备料", value="1"),
                    dmc.Tab("散料备料", value="2", color="green"),
                    dmc.Tab("插件备料", value="3", color="blue"),
                    dmc.Tab("变更确认", value="4", color="orange"),
                    dmc.SegmentedControl(
                        id=id("roll-segment"),
                        value="ongoing",
                        data=[
                            {"value": "ongoing", "label": "未备料"},
                            {"value": "completed", "label": "已备料"},
                        ],
                        color="blue",
                        # size="xs",
                        ml="auto",
                    ),
                ]
            ),
            dmc.Space(h=10),
            dcc.Loading(id=id("out-content")),
        ],
        color="red",
        value="1",
        id=id("out-tabs"),
    )
    return tab


def page_setting():
    tab = dmc.Tabs(
        dmc.Stack(
            [
                dmc.TabsList(
                    [
                        # dmc.Tab("领料", value="1", color="red"),
                        dmc.Tab("CSG料号变更", value="2", color="green"),
                        dmc.Tab("移库", value="3", color="blue"),
                        dmc.Tab("补标签", value="4", color="orange"),
                        dmc.Tab("清库", value="5", color="pink"),
                        dmc.Tab("库位管理", value="6", color="violet"),
                        dmc.Tab("盘点", value="7", color="indigo"),
                    ]
                ),
                dcc.Loading(id=id("setting-content")),
            ],
            spacing=5,
        ),
        color="red",
        value="2",
        id=id("setting-tabs"),
    )
    return tab


def page_query():
    tab = dmc.Tabs(
        dmc.Stack(
            [
                dmc.TabsList(
                    [
                        dmc.Tab("卷料备料", value="卷料备料"),
                        dmc.Tab("卷料出入库", value="卷料出入库"),
                        dmc.Tab("报废料盘", value="报废料盘"),
                        dmc.Tab("库位", value="库位"),
                        dmc.Tab("库存", value="库存"),
                        dmc.Tab("入库", value="入库"),
                        dmc.Tab("出库", value="出库"),
                        dmc.Tab("采购", value="采购"),
                        dmc.Tab("变更确认", value="变更确认"),
                        dmc.Tab("盘点", value="盘点"),
                    ]
                ),
                fac.Input(
                    addonBefore="搜索",
                    debounceWait=1000,
                    id=id("query-filter"),
                    mode="search",
                ),
                dcc.Loading(
                    dag.AgGrid(
                        id=id("query-table"),
                        className="ag-theme-quartz",
                        # columnDefs=[{"field": i, "headerName": i} for i in df.columns],
                        # rowData=[{}],
                        # columnSize="autoSize",
                        dashGridOptions={
                            "rowSelection": "single",
                            "stopEditingWhenCellsLoseFocus": True,
                            "singleClickEdit": True,
                            "rowHeight": 35,
                            "enableCellTextSelection": True,
                            "ensureDomOrder": True,
                        },
                        defaultColDef={
                            "resizable": True,
                            "sortable": True,
                            "filter": True,
                            "wrapHeaderText": True,
                            "autoHeaderHeight": True,
                            "cellRendererSelector": {
                                "function": "rowPinningBottom(params)"
                            },
                        },
                    )
                ),
                dmc.Button(
                    "下载", id=id("download-btn"), size="xs", variant="gradient"
                ),
                dcc.Download(id=id("download")),
            ],
            spacing=5,
        ),
        color="red",
        value="库存",
        id=id("query-tabs"),
    )
    return tab


def layout(user, page=None, **kwargs):
    if page == "in":
        content = page_in()
    elif page == "out":
        content = page_out()
    elif page == "setting":
        content = page_setting()
    # elif page == "pur":
    #     content = page_pur()
    elif page == "query":
        content = page_query()
    # elif page == "stockout":
    #     content = stockout_confirm()
    else:
        content = html.Div("首页")
    sidebar = create_sidebar(page, menu_items)
    appshell = dmc.AppShell(
        content,
        navbar=sidebar,
        style={"position": "fixed"},
    )
    return appshell


# @callback(
#     Output(id("table"), "dashGridOptions"),
#     Input(id("filter"), "value"),
# )
# def update_filter(filter_value):
#     if not filter_value:
#         raise PreventUpdate
#     return {"quickFilterText": filter_value}


# @callback(
#     Output(id("pur-table"), "dashGridOptions"),
#     Input(id("pur-filter"), "value"),
# )
# def pur_filter(filter_value):
#     return {"quickFilterText": filter_value}


@callback(
    Output(id("stockout-table"), "dashGridOptions"),
    Input(id("stockout-filter"), "value"),
)
def stockout_filter(filter_value):
    return {"quickFilterText": filter_value}


# @callback(
#     Output("global-notice", "children"),
#     Output(id("table"), "rowData"),
#     Output(id("filter"), "value"),
#     Output(id("submit"), "disabled"),
#     Input(id("submit"), "n_clicks"),
#     State(id("table"), "rowData"),
#     State(id("table"), "columnState"),
#     State("user", "data"),
# )
# def submit_receive_material(n_clicks, data, columns, user):
#     if not n_clicks:
#         raise PreventUpdate

#     df = pd.DataFrame(data)
#     columns = [i["colId"] for i in columns]
#     df = df.reindex(columns=columns)
#     df["incoming_qty"] = pd.to_numeric(df["incoming_qty"], errors="coerce")
#     df_isna = df.loc[df["incoming_qty"].isna()]
#     df = df.loc[df["incoming_qty"] > 0]

#     if df.empty:
#         raise PreventUpdate
#     nt_name = user.get("nt_name")
#     area = user.get("area")
#     now = datetime.now()
#     df["incoming_qty"] = df["incoming_qty"].astype(int)
#     df["remark"] = (
#         df["remark"].fillna("")
#         + f",{now:%y-%m-%d %H:%M:%S}-{nt_name}-收料"
#         + df["incoming_qty"].astype(str)
#     )
#     df["application"] = df["application"].str.lower()
#     df["deltapn"] = df["deltapn"].str.strip()

#     dfp = df.loc[df["application"] == "project"]
#     df = df.loc[~df.index.isin(dfp.index)]

#     if not dfp.empty:
#         dfp["limituse"] = dfp["dept"]
#         dfp["type"] = dfp["application"]
#         dfp["stockno"] = dfp["rd"]
#         dfp["designno"] = dfp["mat_remark"]
#         dfp["qty"] = dfp["incoming_qty"]
#         dfp["station"] = nt_name
#         dfp["label_template"] = "stock_out"
#         dfp["id"] = dfp["deltapn"] + "{" + dfp["incoming_qty"].astype(str) + "{Pur"
#         dfp["area"] = area
#         dfp["owner"] = nt_name
#         dfp["des"] = dfp["mfgpn"]
#         dfp["checkcode"] = dfp["deltapn"]
#         bg_label_print(dfp.to_json(orient="records"))

#     if not df.empty:
#         params = df["item_pur"].tolist()
#         sql = "select item_pur,received_qty,qty,pur_status,price,inv_no,po_no,pr_no \
#             from pur where item_pur in %s"

#         pur = read_sql(sql, params=[params])
#         df = df.reindex(
#             columns=df.columns.difference(
#                 [
#                     "received_qty",
#                     "qty",
#                     "pur_status",
#                     "price",
#                     "inv_no",
#                     "po_no",
#                     "pr_no",
#                 ]
#             )
#         )

#         df = df.merge(pur, on="item_pur", how="left")
#         df["received_qty"] = df["received_qty"].fillna(0).astype(int)
#         df["received_qty"] = df["received_qty"] + df["incoming_qty"]
#         df["qty"] = df["qty"].fillna(0).astype(int)

#         df["price"] = df["price"].fillna("").astype(str).str.strip().str.len()
#         df["inv_no"] = df["inv_no"].fillna("").astype(str).str.strip().str.len()
#         df["po_no"] = df["po_no"].fillna("").astype(str).str.strip().str.len()
#         df["pr_no"] = df["pr_no"].fillna("").astype(str).str.strip().str.len()

#         df["pur_status"] = np.where(
#             (df["received_qty"] >= df["qty"]),
#             "received",
#             df["pur_status"],
#         )
#         df["pur_status"] = np.where(
#             (df["received_qty"] >= df["qty"]) & (df["price"] == 0) & (df["pr_no"] == 0),
#             "closed",
#             df["pur_status"],
#         )
#         df["pur_status"] = np.where(
#             (df["received_qty"] >= df["qty"])
#             & (df["price"] > 0)
#             & (df["pr_no"] > 0)
#             & (df["inv_no"] > 0)
#             & (df["po_no"] > 0),
#             "closed",
#             df["pur_status"],
#         )
#         df = df.drop(["price", "pr_no", "inv_no", "po_no"], axis=1)
#         df["owner_m"] = nt_name
#         df["mat_receiveddate"] = datetime.now()
#         df = df.replace({np.nan: None})

#         df_plant = df.loc[df["received_qty"] >= df["qty"]]

#         params1 = df.reindex(
#             columns=[
#                 "owner_m",
#                 "received_qty",
#                 "mat_receiveddate",
#                 "pur_status",
#                 "remark",
#                 "item_pur",
#             ]
#         ).values.tolist()

#         params2 = df_plant.reindex(
#             columns=[
#                 "owner_m",
#                 "received_qty",
#                 "mat_receiveddate",
#                 "pur_status",
#                 "remark",
#                 "item_pur",
#             ]
#         ).values.tolist()

#         with pool.connection() as conn:
#             with conn.cursor() as cu:
#                 sql = "update pur set Owner_M=%s,Received_Qty=%s,Mat_ReceivedDate=%s,\
#                     Pur_Status=%s,Mat_Remark=concat_ws(',',Mat_Remark,%s) where Item_Pur=%s"
#                 cu.executemany(sql, params1)
#                 sql = "update pur_plant set Owner_M=%s,Received_Qty=%s,Mat_ReceivedDate=%s,\
#                     Pur_Status=%s,Mat_Remark=concat_ws(',',Mat_Remark,%s) where Item_Pur=%s"
#                 cu.executemany(sql, params2)
#                 conn.commit()

#         df["limituse"] = df["dept"]
#         df["type"] = df["application"]
#         df["stockno"] = df["rd"]
#         df["designno"] = df["mat_remark"]
#         df["qty"] = df["incoming_qty"]
#         df["station"] = nt_name
#         df["label_template"] = "stock_out"
#         df["id"] = df["deltapn"] + "{" + df["incoming_qty"].astype(str) + "{Pur"
#         df["area"] = area
#         df["owner"] = nt_name
#         df["des"] = df["mfgpn"]
#         df["checkcode"] = df["deltapn"]
#         bg_label_print(df.to_json(orient="records"))

#         df["prtno"] = df["prtno"].fillna("")
#         # c1 = df["application"] == "debug"
#         # c2 = df["prtno"] == ""
#         dfm = df.loc[df["prtno"] == ""]

#         if area == "SH":
#             place = "研发大楼3楼"
#         elif area == "WH":
#             place = "F2栋1楼"
#         elif area == "HZ":
#             place = "3幢2楼"
#         else:
#             place = "研发大楼3楼"

#         subject = f"您申请的材料已到，请到{place}({nt_name})处领取"

#         for rd in dfm["rd"].unique():
#             df_mail = dfm.query(f"rd=='{rd}'")
#             df_mail = df_mail.reindex(
#                 columns=["deltapn", "des", "mfgname", "mfgpn", "incoming_qty", "r_qty"]
#             )
#             df_mail.columns = [
#                 "台达料号",
#                 "描述",
#                 "厂商",
#                 "厂商料号",
#                 "到货数量",
#                 "采购数量",
#             ]
#             title = f"<b>尊敬的 {rd} 先生/小姐：</b><br>您好！<br>您申请的如下材料已到,\
#                 请到{place}({nt_name})处领取，谢谢！"
#             body = df_to_html(df_mail, title, link_text="")
#             cc = f"{nt_name}@deltaww.com"
#             to = f"{rd}@deltaww.com"
#             bg_mail(to, subject, body, cc)

#     return notice(), df_isna.to_dict(orient="records"), "", True


# @callback(
#     Output(id("submit"), "disabled"),
#     Input(id("filter"), "value"),
#     Input(id("refresh"), "n_clicks"),
#     Input(id("table"), "cellValueChanged"),
#     State(id("submit"), "disabled"),
# )
# def submit_disabled_to_enable(value, refresh, cellValueChanged, disabled):
#     if not disabled:
#         raise PreventUpdate
#     if ctx.triggered_id == id("filter"):
#         if not value:
#             raise PreventUpdate
#     return False


# @callback(
#     Output(id("table"), "rowData"),
#     Output(id("table"), "dashGridOptions"),
#     Input(id("refresh"), "n_clicks"),
#     State("user", "data"),
# )
# def refresh(n_clicks, user):
#     if not n_clicks:
#         raise PreventUpdate
#     df = get_table_data(user)
#     return df.to_dict("records"), {"quickFilterText": ""}


# @callback(
#     Output(id("stockout-table"), "rowData"),
#     Input(id("tabs"), "value"),
#     prevent_initial_call=False,
# )
# def stockout_confirm_data(value):
#     if value == "tab1":
#         sql = "select * from (select * from ssp.bom_stockout where action is null)a \
#             left join (select area,checkcode,stockno,qty as stock_qty \
#                 from ssp.stock_union_mag) b \
#                 on a.area=b.area and a.checkcode=b.checkcode"
#     else:
#         sql = "select * from ssp.bom_stockout where action is not null \
#             order by gmt_update desc limit 1000"
#     df = read_sql(sql)
#     return df.to_dict(orient="records")


@callback(
    Output("global-notice", "children"),
    Output(id("stockout-table"), "rowData"),
    Output(id("stockout-filter"), "value"),
    Input(id("stockout-submit"), "n_clicks"),
    State(id("stockout-table"), "rowData"),
    State("user", "data"),
)
def stockout_confirm_submit(n_clicks, data, user):
    if not n_clicks:
        raise PreventUpdate
    df = pd.DataFrame(data)

    if "action" not in df.columns:
        raise PreventUpdate

    nt_name = user.get("nt_name")
    area = user.get("area")
    dfx = df.loc[df["action"].isna()]
    df = df.loc[df["action"].notna()]
    df["actual_qty"] = pd.to_numeric(df["actual_qty"], errors="coerce")
    df["actual_qty"] = np.where(df["actual_qty"].isna(), df["qty"], df["actual_qty"])
    df["actual_qty"] = np.where(df["action"] == "关闭", 0, df["actual_qty"])
    df["owner"] = nt_name
    df = df.replace({np.nan: None, "": None})

    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = "update ssp.bom_stockout set action=%s,actual_qty=%s,owner=%s where id=%s"
            params = df[["action", "actual_qty", "owner", "id"]].values.tolist()
            cu.executemany(sql, params)

            df1 = df.loc[df["action"] == "出库"]
            if not df1.empty:
                c1 = df1["stock_qty"] < df1["qty"]
                c2 = df1["stock_qty"] < df1["actual_qty"]
                if (c1 | c2).any():
                    return notice("库存不足,请确认", "error"), no_update, no_update

                sql = "update ssp.stock set qty=qty-%s where id=%s"
                params = df1[["actual_qty", "stock_id"]].values.tolist()
                cu.executemany(sql, params)

                df1 = df1.reset_index()
                df1["type"] = df1["smstatus"]
                df1["qty"] = df1["actual_qty"]
                df1["qty"] = df1["actual_qty"]
                df1["station"] = df1["dept"]
                df1["label_template"] = "stock_out"
                df1["id"] = df1.index + int(f"{datetime.now():%y%m%d%H%M%S%f}")
                df1["id"] = "y" + df1["id"].astype(str)
                df1["area"] = area
                df1["owner"] = nt_name
                df1["type"] = np.where(
                    df1["deltapn"].str.startswith("287"), "mag", df1["type"]
                )
                bg_label_print(df1.to_json(orient="records"))
                task_import_uuid_to_my300(df1)
                dfm = df1.loc[df1["type"] == "mag"]
                if not dfm.empty:
                    to = f"<EMAIL>;<EMAIL>;{nt_name}@deltaww.com"
                    subject = "磁件出库通知"
                    bg_mail(to, subject, dfm.to_html())

            df1 = df.loc[df["action"] == "退库"]
            if not df1.empty:
                sql = "update ssp.stock set qty=qty-%s where id=%s"
                params = df1[["actual_qty", "stock_id"]].values.tolist()
                cu.executemany(sql, params)

                df1 = df1.reset_index()
                df1["new_stock_no"] = df1["stockno"]
                df1["qty"] = -df1["actual_qty"]
                df1["label_template"] = "stock_in"
                df1["area"] = area
                df1["owner"] = nt_name
                df1["id"] = df1.index + int(f"{datetime.now():%y%m%d%H%M%S%f}")
                bg_label_print(df1.to_json(orient="records"))
            conn.commit()

    return notice(), dfx.to_dict(orient="records"), ""


# @callback(
#     Output(id("download"), "data"),
#     Input(id("download-btn"), "n_clicks"),
#     State(id("stockout-table"), "rowData"),
# )
# def stockout_confirm_download(n_clicks, data):
#     if not n_clicks:
#         raise PreventUpdate
#     dff = pd.DataFrame(data)
#     return dcc.send_data_frame(dff.to_excel, "出库确认.xlsx")


@callback(
    Output(id("out-table"), "deleteSelectedRows"),
    Input(id("out-table"), "cellValueChanged"),
)
def delete_roll_task(changed):
    if not changed:
        raise PreventUpdate
    roll_id = changed[0]["data"]["roll_id"]

    sql = "delete from stock_roll where roll_id=%s"
    db.execute(sql, (roll_id,))
    sql = "update stockno_list set block=null where block=%s"
    db.execute(sql, (roll_id,))
    return True


@callback(
    Output(id("in-filter"), "value"),
    Input(id("in-filter"), "value"),
)
def stock_in_scan(value):
    if "{" in value:
        value = value.split("{")[0]
        return value
    else:
        raise PreventUpdate


@callback(
    Output(id("in-table"), "dashGridOptions"),
    Input(id("in-filter"), "value"),
)
def stock_in_filter(filter_value):
    patch_grid_options = Patch()
    patch_grid_options["quickFilterText"] = filter_value
    return patch_grid_options


@callback(
    Output(id("in-table"), "rowData"),
    Input(id("in-filter"), "value"),
    State(id("in-table"), "rowData"),
    State("user", "data"),
)
def stock_in_filter_empty_add_row(value, data, user):
    if not value:
        raise PreventUpdate

    area = user.get("area")
    df0 = pd.DataFrame(data)
    df0: pd.DataFrame = df0.loc[df0["area"] == area]

    if not df0.empty:
        c1 = df0["deltapn"].str.contains(value, na=False, case=False)
        c2 = df0["checkcode"].str.contains(value, na=False, case=False)
        c3 = df0["mfgpn"].str.contains(value, na=False, case=False)
        c4 = df0["po_no"].str.contains(value, na=False, case=False)
        df0 = df0.loc[c1 | c2 | c3 | c4]
        if not df0.empty:
            raise PreventUpdate

    sql = "select checkcode,deltapn,des,mfgname,mfgpn from ssp.stock \
        where deltapn like %s or checkcode like %s or mfgpn like %s limit 1"
    params = (f"%{value}%", f"%{value}%", f"%{value}%")
    df = read_sql(sql, params=params)
    if df.empty:
        sql = "select checkcode,deltapn,des,mfgname,mfgpn from ssp.pur \
            where deltapn like %s or checkcode like %s or mfgpn like %s limit 1"
        params = (f"%{value}%", f"%{value}%", f"%{value}%")
        df = read_sql(sql, params=params)
        if df.empty:
            raise PreventUpdate

    # checkcode = df["checkcode"].iloc[0]
    # sql = "SELECT area,checkcode,stockno,id as stock_id FROM stock where checkcode=%s and area=%s"
    # stock = read_sql(sql, params=[checkcode, area])
    # df = df.merge(stock, on=["checkcode"], how="left")

    df["type"] = "入库"
    df["received_qty"] = 0
    df["qty"] = 9999999
    df["application"] = "stock"
    df["area"] = area
    df["id"] = 9999999
    new_data = df.to_dict(orient="records")
    patch_data = Patch()
    patch_data.extend(new_data)
    return patch_data


@callback(
    Output(id("transfer-table"), "dashGridOptions"),
    Input(id("transfer-filter"), "value"),
)
def transfer_filter(filter_value):
    patch_grid_options = Patch()
    patch_grid_options["quickFilterText"] = filter_value
    return patch_grid_options


@callback(
    Output(id("in-table-2"), "dashGridOptions"),
    Input(id("in-filter-2"), "value"),
)
def stock_in_filter_2(filter_value):
    patch_grid_options = Patch()
    patch_grid_options["quickFilterText"] = filter_value
    return patch_grid_options


@callback(
    Output(id("in-receive"), "disabled"),
    Output(id("in-forward"), "disabled"),
    Input(id("in-table"), "selectedRows"),
)
def stock_in_disable_button(sr):
    if not sr:
        return True, True
    sr = sr[0]
    if sr.get("type") in ("收料", "入库"):
        return False, True
    elif sr.get("type") == "转寄":
        return True, False
    else:
        return True, True


# @callback(
#     Output(id("in-table"), "selectedRows"),
#     Input(id("in-table"), "selectedRows"),
#     State(id("in-table"), "rowData"),
# )
def stock_in_select_multiple_rows(selected_rows, data):
    if not selected_rows:
        raise PreventUpdate

    sr = selected_rows[-1]
    types = ("收料", "入库")
    if sr["type"] in types:
        checkcode = sr["checkcode"]
        area = sr["area"]
        df = pd.DataFrame(data)
        c1 = df["checkcode"] == checkcode
        c2 = df["area"] == area
        c3 = df["type"].isin(types)
        df = df.loc[c1 & c2 & c3]
        return df.to_dict(orient="records")
    else:
        selected_rows = [i for i in selected_rows if i["type"] == "转寄"]
        return selected_rows


@callback(
    Output(id("modal"), "opened"),
    Output(id("modal-table"), "rowData"),
    Output(id("stockno"), "value"),
    Output(id("in-qty"), "prefix"),
    Output(id("stockno"), "disabled"),
    Input(id("in-receive"), "n_clicks"),
    State(id("in-table"), "selectedRows"),
    State(id("in-table"), "rowData"),
)
def stock_in_open_modal(n_clicks, sr, data):
    if not n_clicks:
        raise PreventUpdate

    sr = sr[0]
    area: str = sr["area"]
    deltapn: str = sr["deltapn"]
    checkcode: str = sr["checkcode"]
    # mfgpn: str = sr["mfgpn"]
    if not checkcode:
        checkcode = deltapn

    df = pd.DataFrame(data)
    c1 = df["area"] == area
    c2 = df["deltapn"].str.upper() == deltapn.upper()  # 15P3M0HA04
    # c3 = df["mfgpn"].str.upper() == mfgpn.upper()
    df = df.loc[c1 & c2]

    sql = "select distinct checkcode from ssp_csg.mat_info where deltapn=%s"
    res = db.execute_fetchone(sql, [deltapn])
    if res:
        if checkcode != res["checkcode"]:
            checkcode = res["checkcode"]

    df["checkcode"] = checkcode

    stockno = None
    stockno_disabled = True
    if not stockno:
        if (df["type"] == "入库").any():
            stockno_disabled = False

    sql = "SELECT type as stockno_type,stockno from stockno_list \
        where stock_id=(SELECT id FROM stock WHERE AREA=%s AND checkcode=%s)"
    params = [area, checkcode]
    res = db.execute(sql, params)
    if res:
        stockno_type = {i["stockno_type"] for i in res}
        if "电子料架" in stockno_type:
            not_e_shelf = [i for i in res if i["stockno_type"] != "电子料架"]
            if not_e_shelf:
                stockno = not_e_shelf[0]["stockno"]
            else:
                e_shelf = [i for i in res if i["stockno_type"] == "电子料架"]
                stockno = e_shelf[0]["stockno"]
                stockno_disabled = False
        else:
            stockno = res[0]["stockno"]
    # TODO:stockno_list添加唯一约束，保证散料库位唯一，stock_id+uid,uid要改为非null才行，相应的电子料架sql语句也要改

    # ---------项目状态--------------
    df["smstatus"] = ""
    dfp = df.loc[df["application"] == "project"]
    if not dfp.empty:
        prtnos = df["prtno"].unique().tolist()
        sql = "select prtno,smstatus as smstatus_new from ssp.prt where prtno in %s"
        params = [tuple(prtnos)]
        df_prt = read_sql(sql, params=params)
        df_prt["smstatus_new"] = df_prt["smstatus_new"].str.lower()
        df = df.merge(df_prt, how="left", on="prtno")
        df["smstatus"] = np.where(
            df["smstatus_new"].notna(), df["smstatus_new"], df["smstatus"]
        )
        c1 = df["application"] == "project"
        c2 = df["smstatus"] == "close"
        df["type"] = np.where(c1 & c2, "收料", df["type"])

    df["application"] = (
        df["application"]
        .astype("category")
        .cat.set_categories(["project", "debug", "stock"])
    )
    df = df.sort_values(by=["application", "start_date"])
    data = df.to_dict("records")
    return True, data, stockno, [], stockno_disabled


@callback(
    Output(id("in-table"), "rowTransaction"),
    Input(id("modal"), "opened"),
    State(id("modal-table"), "rowData"),
)
def stock_in_open_modal_delete_data(opened, data):
    if not opened:
        raise PreventUpdate

    ids = [{"id": i["id"]} for i in data]
    return {"remove": ids}


@callback(
    Output(id("in-table"), "deleteSelectedRows"),
    Input(id("in-forward"), "n_clicks"),
    State(id("in-table"), "selectedRows"),
    State("user", "data"),
)
def forward_submit(n_clicks, data, user):
    if not n_clicks:
        raise PreventUpdate
    nt_name = user.get("nt_name")
    now = f"{datetime.now():%y-%m-%d %H:%M:%S}"

    df = pd.DataFrame(data)
    if (df["type"] != "转寄").any():
        msg = fac.Message(content="转寄时,只能选择转寄记录", type="error")
        set_props("msg", {"children": msg})
        return no_update

    df["remark"] = f"{nt_name}-{now}-转寄"
    sql = "update ssp.pur set mat_remark=concat_ws(',',mat_remark,%s) where id=%s"
    params = df[["remark", "pur_id"]].values.tolist()
    db.execute_many(sql, params)
    df["station"] = nt_name
    df["label_template"] = "stock_out"
    df["designno"] = "转寄" + df["area"]
    df["area"] = user.get("area")
    df["owner"] = nt_name
    bg_label_print(df.to_json(orient="records"))
    msg = fac.Message(content="转寄成功", type="success")
    set_props("msg", {"children": msg})
    return True


@callback(
    Output(id("stockno-type"), "options"),
    Output(id("stockno-category"), "options"),
    Output(id("stockno-type"), "value"),
    Output(id("stockno-category"), "value"),
    Input(id("stockno-packing"), "value"),
    State("user", "data"),
)
def stockno_type(packing, user):
    if not packing:
        raise PreventUpdate
    if packing == "项目库位":
        return ["项目库位"], ["项目库位"], "项目库位", "项目库位"
    area = user.get("area")

    sql = "select distinct type from ssp.stockno_list \
        where packing=%s and area=%s and stock_id is null"
    params = [packing, area]
    res = db.execute(sql, params)
    options = [i["type"] for i in res]
    return options, [], None, None


@callback(
    Output(id("stockno-category"), "options"),
    Output(id("stockno-category"), "value"),
    Input(id("stockno-type"), "value"),
    State(id("stockno-packing"), "value"),
    State("user", "data"),
)
def stockno_category(type, packing, user):
    if not (type and packing):
        raise PreventUpdate
    if type == "项目库位":
        raise PreventUpdate
    area = user.get("area")

    sql = "select distinct category from ssp.stockno_list \
        where type=%s and packing=%s and area=%s and stock_id is null"
    params = [type, packing, area]
    res = db.execute(sql, params)
    options = [i["category"] for i in res]
    return options, None


@callback(
    Output(id("stockno"), "value"),
    Input(id("stockno-category"), "value"),
    State(id("stockno-type"), "value"),
    State(id("stockno-packing"), "value"),
    State("user", "data"),
)
def stockno_select(category, type, packing, user):
    if not (type and category and packing):
        raise PreventUpdate
    if category == "项目库位":
        return "项目库位"

    area = user.get("area")

    sql = "select * from ssp.stockno_list \
        where packing=%s and type=%s and category=%s and area=%s \
        and stock_id is null"
    params = [packing, type, category, area]
    stockno = db.execute_fetchone(sql, params)
    if not stockno:
        raise PreventUpdate
    return stockno.get("stockno")


@callback(
    Output(id("in-qty"), "value"),
    Output(id("in-qty"), "prefix"),
    Input(id("in-qty"), "focusing"),
    State(id("in-qty"), "value"),
    State(id("in-qty"), "prefix"),
)
def focusing_update_prefix(focusing, value, prefix):
    if focusing:
        raise PreventUpdate
    if not value:
        raise PreventUpdate
    uid = str(uuid4())
    prefix.append(
        fac.Tag(
            content=value,
            color="gray",
            closeIcon=True,
            id={"type": id("qty-tag"), "index": uid},
        )
    )
    return None, prefix


@callback(
    Output(id("in-qty"), "value"),
    Output(id("in-qty"), "prefix"),
    Input(id("in-qty"), "nSubmit"),
    State(id("in-qty"), "value"),
    State(id("in-qty"), "prefix"),
)
def stock_in_qty_update_prefix(nSubmit, value, prefix):
    if not nSubmit:
        raise PreventUpdate
    uid = str(uuid4())
    prefix.append(
        fac.Tag(
            content=value,
            color="gray",
            closeIcon=True,
            id={"type": id("qty-tag"), "index": uid},
        )
    )
    return None, prefix


@callback(
    Output({"type": id("qty-tag"), "index": MATCH}, "key"),
    Input({"type": id("qty-tag"), "index": MATCH}, "closeCounts"),
    State(id("in-qty"), "prefix"),
)
def stock_in_qty_remove_prefix(closeCounts, prefix):
    idx = ctx.triggered_id["index"]
    prefix = [i for i in prefix if i["props"]["id"]["index"] != idx]
    set_props(id("in-qty"), {"prefix": prefix})
    return no_update


@callback(
    Output(id("modal-table"), "rowData"),
    Input(id("in-qty"), "prefix"),
    State(id("modal-table"), "rowData"),
)
def stock_in_qty_update_table_data(qty, data):
    if not qty:
        for i in data:
            i["new_received_qty"] = None
        return data

    qty = sum(int(i["props"]["content"]) for i in qty)
    df = pd.DataFrame(data)
    df["received_qty"] = df["received_qty"].fillna(0).astype(int)
    df["new_qty"] = df["qty"] - df["received_qty"]
    df["cumsum"] = df["new_qty"].cumsum()
    df["new_received_qty"] = np.where(
        df["cumsum"] <= qty, df["new_qty"], qty - (df["cumsum"] - df["new_qty"])
    )
    df["remaining_qty"] = 0
    r_qty = qty - df["new_qty"].sum()
    if r_qty > 0:
        df.loc[df.index.max(), "new_received_qty"] += r_qty
        df.loc[df.index.max(), "remaining_qty"] = r_qty
    # if remaining_qty > 0:
    #     dfs = df.loc[df["type"] == "入库"]
    #     if dfs.empty:
    #         dfx = df.tail(1)
    #         dfx["new_received_qty"] = remaining_qty
    #         dfx["type"] = "入库"
    #         dfx["application"] = "stock"
    #         dfx["received_qty"] = 0
    #         dfx["qty"] = 0
    #         df = pd.concat([df, dfx], axis=0)
    #         df = df.reset_index(drop=True)
    #     else:
    #         df.loc[dfs.index.max(), "new_received_qty"] += remaining_qty

    df["new_received_qty"] = np.where(
        df["new_received_qty"] < 0, 0, df["new_received_qty"]
    )
    return df.to_dict("records")


@callback(
    Output(id("modal"), "opened"),
    Output(id("in-qty"), "prefix"),
    Input(id("in-submit"), "nClicks"),
    State(id("in-qty"), "prefix"),
    State(id("modal-table"), "rowData"),
    State(id("stockno"), "value"),
    State("user", "data"),
)
def stock_in_submit(clicks, qty, data, stockno, user):
    if not clicks:
        raise PreventUpdate

    if not qty:
        set_props(
            "msg",
            {"children": fac.Message(content="请输入来料数量", type="error")},
        )
        return no_update

    qty = [int(i["props"]["content"]) for i in qty]

    df = pd.DataFrame(data)
    c1 = df["type"] == "入库"
    c2 = df["new_received_qty"] > 0
    type_stock = (c1 & c2).any()
    if type_stock:
        if not stockno:
            set_props(
                "msg",
                {"children": fac.Message(content="入库需选择库位号", type="error")},
            )
            return no_update

    now = datetime.now()
    nt_name = user.get("nt_name").title()
    df["prtno"] = df["prtno"].fillna("")
    df["deltapn"] = df["deltapn"].str.strip()
    df["remark"] = f"{now:%y-%m-%d %H:%M:%S}-{nt_name}-收料" + df[
        "new_received_qty"
    ].astype(str)

    # -------采购状态处理--------
    df["new_received_qty"] = df["new_received_qty"].fillna(0).astype(int)
    df["received_qty"] = df["received_qty"].fillna(0).astype(int)
    c1 = df["new_received_qty"] > 0
    df["received_qty"] = np.where(
        c1, df["received_qty"] + df["new_received_qty"], df["received_qty"]
    )
    df["remark"] = np.where(
        c1,
        f"{now:%y-%m-%d %H:%M:%S}-{nt_name}-收料" + df["new_received_qty"].astype(str),
        "",
    )

    df["qty"] = df["qty"].fillna(0).astype(int)

    df["price"] = df["price"].fillna("").astype(str).str.strip().str.len()
    df["inv_no"] = df["inv_no"].fillna("").astype(str).str.strip().str.len()
    df["po_no"] = df["po_no"].fillna("").astype(str).str.strip().str.len()
    df["pr_no"] = df["pr_no"].fillna("").astype(str).str.strip().str.len()

    c1 = df["received_qty"] >= df["qty"]
    df["pur_status"] = np.where(c1, "received", df["pur_status"])

    c2 = df["price"] == 0
    c3 = df["pr_no"] == 0
    df["pur_status"] = np.where(c1 & c2 & c3, "closed", df["pur_status"])

    c4 = df["price"] > 0
    c5 = df["pr_no"] > 0
    c6 = df["inv_no"] > 0
    c7 = df["po_no"] > 0
    df["pur_status"] = np.where(c1 & c4 & c5 & c6 & c7, "closed", df["pur_status"])

    df["now"] = now
    df["nt_name"] = nt_name
    df["owner"] = nt_name
    df["nt_area"] = user.get("area")
    df = df.replace({np.nan: None})

    # --------入库--------- 3072499820
    df["stockno"] = stockno
    stock_id = None
    if stockno:
        if type_stock:
            if stockno == "项目库位":
                df["stockno"] = df["prtno"]
                stock_in_label(qty, df)
            else:
                stock_id = receive_stock(qty, stockno, df)

    # ----------收料-----------
    df["stock_id"] = stock_id
    for i in df.itertuples():
        if i.new_received_qty > 0:
            if stock_id:
                if (i.application == "project") or (i.type == "收料"):
                    update_stock_qty(i)

            if i.item_pur:
                update_pur(i)

            if i.type == "收料":
                receive_debug(i)

            elif i.type == "入库":
                if i.application == "project":
                    receive_project(i)

    set_props(
        "msg",
        {"children": fac.Message(content="提交成功", type="success")},
    )
    return False, []


@callback(
    Output("msg", "children"),
    Input(id("in-label-2"), "n_clicks"),
    State(id("in-table-2"), "selectedRows"),
    State("user", "data"),
)
def stock_in_tab2_print_label(n_clicks, data, user):
    if not n_clicks:
        raise PreventUpdate
    nt_name = user.get("nt_name")
    df = pd.DataFrame(data)
    if df.empty:
        raise PreventUpdate

    prtnos = df["prtno"].unique().tolist()
    sql = "select prtno,checkcode,group_concat(designno) AS designno \
        from smbom where prtno IN %s GROUP BY prtno,checkcode"
    params = (prtnos,)
    df1 = read_sql(sql, params=params)

    sql = "select prtno,qty as sets from prt where prtno IN %s"
    params = (prtnos,)
    df2 = read_sql(sql, params=params)

    df = df.merge(df1, on=["prtno", "checkcode"], how="left").merge(
        df2, on="prtno", how="left"
    )
    df["designno"] = df["designno"].fillna("")
    df["qpa"] = np.where(df["designno"] == "", 0, df["designno"].str.count(",") + 1)
    df["station"] = nt_name
    df["label_template"] = "stock_out"
    df["area"] = user.get("area")
    df["owner"] = nt_name
    df["shortage"] = "缺料"
    df["id"] = df.index + int(f"{datetime.now():%y%m%d%H%M%S%f}")
    df["id"] = "b" + df["id"].astype(str)
    task_import_uuid_to_my300(df)
    bg_label_print(df.to_json(orient="records"))
    return fac.Message(content="打印提交成功", type="success")


@callback(
    Output("msg", "children"),
    Output(id("in-table-2"), "rowData"),
    Input(id("in-receive-2"), "n_clicks"),
    State(id("in-table-2"), "rowData"),
    State("user", "data"),
)
def stock_in_tab2_close_pur(n_clicks, data, user):
    if not n_clicks:
        raise PreventUpdate

    nt_name = user.get("nt_name")
    now = datetime.now()
    df = pd.DataFrame(data)

    if "incoming_qty" not in df.columns:
        raise PreventUpdate

    dfna = df.loc[df["incoming_qty"].isna()]
    df = df.loc[df["incoming_qty"].notna()]
    if df.empty:
        raise PreventUpdate

    df["incoming_qty"] = df["incoming_qty"].astype(int)
    df["received_qty"] = df["received_qty"].fillna(0).astype(int)
    df["owner_m"] = nt_name
    df["received_qty"] = df["received_qty"] + df["incoming_qty"]
    df["mat_receiveddate"] = now

    df["remark"] = (
        df["remark"].fillna("")
        + f",{now:%y-%m-%d %H:%M:%S}-{nt_name}-收料"
        + df["incoming_qty"].astype(str)
    )
    df["pur_status"] = np.where(
        df["received_qty"] >= df["qty"], "closed", df["pur_status"]
    )

    sql = "update pur set owner_m=%s,received_qty=%s,\
        mat_receiveddate=%s,pur_status=%s,Mat_Remark=concat_ws(',',Mat_Remark,%s) \
        where id=%s"
    params = df[
        [
            "owner_m",
            "received_qty",
            "mat_receiveddate",
            "pur_status",
            "remark",
            "id",
        ]
    ].values.tolist()
    db.execute_many(sql, params)

    # for i in df.itertuples():
    #     db.insert(
    #         "ssp.stockout",
    #         {
    #             "stockoutdate": now,
    #             "dept": i.dept,
    #             "type": "SM",
    #             "prtno": i.prtno,
    #             "deltapn": i.deltapn,
    #             "qty": i.received_qty,
    #             "owner1": nt_name,
    #             "area": i.dest_area,
    #             "source": "short",
    #             "checkcode": i.checkcode,
    #             "dept_id": i.dept_id,
    #             "memo": "short",
    #             "prt_id": i.prt_id,
    #             "bom_id": i.bom_id,
    #             "stockno": i.prtno,
    #         },
    #     )
    notice = fac.Message(content="关闭采购提交成功", type="success")
    return notice, dfna.to_dict("records")


@callback(
    Output(id("stock-uid-table"), "rowData"),
    Input(id("search"), "debounceValue"),
    Input(id("relabel-option"), "value"),
    State("user", "data"),
)
def stock_relabel_search_record(value, option, user):
    if not value:
        raise PreventUpdate
    area = user.get("area")
    if option == "相同唯一码":
        sql = "select * from stock_uid \
            where area=%s and (uid like %s or checkcode like %s or stockno like %s)"
        params = [area, "%" + value + "%", "%" + value + "%", "%" + value + "%"]
    else:
        sql = "select * from stock_uid \
            where area=%s and (uid like %s or checkcode like %s or stockno like %s) limit 1"
        params = [area, "%" + value + "%", "%" + value + "%", "%" + value + "%"]
    df = read_sql(sql, params=params)
    df["action"] = option
    df = df.sort_values("id", ascending=False)
    return df.to_dict("records")


@callback(
    Output("msg", "children"),
    Output(id("stock-uid-table"), "deleteSelectedRows"),
    Output(id("search"), "value"),
    Input(id("relabel-submit"), "nClicks"),
    State(id("stock-uid-table"), "selectedRows"),
    State(id("relabel-option"), "value"),
    State("user", "data"),
)
def stock_relabel_submit(nClicks, data, option, user):
    if not nClicks:
        raise PreventUpdate
    if not data:
        return (
            fac.Message(content="请选择要操作的记录", type="error"),
            no_update,
            no_update,
        )

    df = pd.DataFrame(data)
    d = data[0]
    stock_id = d.get("stock_id")

    stock = db.find_one("stock", {"id": stock_id})
    df["id"] = df["uid"]
    df["des"] = stock.get("des")
    df["mfgname"] = stock.get("mfgname")
    df["mfgpn"] = stock.get("mfgpn")
    df["stockno"] = stock.get("stockno")
    df["limituse"] = stock.get("limituse")
    df["label_template"] = "stock_in"
    df["owner"] = user.get("nt_name")
    df["new_stock_no"] = df["stockno"]

    if option == "新建唯一码":
        uid = f"y{datetime.now():%y%m%d%H%M%S%f}"
        db.insert(
            "stock_uid",
            {
                "uid": uid,
                "stock_id": d["stock_id"],
                "checkcode": d["checkcode"],
                "stockno": d["stockno"],
                "area": d["area"],
                "qty": d["qty"],
            },
        )
        df["id"] = uid
        task_import_uuid_to_my300(df)
    bg_label_print(df.to_json(orient="records"))
    return fac.Message(content="提交成功", type="success"), [], None


@callback(
    Output(id("out-table"), "deleteSelectedRows"),
    Input(id("out-table"), "cellClicked"),
)
def out_table_delete_selected_rows(clicked):
    if not clicked:
        raise PreventUpdate
    if clicked.get("colId") != "action":
        raise PreventUpdate
    return True


@callback(
    Output(id("bulk-table"), "deleteSelectedRows"),
    Input(id("bulk-table"), "cellClicked"),
)
def bluk_table_delete_selected_rows(clicked):
    if not clicked:
        raise PreventUpdate
    if clicked.get("colId") != "action":
        raise PreventUpdate
    return True


# @callback(
#     Output(id("out-table"), "rowData"),
#     Input(id("roll-segment"), "value"),
#     prevent_initial_call=False,
# )
# def out_table_data(value):
#     if value == "ongoing":
#         sql = "select * from ssp.stock_roll where finished_date is null \
#             group by roll_id order by roll_id desc"
#     else:
#         sql = "select * from ssp.stock_roll where finished_date is not null \
#             group by roll_id order by roll_id desc"
#     df = read_sql(sql)
#     df["roll_id"] = df["roll_id"].astype(str)
#     df["action"] = df.apply(
#         lambda x: f"**[备料](/stock/roll?batch={x['roll_id']}&placer={x['placer']}&prtno={x['prtno']})**",
#         axis=1,
#     )
#     return df.to_dict("records")


def roll_data_table(segment, area):
    if area != "SH":
        return fac.Empty()
    if segment == "ongoing":
        sql = "select * from ssp.stock_roll where finished_date is null \
            group by roll_id order by roll_id desc"
    else:
        sql = "select * from ssp.stock_roll where finished_date is not null \
            group by roll_id order by roll_id desc"
    df = read_sql(sql)
    df["roll_id"] = df["roll_id"].astype(str)
    df["action"] = df.apply(
        lambda x: f"**[备料](/stock/roll?batch={x['roll_id']}&placer={x['placer']}&prtno={x['prtno']})**",
        axis=1,
    )
    table = dag.AgGrid(
        id=id("out-table"),
        className="ag-theme-quartz",
        columnDefs=[
            {
                "field": "action",
                "headerName": "备料",
                "cellRenderer": "markdown",
                "linkTarget": "_blank",
                "width": 100,
            },
            {"field": "prtno", "headerName": "项目号", "width": 200},
            {"field": "placer", "headerName": "贴片机", "width": 150},
            {"field": "applicant", "headerName": "申请人", "width": 150},
            {
                "field": "date",
                "headerName": "申请日",
                "valueGetter": {
                    "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')\
                    (params.data.date)"
                },
                "valueFormatter": {
                    "function": "d3.timeFormat('%m/%d %H:%M:%S')(params.value)"
                },
                "width": 200,
            },
            {"field": "operator", "headerName": "作业人", "width": 150},
            {
                "field": "delete",
                "headerName": "删除",
                "cellEditor": {"function": "DMC_Select"},
                "cellEditorParams": {
                    "options": ["删除"],
                    "clearable": True,
                    "shadow": "xl",
                },
                "cellEditorPopup": True,
                "singleClickEdit": True,
                "width": 100,
                "editable": True,
            },
        ],
        rowData=df.to_dict("records"),
        # columnSize="autoSize",
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
        },
        style={"height": 450},
    )
    return table


def bulk_data_table(segment, area):
    if area != "SH":
        return fac.Empty()
    if segment == "ongoing":
        sql = "select distinct prtno from stockout where type=%s and lable is null and area=%s"
        params = ["sm", area]
        dfp = read_sql(sql, params=params)
        # c1 = dfp["stockno"].str.match(r"^[A-Z]\d{4}.*", na=False)
        # c2 = dfp["stockno"].str.match(r"^\d{7}$", na=False)
        # dfp = dfp.loc[~(c1 | c2)]

        prtnos = dfp["prtno"].to_list()
        sql = "select dept,prtno,proj,mat1_date,smstatus,startdate_sch,\
            pcbstatus,placer,pcbpn,qty from ssp.prt \
            where (mat1_date is null and (smd_area=%s or dip_area=%s) \
            and (b_ee in %s and b_me in %s and b_mag in %s)) or (prtno in %s)"
        params = [area, area, ["X", "NA"], ["X", "NA"], ["X", "NA"], prtnos]
        df = read_sql(sql, params=params)

        # sql = "select dept,prtno,proj,mat1_date,smstatus,startdate_sch,\
        #     pcbstatus,placer,pcbpn,qty from ssp.prt where prtno in %s"
        # params = [prtnos]
        # df = read_sql(sql, params=params)
    else:
        sql = "select dept,prtno,proj,mat1_date,smstatus,startdate_sch,\
            pcbstatus,placer,pcbpn,qty from ssp.prt \
            where  mat1_date is not null order by mat1_date desc limit 5000"
        df = read_sql(sql)

    df["pcb"] = pd.to_datetime(df["pcbstatus"], errors="coerce")
    df["aux1"] = np.where(df["pcb"].dt.date <= pd.Timestamp.now().date(), 1, 0)
    df["pcb"] = df["pcb"].dt.strftime("%m/%d")
    df["pcbstatus"] = np.where(df["pcb"].isna(), df["pcbstatus"], df["pcb"])

    df["smstatus"] = df["smstatus"].str.lower()
    df["smstatus"] = df["smstatus"].replace(
        {
            "planok": "已排定",
            "progok": "已编程",
            "planning": "待排定",
            "smtstart": "贴片开始",
            "smtfinish": "贴片结束",
            "dipstart": "插件开始",
            "fsfinish": "首样完成",
            "cancel": "样制取消",
            "close": "样制结束",
        }
    )
    df["smstatus"] = (
        df["smstatus"]
        .astype("category")
        .cat.set_categories(
            [
                "样制取消",
                "样制结束",
                "首样完成",
                "插件开始",
                "贴片结束",
                "贴片开始",
                "已编程",
                "已排定",
                "待排定",
            ]
        )
    )
    df["action"] = df.apply(
        lambda x: f"**[备料](/stock/bulk?prtno={x['prtno']}&placer={x['placer']}&mat=bulk)**",
        axis=1,
    )
    df["order"] = np.where(df["mat1_date"].notna(), 1, 2)
    df = df.sort_values(by=["order", "startdate_sch", "smstatus"])

    table = dag.AgGrid(
        id=id("bulk-table"),
        className="ag-theme-quartz",
        columnDefs=[
            {
                "field": "action",
                "headerName": "",
                "cellRenderer": "markdown",
                "linkTarget": "_blank",
                "width": 60,
                "pinned": "left",
            },
            {"field": "dept", "headerName": "部门", "width": 100},
            {"field": "prtno", "headerName": "项目号", "width": 150},
            {"field": "proj", "headerName": "机种名", "width": 250},
            {"field": "smstatus", "headerName": "状态", "width": 100},
            {
                "field": "pcbstatus",
                "headerName": "PCB状态",
                "width": 110,
                "cellStyle": {
                    "styleConditions": [
                        {
                            "condition": "params.data.aux1==1",
                            "style": {"backgroundColor": "#66c2a5"},
                        },
                    ]
                },
            },
            {"field": "placer", "headerName": "贴片机", "width": 100},
            {"field": "pcbpn", "headerName": "PCB料号", "width": 120},
            {"field": "qty", "headerName": "数量", "width": 80},
            {
                "field": "startdate_sch",
                "headerName": "计划开始",
                "valueGetter": {
                    "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')\
                    (params.data.startdate_sch)"
                },
                "valueFormatter": {
                    "function": "params.value?d3.timeFormat('%m/%d')(params.value):''"
                },
                "width": 90,
            },
            {
                "field": "mat1_date",
                "headerName": "完成日期",
                "valueGetter": {
                    "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')\
                    (params.data.mat1_date)"
                },
                "valueFormatter": {
                    "function": "params.value?d3.timeFormat('%m/%d')(params.value):''"
                },
                "width": 90,
            },
        ],
        rowData=df.to_dict("records"),
        # columnSize="autoSize",
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
        },
        getRowStyle={
            "styleConditions": [
                {
                    "condition": "params.data.mat1_date!=null",
                    "style": {"backgroundColor": "sandybrown"},
                }
            ]
        },
        style={"height": 450},
    )
    return table


def dip_data_table(segment, area):
    if area == "SH":
        return fac.Empty()

    if segment == "ongoing":
        sql = "select dept,prtno,proj,mat1_date,smstatus,startdate_sch \
            from ssp.prt where dip_area=%s and mat_dip_date is null"
        params = [area]
    else:
        sql = "select dept,prtno,proj,mat1_date,smstatus,startdate_sch \
            from ssp.prt where dip_area=%s and mat_dip_date is not null \
            order by mat_dip_date desc limit 5000"
        params = [area]
    df = read_sql(sql, params=params)

    df["smstatus"] = df["smstatus"].str.lower()
    df["smstatus"] = df["smstatus"].replace(
        {
            "smtstart": "贴片开始",
            "smtfinish": "贴片完成",
            "dipstart": "插件开始",
            "fsfinish": "首样完成",
            "progok": "已编程",
            "planok": "已排定",
            "planning": "待排定",
        }
    )
    df["action"] = df.apply(
        lambda x: f"**[备料](/stock/bulk?prtno={x['prtno']}&mat=dip)**", axis=1
    )
    df = df.sort_values(by="smstatus")
    table = dag.AgGrid(
        className="ag-theme-quartz",
        columnDefs=[
            {
                "field": "action",
                "headerName": "备料",
                "cellRenderer": "markdown",
                "linkTarget": "_blank",
                "width": 100,
            },
            {"field": "dept", "headerName": "部门"},
            {"field": "prtno", "headerName": "项目号"},
            {"field": "proj", "headerName": "机种名", "width": 200},
            {"field": "smstatus", "headerName": "状态"},
            {
                "field": "startdate_sch",
                "headerName": "计划开始",
                "valueGetter": {
                    "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')\
                    (params.data.startdate_sch)"
                },
                "valueFormatter": {
                    "function": "params.value?d3.timeFormat('%m/%d')(params.value):''"
                },
            },
        ],
        rowData=df.to_dict("records"),
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
        },
        style={"height": 450},
    )
    return table


@callback(
    Output(id("stock-in-content"), "children"),
    Input(id("in-tabs"), "value"),
    State("user", "data"),
    prevent_initial_call=False,
)
def stock_in_tabs(active, user):
    if active == "1":
        div = stock_in_tab1(user)
    else:
        div = stock_in_tab2(user)
    return div


@callback(
    Output(id("out-content"), "children"),
    Input(id("out-tabs"), "value"),
    Input(id("roll-segment"), "value"),
    State("user", "data"),
    prevent_initial_call=False,
)
def stock_out_tabs(active, segment, user):
    area = user.get("area")
    if active == "1":
        table = roll_data_table(segment, area)
    elif active == "2":
        table = bulk_data_table(segment, area)
    elif active == "3":
        table = dip_data_table(segment, area)
    elif active == "4":
        table = stockout_confirm_table()
    else:
        raise PreventUpdate
    return table


# @cache.memoize(expire=24 * 3600)
def stock_clean(user: dict):
    sql = "select * from ssp.stock_clean where owner=%s and end_date is null"
    df = read_sql(sql, params=[user.get("nt_name")])
    if df.empty:
        disabled = False
    else:
        disabled = True

    table = dag.AgGrid(
        id=id("stock-clean-table"),
        className="ag-theme-quartz",
        columnSize="autoSize",
        columnDefs=[
            {"field": "checkcode", "headerName": "台达料号"},
            {"field": "stockno", "headerName": "库位号"},
            {"field": "des", "headerName": "描述"},
            {"field": "mfgname", "headerName": "厂商"},
            {"field": "mfgpn", "headerName": "厂商料号"},
            {"field": "qty", "headerName": "数量"},
            {"field": "laststockoutdate", "headerName": "最近出库日期"},
            {"field": "lastpurdate", "headerName": "最近采购日期"},
            {"field": "laststockindate", "headerName": "最近入库日期"},
            {"field": "stock_id", "headerName": "stock_id", "hide": True},
            {"field": "uid", "headerName": "uid"},
            {"field": "check_date", "headerName": "check_date"},
        ],
        rowData=df.to_dict("records"),
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
        },
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        getRowId="params.data.uid",
        getRowStyle={
            "styleConditions": [
                {
                    "condition": "params.data['check_date']!=null",
                    "style": {"backgroundColor": "#3D9970"},
                },
            ]
        },
        style={"height": 350},
    )
    div = fac.Flex(
        [
            fac.Flex(
                [
                    fac.Button(
                        "下载清单",
                        id=id("download-clean-btn"),
                        style={"background": "orange"},
                        disabled=disabled,
                    ),
                    fac.Upload(
                        apiUrl="/upload/",
                        buttonContent="上传清单",
                        buttonProps={"danger": True},
                        id=id("upload-clean"),
                        showUploadList=False,
                        disabled=disabled,
                    ),
                    fac.Switch(
                        id=id("light"),
                        checkedChildren="开灯",
                        unCheckedChildren="关灯",
                        checked=None,
                    ),
                    fac.Button("确认清库", id=id("clean-submit"), type="primary"),
                ],
                justify="space-between",
                align="center",
            ),
            fac.Input(
                id=id("check-uid"),
                addonBefore="扫码核对",
                placeholder="激活此输入框进行扫码核对",
            ),
            DashPlayer(
                id=id("fail-audio"),
                url="/assets/fail.mp3",
                playing=False,
                controls=True,
                style={"display": "none"},
            ),
            table,
        ],
        vertical=True,
        gap="small",
    )
    return div


def stock_location(user: dict):
    sql = "select a.*,b.checkcode from ssp.stockno_list a \
        left join stock b on a.stock_id=b.id where a.area=%s"
    df = read_sql(sql, params=[user.get("area")])
    # sql = "select * from ssp.stockno_list where area=%s"
    df = read_sql(sql, params=[user.get("area")])
    df = df.reset_index()
    df = df.sort_values(by=["gmt_create"], ascending=False)
    type_options = df["type"].unique().tolist()
    cat_options = df["category"].unique().tolist()
    pack_options = df["packing"].unique().tolist()

    table = dag.AgGrid(
        id=id("location-table"),
        className="ag-theme-quartz",
        columnSize="sizeToFit",
        columnDefs=[
            {
                "field": "id",
                "headerName": "id",
                "checkboxSelection": True,
                "width": 110,
            },
            {"field": "stockno", "headerName": "库位号", "editable": True},
            {
                "field": "type",
                "headerName": "分类",
                "cellEditor": "agSelectCellEditor",
                "cellEditorParams": {"values": type_options},
                "editable": True,
            },
            {
                "field": "category",
                "headerName": "架别",
                "cellEditor": "agSelectCellEditor",
                "cellEditorParams": {"values": cat_options},
                "editable": True,
            },
            {
                "field": "packing",
                "headerName": "材料包装方式",
                "cellEditor": "agSelectCellEditor",
                "cellEditorParams": {"values": pack_options},
                "editable": True,
            },
            {"field": "checkcode", "headerName": "绑定材料"},
        ],
        getRowId="params.data.index",
        rowData=df.to_dict("records"),
        dashGridOptions={
            "rowSelection": "multiple",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
        },
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        style={"height": "70vh"},
    )
    div = fac.Flex(
        [
            fac.Flex(
                [
                    fac.Button(
                        "添加库位",
                        id=id("add-location"),
                        # id=id("download-inventory"),
                        style={"background": "green"},
                        # disabled=disabled,
                    ),
                    fac.Popconfirm(
                        fac.Button(
                            "更新库位",
                            # id=id("download-inventory"),
                            style={"background": "orange"},
                            # disabled=disabled,
                        ),
                        title="确认更新所选库位?",
                        id=id("update-location"),
                    ),
                    fac.Popconfirm(
                        fac.Button(
                            "删除库位",
                            # id=id("download-inventory"),
                            style={"background": "red"},
                            # disabled=disabled,
                        ),
                        title="确认删除所选库位?",
                        id=id("remove-location"),
                    ),
                ],
                justify="space-between",
                align="center",
            ),
            table,
        ],
        vertical=True,
        gap="small",
    )
    return div


def stock_inventory(user: dict):
    sql = "select * from ssp.stock where area=%s"
    df = read_sql(sql, params=[user.get("area")])
    df.columns = df.columns.str.lower()
    sql = "SELECT stock_id as id,MAX(DATE) AS inventory_date \
        FROM stock_inventory GROUP BY stock_id"
    df1 = read_sql(sql)
    # return fac.Empty()
    sql = "select area,checkcode,sum(qty) as pending_qty from stockout \
        where stockoutdate2 is null group by area,checkcode"
    df2 = read_sql(sql)
    df = df.merge(df1, how="left", on="id").merge(
        df2, how="left", on=["area", "checkcode"]
    )
    df["pending_qty"] = df["pending_qty"].fillna(0)
    df["inventory_date"] = pd.to_datetime(df["inventory_date"]).dt.date
    # df = df.sort_values(by=["inventory_date"], ascending=False)
    table = dag.AgGrid(
        id=id("inventory-table"),
        className="ag-theme-quartz",
        columnSize="sizeToFit",
        columnDefs=[
            {
                "field": "checkcode",
                "headerName": "台达料号",
                "checkboxSelection": True,
                "headerCheckboxSelection": True,
                "headerCheckboxSelectionFilteredOnly": True,
                "width": 250,
            },
            {"field": "stockno", "headerName": "库位号"},
            {"field": "des", "headerName": "描述"},
            {"field": "mfgname", "headerName": "厂商"},
            {"field": "mfgpn", "headerName": "厂商料号"},
            {"field": "qty", "headerName": "数量"},
            {"field": "pending_qty", "headerName": "待出库"},
            {
                "field": "inventory_date",
                "headerName": "最近盘点日期",
                "sort": "desc",
                "filter": "agDateColumnFilter",
                "filterValueGetter": {
                    "function": "d3.timeParse('%Y-%m-%d')(params.data.inventory_date)"
                },
                "width": 180,
            },
        ],
        rowData=df.to_dict("records"),
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
        },
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        getRowStyle={
            "styleConditions": [
                {
                    "condition": "params.data['new_qty']<0",
                    "style": {"backgroundColor": "#c0392b"},
                },
                {
                    "condition": "params.data['new_qty']>=0",
                    "style": {"backgroundColor": "#27ae60"},
                },
            ]
        },
        style={"height": 400},
    )
    modal_table = dag.AgGrid(
        id=id("inventory-modal-table"),
        className="ag-theme-quartz",
        columnSize="autoSize",
        columnDefs=[
            {"field": "checkcode", "headerName": "台达料号"},
            {"field": "stockno", "headerName": "库位号"},
            # {"field": "uid", "headerName": "唯一码"},
            {"field": "stock_qty", "headerName": "库存数量"},
            {"field": "pending_qty", "headerName": "待出库量"},
            {"field": "qty", "headerName": "单盘数量"},
            {
                "field": "actual_qty",
                "headerName": "盘点数量",
                "editable": True,
                "cellEditor": "agNumberCellEditor",
                "cellEditorParams": {"min": 0},
            },
            {"field": "checked", "headerName": "checked", "hide": True},
        ],
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
        },
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        getRowId="params.data.uid",
        getRowStyle={
            "styleConditions": [
                {
                    "condition": "params.data['checked']==1",
                    "style": {"backgroundColor": "#3D9970"},
                },
            ]
        },
        style={"height": 300},
    )
    div = fac.Flex(
        [
            fac.Flex(
                [
                    fac.Space(
                        [
                            "复盘周期",
                            fac.Slider(
                                min=0,
                                max=12,
                                defaultValue=0,
                                style={"width": 300},
                                tooltipPrefix="当前为:",
                                tooltipSuffix="个月内未盘点材料",
                                id=id("inventory-cycle"),
                                marks={i: f"{i}" for i in range(0, 13)},
                                railStyle={"background": "#37d67a"},
                            ),
                        ]
                    ),
                    fac.Button(
                        "下载盘点清单",
                        id=id("download-inventory"),
                        style={"background": "orange"},
                        # disabled=disabled,
                    ),
                    fac.Upload(
                        apiUrl="/upload/",
                        buttonContent="上传盘点清单",
                        buttonProps={"danger": True},
                        id=id("upload-inventory"),
                        showUploadList=False,
                        showSuccessMessage=False,
                        # disabled=disabled,
                    ),
                    fac.Button("单料号盘点", id=id("apply"), type="primary"),
                    fac.Modal(
                        [
                            modal_table,
                            fac.Input(
                                id=id("inventory-remark"),
                                placeholder="在此输入盘点备注",
                            ),
                        ],
                        id=id("inventory-modal"),
                        title=fac.Flex(
                            [
                                fac.Switch(
                                    id=id("inventory-light"),
                                    checkedChildren="开灯",
                                    unCheckedChildren="关灯",
                                    checked=None,
                                    disabled=True,
                                    style={"width": 100},
                                ),
                                fac.Input(
                                    id=id("inventory-check-uid"),
                                    placeholder="激活此输入框进行扫码核对",
                                    style={"width": 500},
                                    disabled=True,
                                ),
                            ],
                            gap="large",
                            align="center",
                        ),
                        renderFooter=True,
                        okClickClose=False,
                        centered=True,
                        width=1000,
                    ),
                ],
                justify="space-between",
                align="center",
            ),
            table,
            DashPlayer(
                id=id("fail-audio"),
                url="/assets/fail.mp3",
                playing=False,
                controls=True,
                style={"display": "none"},
            ),
        ],
        vertical=True,
        gap="small",
    )
    return div


def stock_transfer(area: str):
    sql = "select id as stock_id,deltapn,checkcode,stockno,des,mfgname,mfgpn,qty,\
        area,limituse from ssp.stock where area=%s"
    df = read_sql(sql, params=[area])
    if area == "SH":
        df = df.loc[~df["stockno"].str.match("^\d{7}$", na=False)]

    sql = "select distinct packing from stockno_list where area=%s"
    dfp = read_sql(sql, params=[area])

    table = dag.AgGrid(
        id=id("transfer-table"),
        className="ag-theme-quartz",
        columnSize="autoSize",
        columnDefs=[
            {
                "field": "id",
                "headerName": "id",
                "checkboxSelection": True,
                "headerCheckboxSelection": True,
                "headerCheckboxSelectionFilteredOnly": True,
            },
            {"field": "qty", "headerName": "数量"},
            {"field": "deltapn", "headerName": "料号"},
            {"field": "stockno", "headerName": "库位号"},
            {"field": "des", "headerName": "描述"},
            {"field": "mfgname", "headerName": "厂商"},
            {"field": "mfgpn", "headerName": "厂商料号"},
            {"field": "area", "headerName": "区域"},
            {"field": "checkcode", "headerName": "系列料号"},
            {"field": "limituse", "headerName": "专用"},
        ],
        rowData=df.to_dict("records"),
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
        },
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
    )
    div = dmc.Stack(
        [
            fac.Form(
                [
                    fac.FormItem(
                        fac.Input(
                            addonBefore="模糊搜索",
                            debounceWait=1000,
                            id=id("transfer-filter"),
                        ),
                        style={"width": "400px"},
                    ),
                    fac.FormItem(
                        fac.Select(
                            id=id("stockno"),
                            emptyContent=fac.Form(
                                [
                                    fac.FormItem(
                                        fac.RadioGroup(
                                            options=dfp["packing"].to_list(),
                                            id=id("stockno-packing"),
                                        ),
                                        label="材料包装",
                                    ),
                                    fac.FormItem(
                                        fac.RadioGroup(
                                            options=[],
                                            # options=stockno_type,
                                            id=id("stockno-type"),
                                        ),
                                        label="库位类型",
                                    ),
                                    fac.FormItem(
                                        fac.RadioGroup(
                                            options=[],
                                            id=id("stockno-category"),
                                        ),
                                        label="库位架别",
                                    ),
                                ],
                                layout="vertical",
                            ),
                            style={"width": "300px"},
                        ),
                        label="库位号",
                        # labelCol={"flex": "none"},
                        # wrapperCol={"flex": "auto"},
                    ),
                    fac.FormItem(
                        fac.InputNumber(
                            # addonBefore="模糊搜索",
                            # debounceWait=1000,
                            id=id("label-qty"),
                            value=1,
                        ),
                        label="标签份数",
                    ),
                    fac.FormItem(
                        fac.Button("提交", type="primary", id=id("transfer-submit")),
                    ),
                ],
                layout="inline",
                # labelCol={"flex": "1"},
                # wrapperCol={"flex": "1"},
            ),
            table,
        ]
    )
    return div


def stock_re_label():
    div = dmc.Stack(
        [
            fac.Space(
                [
                    fac.RadioGroup(
                        options=[
                            {"label": "相同唯一码", "value": "相同唯一码"},
                            {"label": "新建唯一码", "value": "新建唯一码"},
                        ],
                        defaultValue="相同唯一码",
                        id=id("relabel-option"),
                    ),
                    fac.Input(
                        addonBefore="搜索",
                        debounceWait=1000,
                        id=id("search"),
                        style={"width": "400px"},
                    ),
                    fac.Button("提交", type="primary", id=id("relabel-submit")),
                ],
                size="large",
                # addSplitLine=True,
            ),
            dag.AgGrid(
                id=id("stock-uid-table"),
                className="ag-theme-quartz",
                columnDefs=[
                    # {
                    #     "field": "action",
                    #     "headerName": "补打类型",
                    #     "cellEditor": {"function": "DMC_Select"},
                    #     "cellEditorParams": {
                    #         "options": ["相同唯一码", "新建唯一码"],
                    #         "clearable": True,
                    #         "shadow": "xl",
                    #     },
                    #     "cellEditorPopup": True,
                    #     "singleClickEdit": True,
                    #     "width": 140,
                    #     "editable": True,
                    #     "pinned": "left",
                    # },
                    {"field": "action", "headerName": "补打类型", "hide": True},
                    {
                        "field": "qty",
                        "headerName": "数量",
                        "width": 80,
                        "editable": {"function": "params.data.action == '新建唯一码'"},
                        "cellEditor": "agNumberCellEditor",
                        "cellEditorParams": {"min": 1},
                    },
                    {"field": "checkcode", "headerName": "料号", "width": 200},
                    {"field": "uid", "headerName": "唯一码", "width": 200},
                    {"field": "area", "headerName": "区域", "width": 150},
                    {"field": "stockno", "headerName": "库位号", "width": 170},
                    {"field": "gmt_create", "headerName": "创建时间", "width": 200},
                    # {"field": "pur_status", "headerName": "状态", "width": 100},
                    # {"field": "type", "headerName": "类型", "width": 80},
                ],
                rowData=[],
                dashGridOptions={
                    "rowSelection": "single",
                    "stopEditingWhenCellsLoseFocus": True,
                    "singleClickEdit": True,
                    "rowHeight": 35,
                    "enableCellTextSelection": True,
                    "ensureDomOrder": True,
                },
                defaultColDef={
                    "resizable": True,
                    "sortable": True,
                    "filter": True,
                    "wrapHeaderText": True,
                    "autoHeaderHeight": True,
                },
            ),
        ]
    )
    return div


def stock_csg(area: str):
    sql = "select temp_pn,deltapn from ce_temp_pn where deltapn is not null"
    ce_temp_pn = read_sql(sql)
    params = ce_temp_pn["temp_pn"].unique().tolist()
    sql = "select id,deltapn as temp_pn,stockno,qty,limituse from stock \
        where area=%s and deltapn in %s"
    params = [area, params]
    stock = read_sql(sql, params=params)
    if stock.empty:
        return fac.Empty()

    sql = "select deltapn,des,mfgname,mfgpn,checkcode from ssp_csg.mat_info \
        where checkcode in %s"
    params = [stock["temp_pn"].unique().tolist()]
    csg = read_sql(sql, params=params)

    df = ce_temp_pn.merge(stock, on="temp_pn", how="inner").merge(
        csg, on="deltapn", how="left"
    )
    df["checkcode"] = np.where(df["checkcode"].isnull(), df["deltapn"], df["checkcode"])

    table = dag.AgGrid(
        id=id("csg-table"),
        className="ag-theme-quartz",
        columnDefs=[
            {"field": "temp_pn", "headerName": "临时料号"},
            {"field": "deltapn", "headerName": "正式料号"},
            {"field": "checkcode", "headerName": "系列料号"},
            {"field": "stockno", "headerName": "库位号"},
            {"field": "des", "headerName": "描述"},
            {"field": "mfgname", "headerName": "厂商"},
            {"field": "mfgpn", "headerName": "厂商料号"},
            {"field": "id", "headerName": "id"},
        ],
        rowData=df.to_dict("records"),
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
        },
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
    )
    div = dmc.Stack(
        [
            table,
            fac.Button("提交更新", id=id("csg-submit"), type="primary"),
        ]
    )
    return div


@callback(
    Output(id("setting-content"), "children"),
    Input(id("setting-tabs"), "value"),
    State("user", "data"),
    prevent_initial_call=False,
)
def setting_tabs(active, user):
    area = user.get("area")
    if active == "1":
        div = fac.Empty()
    elif active == "2":
        div = stock_csg(area)
    elif active == "3":
        div = stock_transfer(area)
    elif active == "4":
        div = stock_re_label()
    elif active == "5":
        div = stock_clean(user)
    elif active == "6":
        div = stock_location(user)
    elif active == "7":
        div = stock_inventory(user)
    else:
        div = fac.Empty()
    return div


@callback(
    Output("download", "data"),
    # Output(id("stock-clean-table"), "rowData"),
    Input(id("download-clean-btn"), "nClicks"),
    State("user", "data"),
    running=[(Output(id("download-clean-btn"), "loading"), True, False)],
)
def stock_clean_download(n_clicks, user):
    if not n_clicks:
        raise PreventUpdate

    area = user.get("area")
    now = datetime.now()
    two_years_ago = (now - timedelta(days=730)).strftime("%Y-%m-%d %H:%M:%S")
    sql = "select id,deltapn,stockno,des,mfgname,mfgpn,qty,checkcode from stock where area=%s"
    stock = read_sql(sql, params=[area])
    stock0 = stock.loc[stock["qty"] == 0]

    sql = "select distinct checkcode from stockout where lable is null"
    stockout_pedding = read_sql(sql)
    stock0 = stock0.loc[~stock0["checkcode"].isin(stockout_pedding["checkcode"])]

    sql = "select distinct stockoutdate,checkcode from stockout where stockoutdate>=%s"
    stockout = read_sql(sql, params=[two_years_ago])

    c1 = stockout["checkcode"].str.strip().str.upper()

    sql = "select distinct checkcode from pur where start_date>=%s"
    pur = read_sql(sql, params=[two_years_ago])
    c2 = pur["checkcode"].str.strip().str.upper()

    sql = "select distinct checkcode from stockin where stockindate>=%s"
    stockin = read_sql(sql, params=[two_years_ago])
    c3 = stockin["checkcode"].str.strip().str.upper()

    df = stock.query("~(checkcode.isin(@c1)|checkcode.isin(@c2)|checkcode.isin(@c3))")
    stock0 = stock0.loc[~stock0["id"].isin(df["id"])]
    df = pd.concat([stock0, df], axis=0)

    params = [df["checkcode"].tolist()]
    sql = "select checkcode,max(stockoutdate) as laststockoutdate from stockout \
        where checkcode in %s group by checkcode"
    laststockout = read_sql(sql, params=params)

    sql = "select checkcode,max(start_date) as lastpurdate from pur \
        where checkcode in %s group by checkcode"
    lastpur = read_sql(sql, params=params)

    sql = "select checkcode,max(stockindate) as laststockindate from stockin \
        where checkcode in %s group by checkcode"
    laststockin = read_sql(sql, params=params)

    df = (
        df.merge(laststockout, on="checkcode", how="left")
        .merge(lastpur, on="checkcode", how="left")
        .merge(laststockin, on="checkcode", how="left")
    )

    return dcc.send_data_frame(df.to_excel, "清库清单.xlsx", index=False)


@callback(
    Output(id("upload-clean"), "disabled"),
    Output(id("stock-clean-table"), "rowData"),
    Input(id("upload-clean"), "lastUploadTaskRecord"),
    State("user", "data"),
)
def stock_clean_upload(record, user):
    if not record:
        raise PreventUpdate
    file = UPLOAD_FOLDER_ROOT / record["taskId"] / record["fileName"]
    df = pd.read_excel(file, dtype=str, keep_default_na=False)
    df = df.rename(columns={"id": "stock_id"})
    sql = "select stock_id,stockno as stockno_x,uid from stockno_list where stock_id in %s"
    params = [df["stock_id"].dropna().tolist()]
    df1 = read_sql(sql, params=params)
    df1["stock_id"] = df1["stock_id"].astype(str)
    df = df.merge(df1, on="stock_id", how="left")
    df["uid"] = np.where(df["uid"].isna(), df["stock_id"], df["uid"])
    df["stockno"] = np.where(df["stockno_x"].isna(), df["stockno"], df["stockno_x"])
    nt_name = user.get("nt_name")
    df["owner"] = nt_name
    df["stock_id"] = df["stock_id"].astype(int)
    df["qty"] = df["qty"].astype(int)
    df = df.drop("stockno_x", axis=1)
    df = df.replace({np.nan: None, "": None})

    fields = ",".join(df.columns)
    ph = ",".join(["%s"] * len(df.columns))

    sql = f"replace into ssp.stock_clean({fields}) values ({ph})"
    params = df.values.tolist()
    db.execute_many(sql, params)
    return True, df.to_dict("records")


@callback(
    Output("msg", "children"),
    Input(id("light"), "checked"),
    State(id("stock-clean-table"), "rowData"),
)
def stock_clean_light(checked, data):
    if checked is None:
        raise PreventUpdate

    df = pd.DataFrame(data)
    if "stock_id" not in df.columns:
        if checked:
            return fac.Message(content="请先上传清库清单", type="error")
        else:
            raise PreventUpdate

    if df["uid"].isna().all():
        if checked:
            return fac.Message(content="无可亮灯库位", type="warning")
        else:
            raise PreventUpdate

    sql = "select stockno from ssp.stockno_list where uid in %s"
    dfx = read_sql(sql, params=[df["uid"].dropna().tolist()])
    stockno = dfx["stockno"].tolist()
    if checked:
        task_take_off_shelf(stockno, color="Red")
        return fac.Message(content="亮灯成功", type="success")
    else:
        task_take_off_shelf(stockno, color="Gray")
        return fac.Message(content="灭灯成功", type="success")


@callback(
    Output(id("check-uid"), "value"),
    Output(id("stock-clean-table"), "rowTransaction"),
    Input(id("check-uid"), "nSubmit"),
    State(id("check-uid"), "value"),
    State(id("stock-clean-table"), "rowData"),
)
def stock_clean_check_uid(n, uid, data):
    if not all([n, uid, data]):
        raise PreventUpdate

    uid: str = uid.strip()
    if uid.startswith(("Ry", "Rv")):
        uid = uid[1:]

    u_data = [i for i in data if i["uid"] == uid]

    if u_data:
        if uid.startswith("R"):
            uid = uid[1:]
        sql = "select stockno from ssp.stockno_list where uid=%s"
        res = db.execute_fetchone(sql, [uid])
        if not res:
            set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
            set_props(
                "msg",
                {
                    "children": fac.Message(
                        content=f"{uid}该料盘不在料架上",
                        type="error",
                    )
                },
            )
            return None, no_update
        stockno = res.get("stockno")
        task_take_off_shelf([stockno], color="Gray")
        sql = "update ssp.stockno_list set uid=null,stock_id=null where uid=%s"
        db.execute(sql, [uid])

        sql = "update ssp.stock_clean set check_date=now() where uid=%s"
        db.execute(sql, [uid])

        set_props(
            "msg",
            {"children": fac.Message(content=f"{uid}扫码核对正确", type="success")},
        )
        u_data = [i | {"check_date": datetime.now()} for i in u_data]
        return None, {"update": u_data}
    else:
        stockno = None
        data1 = [i for i in data if i["stockno"] == uid]
        if data1:
            stockno = data1[0]["stockno"]
        else:
            sql = "select stockno from stock_uid where uid=%s"
            res = db.execute_fetchone(sql, [uid])
            if res:
                stockno = res.get("stockno")

        if not stockno:
            set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
            set_props(
                "msg",
                {
                    "children": fac.Message(
                        content="该料非清库材料，请核实",
                        type="error",
                    )
                },
            )
            return None, no_update

        u_data = [i for i in data if i["stockno"] == stockno]
        if not u_data:
            set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
            set_props(
                "msg",
                {
                    "children": fac.Message(
                        content="该料非清库材料，请核实", type="error"
                    )
                },
            )
            return None, no_update

        sql = "update ssp.stockno_list set uid=null,stock_id=null where stockno=%s"
        db.execute(sql, [stockno])

        sql = "update ssp.stock_clean set check_date=now() where stockno=%s"
        db.execute(sql, [stockno])

        set_props(
            "msg",
            {"children": fac.Message(content=f"{stockno}核对正确", type="success")},
        )
        u_data = [i | {"check_date": datetime.now()} for i in u_data]
        return None, {"update": u_data}


@callback(
    Output("msg", "children"),
    Output(id("clean-submit"), "disabled"),
    Output(id("stock-clean-table"), "rowData"),
    Input(id("clean-submit"), "nClicks"),
    State(id("stock-clean-table"), "rowData"),
    State("user", "data"),
)
def stock_clean_submit(nClicks, data, user):
    if not nClicks:
        raise PreventUpdate

    df = pd.DataFrame(data)
    if "check_date" not in df.columns:
        raise PreventUpdate
    if df["check_date"].isna().any():
        return (
            fac.Message(content="有未核实材料，请核实", type="error"),
            no_update,
            no_update,
        )

    nt_name = user.get("nt_name")
    df["owner"] = nt_name

    stock_id = df["stock_id"].dropna().tolist()
    sql = "update stock_clean set end_date=now() where stock_id=%s and owner=%s"
    params = df[["stock_id", "owner"]].values.tolist()
    db.execute_many(sql, params)

    stock_id = df["stock_id"].dropna().tolist()
    sql = "update stockno_list set uid=null,stock_id=null where stock_id in %s"
    params = [stock_id]
    db.execute(sql, params)

    sql = "delete from stock where id in %s"
    params = [stock_id]
    db.execute(sql, params)
    return fac.Message(content="清库成功", type="success"), True, []


# @callback(
#     Output(id("inventory-table"), "filterModel"),
#     Input(id("inventory-cycle"), "value"),
#     # Input("years-panel", "value"),
# )
# def inventory_filter(cycle):
#     date = datetime.now() - timedelta(days=30 * int(cycle))
#     date = date.strftime("%Y-%m-%d")
#     return {
#         "inventory_date": {
#             "filterType": "date",
#             "operator": "OR",
#             "conditions": [
#                 {"filterType": "date", "type": "lessThan", "dateFrom": date},
#                 {"filterType": "date", "type": "blank"},
#             ],
#         }
#     }


@callback(
    Output(id("inventory-table"), "dashGridOptions"),
    Input(id("inventory-cycle"), "value"),
)
def inventory_filter2(cycle):
    if cycle is None:
        raise PreventUpdate
    date = datetime.now() - timedelta(days=30 * int(cycle))
    date = date.strftime("%Y-%m-%d")

    return {
        "isExternalFilterPresent": {"function": "true"},
        "doesExternalFilterPass": {"function": f"inventoryDate(params,'{date}')"},
    }


@callback(
    Output("download", "data"),
    Input(id("download-inventory"), "nClicks"),
    State(id("inventory-table"), "virtualRowData"),
)
def inventory_download_list(n_clicks, data):
    if not n_clicks:
        raise PreventUpdate

    df = pd.DataFrame(data)
    df = df.reindex(
        columns=[
            "id",
            "deltapn",
            "stockno",
            "des",
            "mfgname",
            "mfgpn",
            "checkcode",
            "inventory_date",
            "qty",
            "pending_qty",
            "盘点数量",
        ]
    ).rename(
        columns={
            "inventory_date": "最近盘点日期",
            "qty": "库存数量",
            "pending_qty": "待出库数量",
        }
    )
    # raise PreventUpdate
    return dcc.send_data_frame(df.to_excel, "盘点清单.xlsx", index=False)


@callback(
    Output("msg", "children"),
    Output(id("inventory-table"), "rowData"),
    Output(id("upload-inventory"), "disabled"),
    Input(id("upload-inventory"), "lastUploadTaskRecord"),
    State("user", "data"),
)
def inventory_upload_list(record: list, user: dict):
    if not record:
        raise PreventUpdate

    file = UPLOAD_FOLDER_ROOT / record["taskId"] / record["fileName"]
    df = pd.read_excel(file, dtype=str, keep_default_na=False)
    df = df.rename(
        columns={
            "id": "stock_id",
            "盘点数量": "actual_qty",
            "待出库数量": "pending_qty",
            "库存数量": "stock_qty",
            "最近盘点日期": "inventory_date",
        }
    )
    df["pending_qty"] = pd.to_numeric(df["pending_qty"], errors="coerce").fillna(0)
    df["actual_qty"] = pd.to_numeric(df["actual_qty"], errors="coerce").fillna(0)
    df["stock_qty"] = pd.to_numeric(df["stock_qty"], errors="coerce").fillna(0)
    df["qty"] = df["actual_qty"] - df["pending_qty"] - df["stock_qty"]

    sql = "select id as stock_id,qty as new_stock_qty from ssp.stock where id in %s"
    stock = db.execute(sql, [df["stock_id"].dropna().tolist()])
    stock = pd.DataFrame(stock)
    stock["stock_id"] = stock["stock_id"].astype(str)

    df = df.merge(stock, how="left", on="stock_id")
    df["new_stock_qty"] = pd.to_numeric(df["new_stock_qty"], errors="coerce").fillna(0)
    df["new_qty"] = df["new_stock_qty"] + df["qty"]
    df1 = df.loc[df["new_qty"] < 0]

    if not df1.empty:
        return (
            fac.Message(content="如下材料无法盘点修正,请删除后重试", type="error"),
            df1.to_dict("records"),
            no_update,
        )

    df = df.loc[df["new_qty"] >= 0]
    for i in df.itertuples():
        inventory_surplus(i.stock_id, user, i._asdict(), i.new_qty)

    df["owner"] = user.get("nt_name")
    df1 = df.drop(columns=["inventory_date", "new_stock_qty", "new_qty"])
    fields = ",".join(df1.columns)
    ph = ",".join(["%s"] * len(df1.columns))
    sql = f"insert into ssp.stock_inventory({fields}) values({ph})"
    params = df1.values.tolist()
    db.execute_many(sql, params)

    return fac.Message(content="提交成功", type="success"), df.to_dict("records"), False


@callback(
    Output("msg", "children"),
    Output(id("inventory-modal"), "visible"),
    Output(id("inventory-modal-table"), "rowData"),
    Output(id("inventory-light"), "disabled"),
    Output(id("inventory-check-uid"), "disabled"),
    Input(id("apply"), "nClicks"),
    State(id("inventory-table"), "selectedRows"),
)
def inventory_open_modal(nClicks, data):
    if not nClicks:
        raise PreventUpdate
    if not data:
        msg = fac.Message(content="请选择要盘点的材料", type="error")
        return msg, no_update, no_update, no_update, no_update

    df = pd.DataFrame(data)
    df["stock_qty"] = df["qty"]
    df["stockno_type"] = ""
    stockno = data[0]["stockno"]
    disabled = True
    if re.match(r"^\d{7}$", stockno):
        disabled = False
        stock_id = data[0]["id"]
        df = df.drop(columns=["stockno", "qty"])
        sql = "select stock_id as id,uid,sum(qty) as qty \
            from ssp.stock_in_out \
            where stock_id = %s group by uid"
        df1 = read_sql(sql, params=[stock_id])

        sql = "select uid,stockno from ssp.stockno_list \
            where stock_id = %s and type=%s"
        df2 = read_sql(sql, params=[stock_id, "电子料架"])
        df = df.merge(df1, how="left", on="id").merge(df2, how="left", on="uid")
        df["stockno"] = df["stockno"].fillna("不在料架")
        df["stockno_type"] = "电子料架"

    return no_update, True, df.to_dict("records"), disabled, disabled


@callback(
    Output("msg", "children"),
    Input(id("inventory-light"), "checked"),
    State(id("inventory-modal-table"), "rowData"),
)
def inventory_light(checked, data):
    if checked is None:
        raise PreventUpdate

    df = pd.DataFrame(data)
    if df["uid"].isna().all():
        if checked:
            return fac.Message(content="无可亮灯库位", type="warning")
        else:
            raise PreventUpdate

    sql = "select stockno from ssp.stockno_list where uid in %s"
    dfx = read_sql(sql, params=[df["uid"].dropna().tolist()])
    stockno = dfx["stockno"].tolist()
    if checked:
        task_take_off_shelf(stockno, color="Red")
        return fac.Message(content="亮灯成功", type="success")
    else:
        task_take_off_shelf(stockno, color="Gray")
        return fac.Message(content="灭灯成功", type="success")


@callback(
    Output(id("inventory-check-uid"), "value"),
    Output(id("inventory-modal-table"), "rowTransaction"),
    Input(id("inventory-check-uid"), "nSubmit"),
    State(id("inventory-check-uid"), "value"),
    State(id("inventory-modal-table"), "rowData"),
)
def inventory_check_uid(n, uid, data):
    if not all([n, uid, data]):
        raise PreventUpdate

    uid: str = uid.strip()
    if uid.startswith(("Ry", "Rv")):
        uid = uid[1:]

    u_data = [i for i in data if i["uid"] == uid]

    if u_data:
        u_data = [i | {"checked": 1} for i in u_data]

        sql = "select stockno from ssp.stockno_list where uid=%s"
        res = db.execute_fetchone(sql, [uid])
        if not res:
            set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
            set_props(
                "msg",
                {
                    "children": fac.Message(
                        content=f"{uid}该料盘不在料架上",
                        type="error",
                    )
                },
            )
            return None, {"update": u_data}
        stockno = res.get("stockno")
        task_take_off_shelf([stockno], color="Gray")
        sql = "update ssp.stockno_list set uid=null,stock_id=null where uid=%s"
        db.execute(sql, [uid])

        set_props(
            "msg",
            {"children": fac.Message(content=f"{uid}扫码核对正确", type="success")},
        )

        return None, {"update": u_data}
    else:
        set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
        set_props(
            "msg",
            {"children": fac.Message(content=f"{uid}不在清单中，请核实", type="error")},
        )
        return None, no_update


@callback(
    Output("msg", "children"),
    Output(id("inventory-modal"), "visible"),
    Output(id("inventory-table"), "deleteSelectedRows"),
    Input(id("inventory-modal"), "okCounts"),
    State(id("inventory-modal-table"), "rowData"),
    State(id("inventory-remark"), "value"),
    State("user", "data"),
)
def inventory_submit(ok, data, remark, user):
    if not ok:
        raise PreventUpdate

    df = pd.DataFrame(data)
    if "actual_qty" not in df.columns:
        return fac.Message(content="请填写盘点数量", type="error"), no_update, no_update

    if df["actual_qty"].isna().any():
        return fac.Message(content="请填写盘点数量", type="error"), no_update, no_update

    actual_qty = df["actual_qty"].sum()
    d = df.iloc[0].to_dict()
    pending_qty = d.get("pending_qty")
    stock_qty = d.get("stock_qty")
    stock_id = d.get("id")
    stockno_type = d.get("stockno_type")
    diff_qty = actual_qty - pending_qty - stock_qty
    df["qty"] = df["qty"].fillna(0)

    nt_name = user.get("nt_name")

    if stockno_type == "电子料架":
        for i in df.itertuples():
            if i.uid:
                db.insert(
                    "ssp.stock_in_out",
                    {
                        "stock_id": i.id,
                        "uid": i.uid,
                        "qty": i.actual_qty - i.qty,
                        "type": "盘点",
                        "owner": nt_name,
                    },
                )
    db.insert(
        "ssp.stock_inventory",
        {
            "stock_id": stock_id,
            "actual_qty": actual_qty,
            "owner": nt_name,
            "qty": diff_qty,
            "pending_qty": pending_qty,
            "stock_qty": stock_qty,
            "checkcode": d.get("checkcode"),
            "deltapn": d.get("deltapn"),
            "stockno": d.get("stockno"),
            "des": d.get("des"),
            "mfgname": d.get("mfgname"),
            "mfgpn": d.get("mfgpn"),
            "remark": remark,
        },
    )

    stock = db.find_one("ssp.stock", {"id": stock_id})
    current_stock_qty = stock.get("qty")
    new_stock_qty = current_stock_qty + diff_qty

    if new_stock_qty >= 0:  # 盘盈
        inventory_surplus(stock_id, user, d, new_stock_qty)
    else:  # 盘亏
        inventory_loss(stock_id, user, d, new_stock_qty)
    return fac.Message(content="提交成功", type="success"), False, True


def inventory_surplus(stock_id, user, d: dict, new_stock_qty):
    """盘点盈余"""

    area = user.get("area")
    nt_name = user.get("nt_name")
    sql = "select item_pur,prt_id,bom_id,prtno,deltapn,checkcode,dept,checkcode,\
        qty,received_qty,price,inv_no,po_no,pr_no,pur_status,dept_id from ssp.pur \
        where application=%s and dest_area=%s and pur!=%s \
        and checkcode=%s and pur_status not in %s"
    params = [
        "project",
        area,
        "bo.sm.wang",
        d.get("checkcode"),
        ["closed", "cancel", "received"],
    ]
    pur = db.execute(sql, params=params)
    if not pur:
        db.update("ssp.stock", {"qty": new_stock_qty, "id": stock_id})
        return
    df = pd.DataFrame(pur)
    df["received_qty"] = df["received_qty"].fillna(0).astype(int)
    df["qty"] = df["qty"].fillna(0).astype(int)
    df["new_qty"] = df["qty"] - df["received_qty"]
    df["cumsum"] = df["new_qty"].cumsum()
    df["cumsum"] = df["cumsum"].astype(int)
    df["new_received_qty"] = np.where(
        df["cumsum"] <= abs(new_stock_qty),
        df["new_qty"],
        abs(new_stock_qty) - (df["cumsum"] - df["new_qty"]),
    )
    df = df.loc[df["new_received_qty"] > 0]
    now = datetime.now()

    df["remark"] = f"{now:%y-%m-%d %H:%M:%S}-{nt_name}-盘赢收料" + df[
        "new_received_qty"
    ].astype(str)

    # -------采购状态处理--------
    df["received_qty"] = df["received_qty"] + df["new_received_qty"]
    df["price"] = df["price"].fillna("").astype(str).str.strip().str.len()
    df["inv_no"] = df["inv_no"].fillna("").astype(str).str.strip().str.len()
    df["po_no"] = df["po_no"].fillna("").astype(str).str.strip().str.len()
    df["pr_no"] = df["pr_no"].fillna("").astype(str).str.strip().str.len()

    c1 = df["received_qty"] >= df["qty"]
    df["pur_status"] = np.where(c1, "received", df["pur_status"])

    c2 = df["price"] == 0
    c3 = df["pr_no"] == 0
    df["pur_status"] = np.where(c1 & c2 & c3, "closed", df["pur_status"])

    c4 = df["price"] > 0
    c5 = df["pr_no"] > 0
    c6 = df["inv_no"] > 0
    c7 = df["po_no"] > 0
    df["pur_status"] = np.where(c1 & c4 & c5 & c6 & c7, "closed", df["pur_status"])
    df["nt_name"] = nt_name
    df["now"] = now
    df["remaining_qty"] = 0
    df["area"] = area
    df["stock_id"] = stock_id
    df["stockno"] = d.get("stockno")
    for i in df.itertuples():
        update_pur(i)
        receive_project(i)

    new_received_qty = df["new_received_qty"].sum()
    remaining_qty = new_stock_qty - new_received_qty
    if remaining_qty > 0:
        db.update("ssp.stock", {"qty": remaining_qty, "id": stock_id})


def inventory_loss(stock_id, user, d: dict, new_stock_qty):
    """盘点亏损"""

    db.update("ssp.stock", {"qty": 0, "id": stock_id})
    sql = "select group_concat(a.id) as id,a.prt_id,a.bom_id,a.prtno,\
        a.deltapn,a.checkcode,a.dept,sum(a.qty) as qty,a.area,a.dept_id,b.des,\
        b.mfgname,b.mfgpn,c.proj,c.ee,a.stockno,b.id as stock_id \
        from ssp.stockout a \
        left join ssp.stock b on a.stock_id=b.id \
        left join ssp.prt c on a.prtno=c.prtno \
        where a.area=%s and a.checkcode=%s and lable is null \
        group by area,prtno,checkcode order by stockoutdate desc"
    params = [user.get("area"), d.get("checkcode")]
    stockout = db.execute(sql, params=params)
    df = pd.DataFrame(stockout)
    df["qty"] = df["qty"].astype(int)
    df["cumsum"] = df["qty"].cumsum()
    df["cumsum"] = df["cumsum"].astype(int)
    df["pur_qty"] = np.where(
        df["cumsum"] <= abs(new_stock_qty),
        df["qty"],
        abs(new_stock_qty) - (df["cumsum"] - df["qty"]),
    )

    df = df.loc[df["pur_qty"] > 0]
    if not df.empty:
        now = datetime.now()
        item_pur = int(f"{now:%y%m%d%H%M%S%f}")
        for i in df.itertuples():
            item_pur = item_pur + i.Index
            db.insert(
                "ssp.pur",
                {
                    "prt_id": i.prt_id,
                    "bom_id": i.bom_id,
                    "prtno": i.prtno,
                    "deltapn": i.deltapn,
                    "checkcode": i.checkcode,
                    "qty": i.pur_qty,
                    "application": "project",
                    "item_pur": item_pur,
                    "dept": i.dept,
                    "des": i.des,
                    "mfgname": i.mfgname,
                    "mfgpn": i.mfgpn,
                    "start_date": now,
                    "pur_status": "SA",
                    "mat_remark": "盘点不足",
                    "req_date": now + timedelta(days=30),
                    "dept_id": i.dept_id,
                    "dest_area": i.area,
                    "proj": i.proj,
                    "rd": i.ee,
                },
            )
            db.insert(
                "ssp.stockout",
                {
                    "prt_id": i.prt_id,
                    "bom_id": i.bom_id,
                    "stock_id": i.stock_id,
                    "stockoutdate": now,
                    "dept": i.dept,
                    "type": "SM",
                    "prtno": i.prtno,
                    "deltapn": i.deltapn,
                    "checkcode": i.checkcode,
                    "qty": -i.pur_qty,
                    "owner1": user.get("nt_name"),
                    "dept_id": i.dept_id,
                    "area": i.area,
                    "source": "bom",
                    "stockno": i.stockno,
                    "memo": "盘点不足",
                },
            )


@callback(
    Output(id("location-table"), "rowTransaction"),
    Input(id("add-location"), "nClicks"),
)
def location_add(nClicks):
    if not nClicks:
        raise PreventUpdate
    return {"add": [{"index": uuid4()}], "addIndex": 0}


@callback(
    Output("msg", "children"),
    Output(id("location-table"), "rowTransaction"),
    Input(id("location-table"), "cellValueChanged"),
    State("user", "data"),
)
def location_add_to_db(changed, user):
    if not changed:
        raise PreventUpdate
    data: dict = changed[0]["data"]
    if set(data) != {"packing", "category", "stockno", "index", "type"}:
        raise PreventUpdate

    stockno = data["stockno"]
    res = db.insert(
        "ssp.stockno_list",
        {
            "packing": data["packing"],
            "category": data["category"],
            "stockno": stockno,
            "area": user["area"],
            "type": data["type"],
        },
    )
    data["id"] = res
    msg = fac.Message(content=f"库位{stockno}添加成功", type="success")
    return msg, {"update": [data]}


@callback(
    Output("msg", "children"),
    Output(id("location-table"), "rowTransaction"),
    Input(id("update-location"), "confirmCounts"),
    State(id("location-table"), "selectedRows"),
)
def location_update(confirmCounts, rows):
    if not confirmCounts:
        raise PreventUpdate

    for i in rows:
        db.update(
            "ssp.stockno_list",
            {
                "id": i["id"],
                "type": i["type"],
                "packing": i["packing"],
                "category": i["category"],
            },
        )
    return fac.Message(content="库位更新成功", type="success"), {"remove": rows}


@callback(
    Output("msg", "children"),
    Output(id("location-table"), "rowTransaction"),
    Input(id("remove-location"), "confirmCounts"),
    State(id("location-table"), "selectedRows"),
)
def location_remove(confirmCounts, rows):
    if not confirmCounts:
        raise PreventUpdate

    if any(i["checkcode"] for i in rows):
        return fac.Message(
            content="要删除绑定材料的库位，请先清库", type="error"
        ), no_update

    for i in rows:
        db.delete("ssp.stockno_list", {"id": i["id"]})
    return fac.Message(content="库位删除成功", type="success"), {"remove": rows}


@callback(
    Output("msg", "children"),
    Output(id("csg-table"), "rowData"),
    Input(id("csg-submit"), "nClicks"),
    State(id("csg-table"), "rowData"),
    State("user", "data"),
)
def csg_submit(nclicks, data, user):
    if not nclicks:
        raise PreventUpdate

    df = pd.DataFrame(data)
    if df.empty:
        raise PreventUpdate

    nt_name = user.get("nt_name")
    area = user.get("area")
    df["area"] = area

    with pool.connection() as conn:
        with conn.cursor() as cu:
            params = df.reindex(
                columns=["checkcode", "deltapn", "temp_pn", "temp_pn"]
            ).values.tolist()
            sql = "update pur set checkcode=%s,deltapn=%s where deltapn=%s or checkcode=%s"
            cu.executemany(sql, params)

            sql = "update stockout set checkcode=%s,deltapn=%s where deltapn=%s or checkcode=%s"
            cu.executemany(sql, params)

            sql = "update smbom set checkcode=%s,deltapn=%s where deltapn=%s or checkcode=%s"
            cu.executemany(sql, params)

            sql = "update stock_uid set checkcode=%s where checkcode=%s"
            params = df.reindex(columns=["checkcode", "temp_pn"]).values.tolist()
            cu.executemany(sql, params)

            checkcode = df["checkcode"].tolist()
            sql = "select checkcode,id as stock_id,qty as stock_qty \
                from stock where checkcode in %s and area=%s"
            params = [checkcode, area]
            stock = read_sql(sql, params=params)
            df = df.merge(stock, on="checkcode", how="left")

            c1 = df["stock_id"].notna()
            c2 = ~df["stockno"].str.match(r"^\d{7}$")
            df1 = df.loc[c1 & c2]
            df2 = df.loc[df["stock_id"].isna()]

            # TODO:电子料架的库位在库存清单里面不唯一，更新stockno_list时可能会出错，目前只更新非电子料架
            # if not df1.empty:
            #     params = df1[["qty", "stock_id"]].values.tolist()
            #     sql = "update stock set qty=qty+%s where id=%s"
            #     cu.executemany(sql, params)

            #     params = df1[["id"]].values.tolist()
            #     sql = "delete from stock where id=%s"
            #     cu.executemany(sql, params)

            #     params = df[["stockno"]].values.tolist()
            #     sql = "update stockno_list set stock_id=null,uid=null,block=null \
            #         where stockno=%s"
            #     cu.executemany(sql, params)

            if not df2.empty:
                df2 = df2.reset_index(drop=True)
                params = df2[
                    ["checkcode", "deltapn", "des", "mfgname", "id"]
                ].values.tolist()
                sql = "update stock set checkcode=%s,deltapn=%s,des=%s,mfgname=%s where id=%s"
                cu.executemany(sql, params)
                df2["new_stock_no"] = df2["stockno"]
                df2["label_template"] = "stock_in"
                df2["owner"] = nt_name
                df2["id"] = df2.index + int(f"{datetime.now():%y%m%d%H%M%S%f}")
                df2["id"] = "y" + df2["id"].astype(str)
                bg_label_print(df2.to_json(orient="records"))
                task_import_uuid_to_my300(df2)

            conn.commit()

    return fac.Message(content="临时料号变更成功", type="success"), None


@callback(
    Output("msg", "children"),
    Output(id("transfer-table"), "deleteSelectedRows"),
    Output(id("stockno"), "value"),
    Input(id("transfer-submit"), "nClicks"),
    State(id("transfer-table"), "selectedRows"),
    State(id("stockno"), "value"),
    State(id("label-qty"), "value"),
    State("user", "data"),
)
def stock_transfer_submit(nclicks, rows, stockno, qty, user):
    if not nclicks:
        raise PreventUpdate
    if not rows:
        return fac.Message(content="请先选择记录", type="error"), no_update, no_update
    if not stockno:
        return fac.Message(content="请选择目标库位", type="error"), no_update, no_update

    row = rows[0]
    old_stockno = row["stockno"]
    stock_id = row["stock_id"]

    sql = "update ssp.stockno_list set stock_id=null where stockno=%s"
    params = [old_stockno]
    db.execute(sql, params)

    sql = "update ssp.stockno_list set stock_id=%s where stockno=%s"
    params = [stock_id, stockno]
    db.execute(sql, params)

    sql = "update ssp.stock set stockno=%s where id=%s"
    params = [stockno, stock_id]
    db.execute(sql, params)

    sql = "update ssp.stockout set stockno=%s where stock_id=%s"
    params = [stockno, stock_id]
    db.execute(sql, params)
    nt_name = user.get("nt_name")

    db.insert(
        "stock_modifyrecord",
        {
            "deltapn": row["deltapn"],
            "modify_type": "StockNo",
            "modify1": f"{old_stockno}>{stockno}",
            "m_date": datetime.now(),
            "owner": nt_name,
        },
    )

    df = pd.DataFrame(rows)
    df["stockno"] = stockno
    df["new_stock_no"] = stockno
    df["label_template"] = "stock_in"
    df["owner"] = nt_name

    for i in range(qty):
        df["id"] = f"y{datetime.now():%y%m%d%H%M%S%f}"
        bg_label_print(df.to_json(orient="records"))

    return fac.Message(content="移库提交成功", type="success"), True, None


@callback(
    Output(id("query-table"), "rowData"),
    Output(id("query-table"), "columnDefs"),
    Input(id("query-tabs"), "value"),
    State("user", "data"),
    prevent_initial_call=False,
)
def query_tabs(active, user):
    area = user.get("area")
    if active == "卷料备料":
        sql = "select uid,checkcode,stockno,prtno,qty,placer,finished_date \
            from ssp.stock_roll order by id desc limit 5000"
        df = read_sql(sql)

    elif active == "卷料出入库":
        sql = "select a.uid,b.checkcode,a.type,a.qty,a.gmt_create \
            from ssp.stock_in_out a \
            left join ssp.stock b on a.stock_id=b.id order by a.id desc limit 5000"
        df = read_sql(sql)

    elif active == "报废料盘":
        sql = "select distinct a.uid,b.checkcode,a.gmt_create from ssp.stock_scrap a \
            left join ssp.stock b on a.stock_id=b.id"
        df = read_sql(sql)

    elif active == "库位":
        sql = "select a.stockno,a.type,a.category,a.packing,a.area,a.uid,b.checkcode \
            from ssp.stockno_list a \
            left join ssp.stock b on a.stock_id=b.id where a.area=%s"
        df = read_sql(sql, params=[area])

    elif active == "库存":
        sql = "select deltapn,qty,stockno,checkcode,des,mfgname,mfgpn,area,limituse \
            from ssp.stock"
        df = read_sql(sql)

    elif active == "入库":
        sql = "select deltapn,qty,stockindate,owner,area,checkcode \
            from ssp.stockin order by id desc limit 5000"
        df = read_sql(sql)

    elif active == "出库":
        sql = "select deltapn,qty,stockoutdate,dept,type,prtno,owner1,stockoutdate2,\
            owner2,lable,area,checkcode,stockno from ssp.stockout \
            order by id desc limit 5000"
        df = read_sql(sql)
        df["owner1"] = df["owner1"].str.title()
        sql = "select nt_name as owner1,area as rd_area from ssp.user"
        df1 = read_sql(sql)
        df1["owner1"] = df1["owner1"].str.title()
        df = df.merge(df1, how="left", on="owner1")

    elif active == "采购":
        sql = "select deltapn,qty,mat_receiveddate,application,des,mfgname,mfgpn,prtno,\
            pur_remark,dept,pur,mat_remark,rd from ssp.pur where pur_status=%s \
            order by mat_receiveddate desc limit 5000"
        params = ["closed"]
        df = read_sql(sql, params=params)

    elif active == "变更确认":
        sql = "select action,qty,actual_qty,source,designno,prtno,dept,area,\
            checkcode,stockno,deltapn,des,mfgname,mfgpn,owner,smstatus,bom_owner \
            from ssp.bom_stockout where action is not null \
            order by gmt_update desc limit 5000"
        df = read_sql(sql)
    elif active == "盘点":
        sql = "select deltapn,checkcode,stockno,des,mfgname,mfgpn, \
            stock_qty,pending_qty,actual_qty,date,owner,remark \
            from ssp.stock_inventory order by id desc limit 5000"
        df = read_sql(sql)
        df = df.rename(
            columns={
                "stock_qty": "库存数量",
                "pending_qty": "待出库量",
                "actual_qty": "实际数量",
                "date": "盘点日期",
            }
        )
    else:
        return fac.Empty()

    rowData = df.to_dict("records")
    columnDefs = [{"field": i, "headerName": i} for i in df.columns]

    return rowData, columnDefs


# @callback(
#     Output(id("query-table"), "dashGridOptions"),
#     Input(id("query-filter"), "value"),
#     State(id("query-tabs"), "value"),
# )
# def query_filter(filter_value, active):
#     if active in ("入库", "出库", "采购"):
#         raise PreventUpdate
#     patch_grid_options = Patch()
#     patch_grid_options["quickFilterText"] = filter_value
#     return patch_grid_options


@callback(
    Output(id("query-table"), "rowData"),
    Output(id("query-table"), "columnDefs"),
    Input(id("query-filter"), "nClicksSearch"),
    Input(id("query-filter"), "nSubmit"),
    State(id("query-filter"), "value"),
    State(id("query-tabs"), "value"),
)
def query_server_filter(nClicksSearch, nSubmit, filter_value, active):
    if (not nClicksSearch) and (not nSubmit):
        raise PreventUpdate

    if active == "卷料备料":
        sql = "select uid,checkcode,stockno,prtno,qty,placer,finished_date \
            from ssp.stock_roll where checkcode=%s or prtno=%s or uid=%s"
        params = [filter_value, filter_value, filter_value]
        df = read_sql(sql, params=params)
    elif active == "卷料出入库":
        sql = "select a.uid,b.checkcode,a.type,a.qty,a.gmt_create \
            from ssp.stock_in_out a \
            left join ssp.stock b on a.stock_id=b.id \
            where a.uid=%s or b.checkcode=%s"
        params = [filter_value, filter_value]
        df = read_sql(sql, params=params)
    elif active == "报废料盘":
        sql = "select distinct a.uid,b.checkcode,a.gmt_create from ssp.stock_scrap a \
            left join ssp.stock b on a.stock_id=b.id where a.uid=%s or b.checkcode=%s"
        params = [filter_value, filter_value]
        df = read_sql(sql, params=params)
    elif active == "库位":
        sql = "select a.stockno,a.type,a.category,a.packing,a.area,a.uid,b.checkcode \
            from ssp.stockno_list a \
            left join ssp.stock b on a.stock_id=b.id \
            where a.stockno=%s or b.checkcode=%s"
        params = [filter_value, filter_value]
        df = read_sql(sql, params=params)
    elif active == "库存":
        sql = "select deltapn,qty,stockno,checkcode,des,mfgname,mfgpn,area,limituse \
            from ssp.stock where deltapn=%s or checkcode=%s"
        params = [filter_value, filter_value]
        df = read_sql(sql, params=params)
    elif active == "入库":
        sql = "select deltapn,qty,stockindate,owner,area,checkcode \
            from ssp.stockin where deltapn=%s or checkcode=%s"
        params = [filter_value, filter_value]
        df = read_sql(sql, params=params)
    elif active == "出库":
        sql = "select deltapn,qty,stockoutdate,dept,type,prtno,owner1,stockoutdate2,\
            owner2,lable,area,checkcode,stockno from ssp.stockout \
            where deltapn=%s or checkcode=%s or prtno=%s"
        params = [filter_value, filter_value, filter_value]
        df = read_sql(sql, params=params)
    elif active == "采购":
        sql = "select deltapn,qty,pur_status,start_date,application,prtno,\
            des,mfgname,mfgpn,pur_remark,mat_remark,dept,pur,rd,checkcode \
            from ssp.pur \
            where deltapn=%s or checkcode=%s or prtno=%s"
        params = [filter_value, filter_value, filter_value]
        df = read_sql(sql, params=params)
    elif active == "变更确认":
        sql = "select action,qty,actual_qty,source,designno,prtno,dept,area,\
            checkcode,stockno,deltapn,des,mfgname,mfgpn,owner,smstatus,bom_owner \
            from ssp.bom_stockout where action is not null \
            and (prtno=%s or checkcode=%s or deltapn=%s)"
        params = [filter_value, filter_value, filter_value]
        df = read_sql(sql, params=params)
    elif active == "盘点":
        sql = "select deltapn,checkcode,stockno,des,mfgname,mfgpn, \
            stock_qty,pending_qty,actual_qty,date,owner,remark \
            from ssp.stock_inventory \
            where stockno=%s or checkcode=%s or deltapn=%s"
        params = [filter_value, filter_value, filter_value]
        df = read_sql(sql, params=params)
        df = df.rename(
            columns={
                "stock_qty": "库存数量",
                "pending_qty": "待出库量",
                "actual_qty": "实际数量",
            }
        )
    else:
        return fac.Empty()

    rowData = df.to_dict("records")
    columnDefs = [{"field": i, "headerName": i} for i in df.columns]
    return rowData, columnDefs


@callback(
    Output(id("query-table"), "dashGridOptions"),
    Input(id("query-table"), "virtualRowData"),
)
def query_row_pinning_bottom(data):
    dff = pd.DataFrame(data)
    if "qty" not in dff.columns:
        raise PreventUpdate
    means = dff[["qty"]].sum() if data else {"qty": 0}

    # Using custom cell renderer doesn't apply "valueFormatter", so we can format the values here
    means_formatted = {"qty": f"{means['qty']:.1f}"}

    grid_option_patch = Patch()
    grid_option_patch["pinnedBottomRowData"] = [
        {"deltapn": "合计数量", **means_formatted}
    ]
    return grid_option_patch


@callback(
    Output(id("download"), "data"),
    Input(id("download-btn"), "n_clicks"),
    State(id("query-table"), "rowData"),
    State(id("query-tabs"), "value"),
)
def download_query_data(n_clicks, data, active):
    if not n_clicks:
        raise PreventUpdate
    df = pd.DataFrame(data)
    return dcc.send_data_frame(df.to_excel, f"{active}记录.xlsx", index=False)


def update_pur(i):
    if i.pur_status in ("received", "closed"):
        sql = "update pur set Owner_M=%s,received_qty=ifnull(received_qty,0)+%s,\
            Mat_ReceivedDate=%s,Pur_Status=%s,Mat_Remark=concat_ws(',',Mat_Remark,%s) \
            where Item_Pur=%s"
        params = [
            i.nt_name,
            i.new_received_qty,
            i.now,
            i.pur_status,
            i.remark,
            i.item_pur,
        ]
        db.execute(sql, params)

        sql = "update pur_plant set Owner_M=%s,received_qty=ifnull(received_qty,0)+%s,\
            Mat_ReceivedDate=%s,Pur_Status=%s,Mat_Remark=concat_ws(',',Mat_Remark,%s) \
            where Item_Pur=%s"
        params = [
            i.nt_name,
            i.new_received_qty,
            i.now,
            i.pur_status,
            i.remark,
            i.item_pur,
        ]
        db.execute(sql, params)
    else:
        sql = "update pur set Owner_M=%s,received_qty=ifnull(received_qty,0)+%s,\
            Mat_ReceivedDate=%s,Mat_Remark=concat_ws(',',Mat_Remark,%s) \
            where Item_Pur=%s"
        params = [i.nt_name, i.new_received_qty, i.now, i.remark, i.item_pur]
        db.execute(sql, params)

        sql = "update pur_plant set Owner_M=%s,received_qty=ifnull(received_qty,0)+%s,\
            Mat_ReceivedDate=%s,Mat_Remark=concat_ws(',',Mat_Remark,%s) \
            where Item_Pur=%s"
        params = [i.nt_name, i.new_received_qty, i.now, i.remark, i.item_pur]
        db.execute(sql, params)

    db.insert(
        "ssp.pur_update",
        {
            "item_pur": i.item_pur,
            "up_type": "M_StockIn",
            "remark1": f"StockInQty:{i.new_received_qty}",
            "owner": i.nt_name,
            "up_date": i.now,
        },
    )


def update_stock_qty(i):
    if i.application == "project":
        iqty = i.new_received_qty - i.remaining_qty
    else:
        iqty = i.new_received_qty
    sql = "update ssp.stock set qty=qty-%s where id=%s"
    params = (iqty, i.stock_id)
    db.execute(sql, params)


def receive_project(i):
    if re.match(r"^\d{7}$", i.stockno):
        stockoutdate2 = i.now
        owner2 = i.nt_name
    else:
        stockoutdate2 = None
        owner2 = None

    db.insert(
        "ssp.stockout",
        {
            "stockoutdate": i.now,
            "dept": i.dept,
            "type": "SM",
            "prtno": i.prtno,
            "deltapn": i.deltapn,
            "qty": i.new_received_qty - i.remaining_qty,
            "owner1": i.nt_name,
            "area": i.area,
            "source": "short",
            "checkcode": i.checkcode,
            "dept_id": i.dept_id,
            "memo": "short",
            "prt_id": i.prt_id,
            "bom_id": i.bom_id,
            "stock_id": i.stock_id,
            "stockno": i.stockno,
            "stockoutdate2": stockoutdate2,
            "lable": stockoutdate2,
            "owner2": owner2,
        },
    )


def receive_debug(i):
    if i.stockno:
        db.insert(
            "ssp.stockout",
            {
                "stockoutdate": i.now,
                "dept": i.dept,
                "type": "debug",
                "prtno": i.prtno,
                "deltapn": i.deltapn,
                "qty": i.new_received_qty,
                "owner1": i.nt_name,
                "area": i.area,
                "source": "debug",
                "checkcode": i.checkcode,
                "dept_id": i.dept_id,
                "memo": "debug",
                "prt_id": i.prt_id,
                "bom_id": i.bom_id,
                "stock_id": i.stock_id,
                "stockno": i.stockno,
                "stockoutdate2": i.now,
                "lable": i.now,
                "owner2": i.nt_name,
            },
        )
    label = {
        "limituse": i.dept,
        "type": i.smstatus,
        "stockno": i.rd,
        "designno": i.mat_remark,
        "qty": i.new_received_qty,
        "station": i.nt_name,
        "label_template": "stock_out",
        "id": i.deltapn,
        "area": i.area,
        "owner": i.nt_name,
        "des": i.mfgpn,
        "checkcode": i.deltapn,
        "prtno": i.prtno,
    }
    bg_label_print(pd.DataFrame([label]).to_json(orient="records"))

    if (not i.prtno) or (
        (i.application == "project") and (i.type == "收料") and (i.smstatus == "close")
    ):
        place = {"SH": "研发大楼3楼", "WH": "F2栋1楼", "HZ": "3幢2楼"}.get(i.area)
        subject = f"您申请的材料已到，请到{place}({i.nt_name})处领取"

        to = f"{i.rd}@deltaww.com"
        cc = f"{i.nt_name}@deltaww.com"
        df_mail = pd.DataFrame(
            [
                {
                    "台达料号": i.deltapn,
                    "描述": i.des,
                    "厂商": i.mfgname,
                    "厂商料号": i.mfgpn,
                    "到货数量": i.new_received_qty,
                    "采购数量": i.qty,
                }
            ]
        )
        title = f"<b>尊敬的 {i.rd} 先生/小姐：</b><br>您好！<br>您申请的如下材料已到,\
            请到{place}({i.nt_name})处领取，谢谢！"
        body = df_to_html(df_mail, title, link_text="")
        bg_mail(to, subject, body, cc)


def receive_stock(qty_list: list, stockno: str, df: pd.DataFrame):
    d = df.iloc[0].to_dict()
    stock_id = None

    stock = db.find_one(
        "ssp.stock", {"area": d["nt_area"], "checkcode": d["checkcode"]}
    )
    if stock:
        stock_id = stock.get("id")
    else:
        sql = "select limituse from safetystock \
            where checkcode=%s and status<>%s and area=%s \
            and limituse is not null"
        params = [d["checkcode"], "cancel", d["nt_area"]]
        limituse = db.execute_fetchone(sql, params)
        if limituse:
            limituse = limituse.get("limituse")
        else:
            limituse = "ALL"
        stock_id = db.insert(
            "ssp.stock",
            {
                "qty": 0,
                "area": d["nt_area"],
                "checkcode": d["checkcode"],
                "deltapn": d["deltapn"],
                "stockno": stockno,
                "des": d["des"],
                "mfgname": d["mfgname"],
                "mfgpn": d["mfgpn"],
                "limituse": limituse,
            },
        )
        ss = db.find_one(
            "ssp.safetystock",
            {"checkcode": d["checkcode"], "area": d["nt_area"]},
        )
        if not ss:
            db.insert(
                "ssp.safetystock",
                {
                    "area": d["nt_area"],
                    "status": "TBD",
                    "checkcode": d["checkcode"],
                    "deltapn": d["deltapn"],
                    "des": d["des"],
                    "mfgname": d["mfgname"],
                    "mfgpn": d["mfgpn"],
                    "adddate": d["now"],
                    "addtype": "NewStockIn",
                    "latestowner": d["pur"],
                },
            )

    sql = "update stockno_list set stock_id=%s where stockno=%s and type!=%s"
    params = (stock_id, stockno, "电子料架")
    db.execute(sql, params)

    for i, qty in enumerate(qty_list):
        sql = "update ssp.stock set qty=qty+%s where id=%s"
        params = (qty, stock_id)
        db.execute(sql, params)

        db.insert(
            "stock_modifyrecord",
            {
                "deltapn": d["deltapn"],
                "modify_type": "StockIn",
                "modify1": f"qty+{qty}",
                "m_date": d["now"],
                "owner": d["nt_name"],
            },
        )

        stockin_id = db.insert(
            "ssp.stockin",
            {
                "qty": qty,
                "owner": d["nt_name"],
                "area": d["nt_area"],
                "checkcode": d["checkcode"],
                "stockindate": d["now"],
                "deltapn": d["deltapn"],
            },
        )
        now = datetime.now() + timedelta(microseconds=i)
        uid = f"y{now:%y%m%d%H%M%S%f}"
        db.insert(
            "ssp.stock_uid",
            {
                "uid": uid,
                "stock_id": stock_id,
                "area": d["nt_area"],
                "checkcode": d["checkcode"],
                "stockno": stockno,
                "qty": qty,
                "stockin_id": stockin_id,
            },
        )

        d.update(
            {
                "qty": qty,
                "new_stock_no": stockno,
                "label_template": "stock_in",
                "id": uid,
            }
        )
        df_label = pd.DataFrame([d])
        task_import_uuid_to_my300(df_label)
        bg_label_print(df_label.to_json(orient="records"))

    return stock_id


def stock_in_label(qty_list: list, df: pd.DataFrame):
    c1 = df["prtno"] != ""
    c2 = df["new_received_qty"] > 0
    df = df.loc[c1 & c2]
    df = df.drop_duplicates(subset=["prtno"])
    df["label_template"] = "stock_in"
    df["new_stock_no"] = df["prtno"]

    for i, qty in enumerate(qty_list):
        now = datetime.now() + timedelta(microseconds=i)
        df["id"] = "y" + (df.index + int(f"{now:%y%m%d%H%M%S%f}")).astype(str)
        df["qty"] = df["new_received_qty"]
        task_import_uuid_to_my300(df)
        bg_label_print(df.to_json(orient="records"))
