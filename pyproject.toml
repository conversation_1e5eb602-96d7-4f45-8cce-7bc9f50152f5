[project]
name = "webssp"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "alchemical>=1.0.2",
    "chinese-calendar>=1.9.1",
    "dash<3",
    "dash-ag-grid>=31.3.1",
    "dash-bootstrap-components>=1.6.0",
    "dash-extensions>=1.0.18",
    "dash-iconify>=0.1.2",
    "dash-keyed-file-browser>=0.0.2",
    "dash-mantine-components==0.12.1",
    "dash-tabulator>=0.4.2",
    "dash-uploader==0.4.2",
    "datafiles>=2.2.3",
    "dbutils>=3.1.0",
    "diskcache>=5.6.3",
    "duckdb==1.3.0",
    "exchangelib>=5.4.3",
    "feffery-antd-charts>=0.1.0",
    "feffery-antd-components>=0.3.15",
    "feffery-utils-components>=0.2.0rc27",
    "flask-compress>=1.15",
    "huey>=2.5.2",
    "mysqlclient>=2.2.7",
    "openpyxl>=3.1.5",
    "orjson>=3.10.7",
    "pandas==1.5.3",
    "numpy==1.26.4",
    "pony>=0.7.19",
    "psycopg2-binary>=2.9.9",
    "python-dotenv>=1.0.1",
    "redis>=5.0.8",
    "sqlalchemy>=2.0.34",
    "waitress>=3.0.0",
    "xlrd>=2.0.1",
    "xlsxwriter>=3.2.0",
    "zen-engine>=0.48.0",
    "dash-player>=1.1.0",
    "dash-dynamic-grid-layout>=0.1.1",
    "feffery-dash-utils>=0.1.4",
    "polars>=1.18.0",
    "dash-snap-grid>=0.1.0",
    "decohints>=1.0.9",
    "pocketflow>=0.0.2",
    "openai>=1.84.0",
    "instructor>=1.8.3",
]

[tool.uv]
dev-dependencies = [
    "jurigged>=0.6",
    "reloading>=1.1.2",
    "reloadium>=1.5.1",
    "granian>=2.3.4",
]

[[tool.uv.index]]
url = "https://mirrors.aliyun.com/pypi/simple"
default = true
