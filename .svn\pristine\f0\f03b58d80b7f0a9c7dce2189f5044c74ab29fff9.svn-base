# -*- coding: utf-8 -*-
from datetime import datetime
from io import BytesIO

import dash_ag_grid as dag
import dash_mantine_components as dmc
import feffery_antd_components as fac
import feffery_utils_components as fuc
import numpy as np
import openpyxl
import pandas as pd
from dash import Patch
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    Input,
    Output,
    State,
    callback,
    clientside_callback,
    dcc,
    html,
    no_update,
)
from dash_iconify import DashIconify
from openpyxl.styles import Protection
from openpyxl.worksheet.datavalidation import DataValidation

from common import (
    bom_check,
    bom_outbound_result,
    bom_shortage_list,
    df_add_checkcode,
    df_add_mat_category,
    df_add_packaging,
    df_insert,
    df_update,
    get_nt_name,
    get_temp_pn,
    id_factory,
    parse_search,
    read_sql,
)
from components import notice
from config import BOM_DIR, SSP_DIR, cfg
from utils import db

id = id_factory(__name__)
bom_title = {
    "DeltaPN": "deltapn",
    "DESCRIPTION": "des",
    "VendorName": "mfgname",
    "VendorPart": "mfgpn",
    "source": "source",
    "DESGIN NO": "designno",
    "SMD/DIP": "packaging",
}
bom_type_dict = {"EE": "b_ee", "ME": "b_me", "MAG": "b_mag"}


def ongoing_btn_disable(bom_id, nt_name):
    res = db.find_one("ssp.bom_record", {"id": bom_id, "owner1": nt_name})
    if res:
        return False
    return True


def ongoing_1_layout(prt_id, bom_id, source, prtno):
    nt_name = get_nt_name()
    disabled = ongoing_btn_disable(bom_id, nt_name)

    df = bom_check(prt_id, bom_id, source)
    dfc = df.reindex(
        columns=[
            "prtno",
            "deltapn",
            "des",
            "mfgname",
            "mfgpn",
            "source",
            "designno",
            "packaging",
            "cat1",
            "cat2",
            "cat3",
            "pcb_remark",
        ]
    ).rename(
        columns={
            "pcb_remark": "BOM问题",
            "cat1": "类别1",
            "cat2": "类别2",
            "cat3": "类别3",
            "packaging": "SMD/DIP",
        }
    )
    dfc.columns = dfc.columns.str.upper()

    layout = dmc.Container(
        dmc.Stack(
            [
                dmc.Center(
                    [
                        fac.AntdCopyText(
                            text=dfc.to_html(index=False),
                            format="text/html",
                            id=id("clipboard"),
                            style={"margin-right": "auto"},
                            beforeIcon="复制表格",
                            afterIcon="复制成功",
                        ),
                        dmc.Text(f"{prtno}料表检查", fw=800, mr="auto"),
                    ],
                ),
                dag.AgGrid(
                    id=id("table"),
                    className="ag-theme-quartz",
                    columnDefs=[
                        {
                            "field": "action",
                            "headerName": "ACTION",
                            "cellEditor": {"function": "AllFunctionalComponentEditors"},
                            "cellEditorParams": {
                                "component": dmc.Select(data=["delete"])
                            },
                            "cellEditorPopup": True,
                            "singleClickEdit": True,
                            "width": 80,
                        },
                        {"field": "id", "headerName": "id", "width": 80, "hide": True},
                        {
                            "field": "deltapn",
                            "headerName": "DeltaPN",
                            "width": 100,
                            "cellEditor": {"function": "AllFunctionalComponentEditors"},
                            "cellEditorParams": {
                                "component": dmc.Select(data=["NEW PART"])
                            },
                            "cellEditorPopup": True,
                            "singleClickEdit": True,
                        },
                        {"field": "des", "headerName": "DES", "width": 100},
                        {"field": "mfgname", "headerName": "MFGName", "width": 80},
                        {"field": "mfgpn", "headerName": "MFGPN", "width": 100},
                        {
                            "field": "source",
                            "headerName": "Source",
                            "width": 80,
                            "cellEditor": {"function": "AllFunctionalComponentEditors"},
                            "cellEditorParams": {
                                "component": dmc.Select(data=["100", "0"])
                            },
                            "cellEditorPopup": True,
                            "singleClickEdit": True,
                        },
                        {"field": "designno", "headerName": "DESIGNNo", "width": 80},
                        {
                            "field": "packaging",
                            "headerName": "SMD/DIP",
                            "width": 80,
                            "cellEditor": {"function": "AllFunctionalComponentEditors"},
                            "cellEditorParams": {
                                "component": dmc.Select(data=["SMD", "DIP"])
                            },
                            "cellEditorPopup": True,
                            "singleClickEdit": True,
                        },
                        {
                            "field": "cat1",
                            "headerName": "类别1",
                            "cellEditor": {"function": "AllFunctionalComponentEditors"},
                            "cellEditorParams": {
                                "component": dmc.Select(data=cfg.cat1)
                            },
                            "cellEditorPopup": True,
                            "singleClickEdit": True,
                            "width": 120,
                        },
                        {
                            "field": "cat2",
                            "headerName": "类别2",
                            "cellEditor": {"function": "AllFunctionalComponentEditors"},
                            "cellEditorParams": {"component": dmc.Select(data=[])},
                            "cellEditorPopup": True,
                            "singleClickEdit": True,
                            "width": 120,
                        },
                        {
                            "field": "cat3",
                            "headerName": "类别3",
                            "cellEditor": {"function": "AllFunctionalComponentEditors"},
                            "cellEditorParams": {"component": dmc.Select(data=[])},
                            "cellEditorPopup": True,
                            "singleClickEdit": True,
                            "width": 120,
                        },
                        {"field": "pcb_remark", "headerName": "BOM问题", "width": 250},
                    ],
                    rowData=df.to_dict(orient="records"),
                    # columnSize="sizeToFit",
                    defaultColDef={
                        "resizable": True,
                        "sortable": True,
                        "filter": True,
                        "wrapHeaderText": True,
                        "autoHeaderHeight": True,
                        "editable": True,
                    },
                    dashGridOptions={
                        "rowSelection": "single",
                        "stopEditingWhenCellsLoseFocus": True,
                        "singleClickEdit": True,
                        "rowHeight": 35,
                    },
                    style={"height": 450},
                ),
                dmc.Group(
                    [
                        dmc.Button("提交", id=id("submit"), disabled=disabled),
                        dmc.Button(
                            "跳至下一步",
                            id=id("ongoing1-next"),
                            color="red",
                            disabled=disabled,
                        ),
                    ],
                    grow=True,
                ),
                dmc.Space(h=5),
                fuc.FefferyExecuteJs(id=id("js")),
                dcc.Store(id=id("store")),
            ],
            gap=5,
        ),
        fluid=True,
    )
    return layout


def ongoing_2_layout(bom_id, prtno):
    sql = "select * from bom_shortage where bom_id=%s and status!=%s"
    df = read_sql(sql, params=[bom_id, "ok"])

    nt_name = get_nt_name()
    disabled = ongoing_btn_disable(bom_id, nt_name)

    layout = dmc.Container(
        dmc.Stack(
            [
                dmc.Center(
                    [
                        dmc.Text(prtno, fw=800),
                        dmc.Text("缺料建议", fw=800),
                    ]
                ),
                dag.AgGrid(
                    id=id("table2"),
                    className="ag-theme-quartz",
                    columnDefs=[
                        {"field": "id", "headerName": "id", "width": 80, "hide": True},
                        {"field": "designno", "headerName": "DESIGNNo", "width": 80},
                        {"field": "deltapn", "headerName": "DeltaPN", "width": 100},
                        {"field": "des", "headerName": "DES", "width": 100},
                        {"field": "mfgname", "headerName": "MFGName", "width": 80},
                        {"field": "mfgpn", "headerName": "MFGPN", "width": 100},
                        {"field": "area", "headerName": "Area", "width": 60},
                        {"field": "demand_qty", "headerName": "需求数量", "width": 80},
                        {"field": "pur_ss", "headerName": "替代料", "width": 80},
                    ],
                    rowData=df.to_dict(orient="records"),
                    columnSize="sizeToFit",
                    defaultColDef={
                        "resizable": True,
                        "sortable": True,
                        "filter": True,
                        "wrapHeaderText": True,
                        "autoHeaderHeight": True,
                        "editable": True,
                    },
                    dashGridOptions={
                        "rowSelection": "single",
                        "stopEditingWhenCellsLoseFocus": True,
                        "singleClickEdit": True,
                        "rowHeight": 35,
                    },
                    style={"height": 300},
                ),
                dmc.Group(
                    [
                        dmc.Button("提交", id=id("submit2"), disabled=disabled),
                        dmc.Button(
                            "跳至下一步",
                            id=id("ongoing2-next"),
                            color="red",
                            disabled=disabled,
                        ),
                    ],
                    # position="apart",
                    grow=True,
                    # spacing=200,
                ),
                dmc.Space(h=5),
                # fuc.FefferyReload(id=id("reload2")),
                fuc.FefferyExecuteJs(id=id("js")),
            ],
            gap=5,
        ),
        fluid=True,
    )
    return layout


def ongoing_3_layout(bom_id, prtno):
    sql = "select * from bom_shortage \
        where bom_id=%s and (deltapn_ss is not null or status!=%s)"
    df = read_sql(sql, params=[bom_id, "ok"])
    df = df.sort_values(by="status", ascending=False)

    nt_name = get_nt_name()
    disabled = ongoing_btn_disable(bom_id, nt_name)
    d1, d2, d3 = disabled, disabled, disabled
    if not disabled:
        if df.empty:
            d1 = True
            d2 = True
        else:
            d3 = True

    layout = dmc.Container(
        dmc.Stack(
            [
                dmc.Center(
                    [
                        dmc.Text(prtno, fw=800),
                        dmc.Text("缺料确认", fw=800),
                    ]
                ),
                dag.AgGrid(
                    id=id("table3"),
                    className="ag-theme-quartz",
                    columnDefs=[
                        {"field": "id", "headerName": "id", "width": 80, "hide": True},
                        {"field": "designno", "headerName": "DESIGNNo", "width": 80},
                        {"field": "deltapn", "headerName": "DeltaPN", "width": 100},
                        {"field": "des", "headerName": "DES", "width": 100},
                        {"field": "mfgname", "headerName": "MFGName", "width": 80},
                        {"field": "mfgpn", "headerName": "MFGPN", "width": 100},
                        {"field": "area", "headerName": "Area", "width": 60},
                        {"field": "status", "headerName": "Status", "width": 60},
                        {"field": "demand_qty", "headerName": "需求数量", "width": 80},
                        {"field": "deltapn_ss", "headerName": "替代料", "width": 80},
                    ],
                    rowData=df.to_dict(orient="records"),
                    columnSize="sizeToFit",
                    defaultColDef={
                        "resizable": True,
                        "sortable": True,
                        "filter": True,
                        "wrapHeaderText": True,
                        "autoHeaderHeight": True,
                        # "editable": True,
                    },
                    dashGridOptions={
                        "rowSelection": "single",
                        "stopEditingWhenCellsLoseFocus": True,
                        "singleClickEdit": True,
                        "rowHeight": 35,
                    },
                    style={"height": 400},
                ),
                dmc.Group(
                    [
                        dmc.Button(
                            "下载",
                            id=id("download-btn"),
                            disabled=d1,
                            leftSection=DashIconify(
                                icon="material-symbols:download-sharp", width=20
                            ),
                        ),
                        fac.AntdUpload(
                            apiUrl="/upload/bom/",
                            id=id("upload"),
                            uploadId=prtno,
                            disabled=d2,
                            buttonProps={"block": True, "style": {"height": "37px"}},
                            showUploadList=False,
                            showSuccessMessage=False,
                        ),
                        dmc.Button(
                            "跳到下一步",
                            id=id("ongoing3-next"),
                            color="red",
                            disabled=d3,
                            rightIcon=DashIconify(
                                icon="ic:baseline-double-arrow", width=25
                            ),
                        ),
                    ],
                    grow=True,
                    gap=200,
                ),
                dcc.Download(id=id("download")),
                dmc.Space(h=5),
                fuc.FefferyReload(id=id("reload2")),
                fuc.FefferyExecuteJs(id=id("js")),
            ],
            gap=5,
        ),
        fluid=True,
    )
    return layout


def ongoing_4_layout(bom_id, prtno):
    nt_name = get_nt_name()
    disabled = ongoing_btn_disable(bom_id, nt_name)

    layout = dmc.Container(
        dmc.Stack(
            [
                dmc.Center(
                    [
                        dmc.Text(prtno, fw=800),
                        dmc.Text("料表扣库", fw=800),
                    ]
                ),
                dmc.Divider(),
                dmc.Group(
                    [
                        dmc.Button(
                            "提交扣库",
                            id=id("ongoing4-submit"),
                            color="green",
                            disabled=disabled,
                        ),
                        dmc.Button(
                            "提前查库",
                            id=id("ongoing4-check-stock"),
                            color="red",
                            disabled=disabled,
                        ),
                    ],
                    grow=True,
                ),
                fuc.FefferyExecuteJs(id=id("js")),
                dcc.Download(id=id("ongoing4-download")),
            ],
            gap=5,
        ),
    )
    return layout


def layout(**query):
    bom_id = query.get("bom_id")
    prt_id = query.get("prt_id")
    source = query.get("source")
    prtno = query.get("prtno")
    step = query.get("step")
    if step == "1":
        layout = ongoing_1_layout(prt_id, bom_id, source, prtno)
    elif step == "2":
        layout = ongoing_2_layout(bom_id, prtno)
    elif step == "3":
        layout = ongoing_3_layout(bom_id, prtno)
    elif step == "4":
        layout = ongoing_4_layout(bom_id, prtno)
    else:
        layout = html.Div("待开发")
    return layout


@callback(
    Output(id("table"), "columnDefs"),
    Input(id("table"), "cellValueChanged"),
)
def update_ongoing1_table_columndefs(cell_changed):
    if not cell_changed:
        raise PreventUpdate

    cell_changed = cell_changed[0]
    field = cell_changed.get("colId")

    if field not in ("cat1", "cat2"):
        raise PreventUpdate

    value = cell_changed.get("value")
    if not value:
        raise PreventUpdate
    columns = Patch()
    if field == "cat1":
        sql = "select distinct pur_category2 as cat2 from ssp_ce.a_mat_catalogue \
            where category_1=%s"
        cat2 = read_sql(sql, params=[value])
        columns[10]["cellEditorParams"]["options"] = cat2["cat2"].tolist()
    elif field == "cat2":
        cat1 = cell_changed.get("data").get("cat1")
        sql = "select distinct pur_category_3 as cat3 from ssp_ce.a_mat_catalogue \
            where category_1=%s and category_2=%s"
        cat3 = read_sql(sql, params=[cat1, value])
        columns[11]["cellEditorParams"]["options"] = cat3["cat3"].tolist()
    return columns


@callback(
    Input(id("table"), "cellValueChanged"),
    State("user", "data"),
)
def new_smd_packaging(cell_changed, user):
    if not cell_changed:
        raise PreventUpdate

    cell_changed = cell_changed[0]
    field = cell_changed.get("colId")

    if field != "packaging":
        raise PreventUpdate

    value = cell_changed.get("value")
    if not value:
        raise PreventUpdate
    if value != "SMD":
        raise PreventUpdate
    deltapn = cell_changed.get("data").get("deltapn")
    if not deltapn:
        raise PreventUpdate

    if not deltapn[0].isnumeric():
        raise PreventUpdate
    nt_name = user.get("nt_name")
    ts = int(datetime.now().timestamp())
    sql = "replace into ssp.smddip (c_deltapn, owner, r_sequence) \
        values (%s, %s, %s)"

    db.execute(sql, (deltapn, nt_name, ts))


@callback(
    Output("global-notice", "children"),
    Output(id("table"), "rowData"),
    Input(id("submit"), "n_clicks"),
    State(id("table"), "rowData"),
    State("url", "search"),
)
def ongoing1_submit(n_clicks, data, search):
    if not n_clicks:
        raise PreventUpdate

    url = parse_search(search)
    bom_id = url.get("bom_id")
    prt_id = url.get("prt_id")
    source = url.get("source")
    if source == "change":
        table = "ssp.bom_change"
    else:
        table = "ssp.bom_initial"

    df = pd.DataFrame(data)
    if df.empty:
        raise PreventUpdate

    if "action" not in df.columns:
        # return notice("请选择action", "error"), no_update
        df["action"] = "update"

    df = df_add_checkcode(df)
    df = df_add_packaging(df)
    df = df_add_mat_category(df)
    df["action"].fillna("update", inplace=True)

    dfd = df.loc[df["action"] == "delete"]
    if not dfd.empty:
        for item in dfd.itertuples():
            db.delete(table, {"id": item.id})

    dfu = df.loc[df["action"] == "update"]
    if not dfu.empty:
        dfu["np"] = dfu.groupby(["mfgname", "mfgpn"])["deltapn"].transform(
            lambda x: x.replace("", "NEW PART") if (x == "NEW PART").any() else x
        )
        c1 = dfu["deltapn"] == ""
        c2 = dfu["np"] == "NEW PART"
        dfu["deltapn"] = np.where(c1 & c2, dfu["np"], dfu["deltapn"])

        c1 = dfu["deltapn"] == "NEW PART"
        dfn = dfu.loc[c1]

        if not dfn.empty:
            if ((dfn["mfgname"] == "") | (dfn["mfgpn"] == "")).any():
                return notice("编写临时料号,厂商和厂商料号不能为空", "error"), no_update

            dfn["des"] = dfn["des"].fillna("")
            dfn["des"] = np.where(
                dfn["des"] == "",
                dfn["cat1"] + " " + dfn["cat2"] + " " + dfn["packaging"],
                dfn["des"],
            )
            dfn = get_temp_pn(dfn)
            dfu = dfu.merge(
                dfn.add_suffix("_x"), left_index=True, right_index=True, how="left"
            )
            c1 = dfu["temp_pn_x"].notna()
            dfu["deltapn"] = np.where(c1, dfu["temp_pn_x"], dfu["deltapn"])
            dfu["checkcode"] = np.where(c1, dfu["temp_pn_x"], dfu["checkcode"])
            dfu["des"] = np.where(c1, dfu["des_x"], dfu["des"])

        dfu = dfu.replace({np.nan: None, "": None})
        dfu["packaging"] = dfu.groupby("deltapn")["packaging"].transform(
            lambda x: x.ffill().bfill()
        )
        dfu["cat1"] = dfu.groupby("deltapn")["cat1"].transform(
            lambda x: x.ffill().bfill()
        )
        dfu["cat2"] = dfu.groupby("deltapn")["cat2"].transform(
            lambda x: x.ffill().bfill()
        )
        dfu["cat3"] = dfu.groupby("deltapn")["cat3"].transform(
            lambda x: x.ffill().bfill()
        )
        dfu = dfu.reindex(
            columns=[
                "id",
                "designno",
                "deltapn",
                "des",
                "mfgname",
                "mfgpn",
                "checkcode",
                "source",
                "packaging",
                "cat1",
                "cat2",
                "cat3",
            ]
        )
        df_update(table, dfu)
    dfx = bom_check(prt_id, bom_id, source)
    return notice("提交成功"), dfx.to_dict(orient="records")


# @callback(
#     Output(id("ongoing1-download"), "data"),
#     Input(id("ongoing1-download-btn"), "n_clicks"),
#     State("url", "search"),
#     State(id("table"), "rowData"),
# )
# def ongoing1_download(n_clicks, search, data):
#     if not n_clicks:
#         raise PreventUpdate
#     df = pd.DataFrame(data)
#     if df.empty:
#         raise PreventUpdate

#     url = parse_search(search)
#     prtno = url.get("prtno")
#     df = df.reindex(
#         columns=[
#             "id",
#             "designno",
#             "deltapn",
#             "des",
#             "mfgname",
#             "mfgpn",
#             "checkcode",
#             "source",
#             "packaging",
#             "cat1",
#             "cat2",
#             "cat3",
#         ]
#     )
#     return dcc.send_data_frame(df.to_excel, f"{prtno}_check.xlsx", index=False)


@callback(
    Output(id("js"), "jsString"),
    Input(id("ongoing1-next"), "n_clicks"),
    State("url", "search"),
    State(id("table"), "rowData"),
)
def ongoing1_next(n_clicks, search, data):
    if not n_clicks:
        raise PreventUpdate
    df = pd.DataFrame(data)
    if not df.empty:
        if (
            df["pcb_remark"]
            .str.contains(
                "位置号空白|位置号重复|SMD/DIP需设定|台达料号空白|多个主source|无主source|材料类别需设定"
            )
            .any()
        ):
            return "window.alert('存在不可跳过问题点,请修改')"

    url = parse_search(search)
    prt_id = url.get("prt_id")
    bom_id = url.get("bom_id")
    source = url.get("source")
    dfx = bom_shortage_list(prt_id, bom_id, source)
    dfx = dfx.loc[dfx["status"] != "ok"]

    db.update(
        "ssp.bom_record",
        {
            "id": bom_id,
            "processingmode": "3",
            "check_date": datetime.now(),
            "problem_qty": dfx.shape[0],
        },
    )
    return "close()"


@callback(
    Output("global-notice", "children"),
    Output(id("table2"), "rowData"),
    Input(id("submit2"), "n_clicks"),
    State(id("table2"), "rowData"),
    State("user", "data"),
)
def ongoing2_submit(n_clicks, data, user):
    if not n_clicks:
        raise PreventUpdate
    df = pd.DataFrame(data)
    if "pur_ss" not in df.columns:
        return notice("替代料不能为空", "error"), no_update
    # url = parse_search(search)
    # record_id = url.get("id")
    df["pur_ss"] = df["pur_ss"].fillna("")

    df1 = df.loc[df["pur_ss"] != ""]
    if df1.empty:
        raise PreventUpdate
    dfx = df.loc[~df.index.isin(df1.index)]

    params = df1["pur_ss"].unique().tolist()
    sql = "select distinct area,deltapn as pur_ss,des as des_pur_ss,qty \
        from stock where deltapn in %s"
    stock = read_sql(sql, params=[params])
    stock = stock.drop_duplicates(["area", "pur_ss"])
    df1 = df1.merge(stock, on=["area", "pur_ss"], how="left")
    df1["qty"] = df1["qty"].fillna(0)
    df1x = df1.loc[df1["qty"] < df1["demand_qty"], "pur_ss"]
    if not df1x.empty:
        return notice(f"{df1x.tolist()}`库存数量不满足`", "error"), no_update

    for i in df1.itertuples():
        db.update(
            "ssp.bom_shortage",
            {
                "id": i.id,
                "deltapn_ss": i.pur_ss,
                "pur_ss": i.pur_ss,
                "des_ss": i.des_pur_ss,
                "status": "ok",
            },
        )
    return notice("提交成功"), dfx.to_dict(orient="records")


@callback(
    Output(id("js"), "jsString"),
    Input(id("ongoing2-next"), "n_clicks"),
    State("url", "search"),
)
def ongoing2_next(n_clicks, search):
    if not n_clicks:
        raise PreventUpdate

    url = parse_search(search)
    bom_id = url.get("bom_id")

    sql = "select id from bom_shortage \
        where bom_id=%s and (deltapn_ss is not null or status!=%s)"
    dfx = read_sql(sql, params=[bom_id, "ok"])

    db.update(
        "ssp.bom_record",
        {"id": bom_id, "processingmode": "3", "problem_qty": dfx.shape[0]},
    )
    return "close()"


@callback(
    Output("global-notice", "children"),
    Output(id("download"), "data"),
    Input(id("download-btn"), "n_clicks"),
    State(id("table3"), "rowData"),
    State("url", "search"),
)
def ongoing3_download(n_clicks, data, search):
    if not n_clicks:
        raise PreventUpdate

    df = pd.DataFrame(data)
    df = df.reindex(
        columns=[
            "id",
            "packaging",
            "designno",
            "deltapn",
            "des",
            "mfgname",
            "mfgpn",
            "demand_qty",
            "area",
            "des_ss",
            "deltapn_ss",
            # "designno",
            # "status",
            "rd_confirm",
            # "remark",
        ]
    )
    df = df.fillna("")
    df = df.sort_values(by=["deltapn_ss"], ascending=False)
    url = parse_search(search)
    prtno = url.get("prtno")
    bom_id = url.get("bom_id")
    source = url.get("source")

    if source == "change":
        sql = "select deltapn,checkcode,des,mfgname,mfgpn,designno,source,bomtype \
            from ssp.bom_change where bom_id=%s"
    else:
        sql = "select deltapn,checkcode,des,mfgname,mfgpn,designno,source,bomtype \
            from ssp.bom_initial where bom_id=%s"
    bom = read_sql(sql, params=[bom_id])
    bom.dropna(axis=1, how="all", inplace=True)

    wb = openpyxl.load_workbook(SSP_DIR / "program" / "template" / "rd_confirm.xlsx")

    if not bom.empty:
        ws0 = wb.worksheets[0]
        data = bom.values.tolist()
        for index, row in enumerate(data, start=2):
            for col_num, value in enumerate(row, start=1):
                ws0.cell(row=index, column=col_num).value = value

    if not df.empty:
        ws = wb.worksheets[1]
        data = df.values.tolist()
        for index, row in enumerate(data, start=3):
            for col_num, value in enumerate(row, start=1):
                ws.cell(row=index, column=col_num).value = value

        dvl = DataValidation(type="list", formula1='"Y,N"')
        dvm = DataValidation(type="list", formula1='"NEW PART"')
        dvn = DataValidation(type="list", formula1='"Y"')
        dvo = DataValidation(
            type="list", formula1='"缺料作业(样制不补焊),RD提供(手上现有实物)"'
        )

        ws.add_data_validation(dvl)
        ws.add_data_validation(dvm)
        ws.add_data_validation(dvn)
        ws.add_data_validation(dvo)

        for row in ws.iter_rows(min_row=3):
            row[11].protection = Protection(locked=False)
            row[12].protection = Protection(locked=False)
            row[13].protection = Protection(locked=False)
            row[14].protection = Protection(locked=False)

            row[11].number_format = "@"
            row[12].number_format = "@"
            row[13].number_format = "@"
            row[14].number_format = "@"

            dvm.add(row[12])
            dvn.add(row[13])
            dvo.add(row[14])

            if row[10].value:
                dvl.add(row[11])
            else:
                row[11].protection = Protection(locked=True)

        dvl.showErrorMessage = True
        dvn.showErrorMessage = True
        dvm.showErrorMessage = False
        dvo.showErrorMessage = False

    now = datetime.now()
    dst_file = BOM_DIR / prtno / f"{prtno}_RD确认{now:%y%m%d%H%M%S}.xlsx"
    wb.save(dst_file)

    db.update(
        "ssp.bom_record", {"prepare_date": f"{now:%Y-%m-%d %H:%M:%S}", "id": bom_id}
    )

    return notice("下载成功"), dcc.send_file(dst_file)


@callback(
    Output("global-notice", "children"),
    Output(id("js"), "jsString"),
    Input(id("upload"), "lastUploadTaskRecord"),
    State("url", "search"),
)
def ongoing3_upload(upload, search):
    if not upload:
        raise PreventUpdate

    url = parse_search(search)
    prt_id = url.get("prt_id")
    bom_id = url.get("bom_id")
    prtno = url.get("prtno")
    source = url.get("source")

    fp = BOM_DIR / upload.get("taskId") / upload.get("fileName")
    stem = fp.stem
    if prtno not in stem:
        return notice("文件名需包含相同项目号", "error"), no_update

    r = db.find_one("ssp.bom_record", {"id": bom_id})
    item = r.get("prepare_date")
    if f"{item:%y%m%d%H%M%S}" not in stem:
        return notice("文件名编号需与下载的一致", "error"), no_update

    xls = pd.read_excel(
        fp, dtype=str, keep_default_na=False, header=1, sheet_name=[1, 2]
    )
    df = xls[1]
    dfc = xls[2]
    # !===========上传change==============
    if not dfc.empty:
        sql = "select distinct deltapn from ssp_csg.mat_info where deltapn in %s"
        params = [dfc["deltapn"].tolist()]
        mat = read_sql(sql, params=params)
        c1 = ~dfc["deltapn"].isin(mat["deltapn"])
        xx = dfc["deltapn"][c1]
        if not xx.empty:
            return notice(f"Change料号{xx.tolist()}不存在,请修改", "error"), no_update

        sql = "select designno from ssp.bom_initial where prt_id=%s"
        params = [prt_id]
        dfd = read_sql(sql, params=params)
        if not dfc["designno"].isin(dfd["designno"]).all():
            return notice("Change位置号不存在,请修改", "error"), no_update

        dfc = df_add_checkcode(dfc)
        dfc = df_add_packaging(dfc)
        dfc = df_add_mat_category(dfc)
        dfc["prt_id"] = prt_id
        dfc["bom_id"] = bom_id
        dfc["change_type"] = "change"
        dfc["prtno"] = prtno
        dfc["source"] = "100"
        dfc["bomtype"] = "change"
        df_insert("ssp.bom_change", dfc)

    df.index = df.index + 3
    c1 = df["deltapn_ss"] != ""
    c2 = df["rd_confirm"] == ""
    if (c1 & c2).any():
        return notice("替代料确认不能为空", "error"), no_update

    c1 = df["rd_confirm"] == "Y"
    c2 = df["rd_ss"] != ""
    c3 = df["deltapn_ss"] != ""
    dfx = df.loc[c1 & c2 & c3]
    if not dfx.empty:
        return notice(f"第{dfx.index.tolist()}行,RD替代料须为空", "error"), no_update

    c1 = df["rd_confirm"] == "N"
    c2 = df["deltapn_ss"] == ""
    c3 = df["rd_ss"] == ""
    c4 = df["apply_main_source"] == "Y"

    dfx = df.loc[(c1 & c3 & c4) | (c2 & c3 & c4)]
    if not dfx.empty:
        return notice(f"第{dfx.index.tolist()}行,主料仍需备料无效", "error"), no_update

    df["rd_ss"] = df["rd_ss"].str.strip().str.upper()

    # *========RD替代料==============
    new_part = df.loc[df["rd_ss"] == "NEW PART"]
    if not new_part.empty:
        df1 = new_part["rd_remark"].str.extract("(.+)\+(.+)", expand=True)
        new_part[["mfgname", "mfgpn"]] = df1
        new_part = get_temp_pn(new_part)

        df = df.merge(
            new_part.add_suffix("_x"), left_index=True, right_index=True, how="left"
        )
        c1 = df["temp_pn_x"].notna()
        df["rd_ss"] = np.where(c1, df["temp_pn_x"], df["rd_ss"])
        # df["mfgname"] = np.where(c1, df["mfgname_x"], df["mfgname"])
        # df["mfgpn"] = np.where(c1, df["mfgpn_x"], df["mfgpn"])

    df["confirmed_ss"] = ""
    c1 = df["rd_confirm"] == "Y"
    c2 = df["deltapn_ss"] != ""
    df["confirmed_ss"] = np.where(c1 & c2, df["deltapn_ss"], df["confirmed_ss"])

    c1 = df["rd_ss"] != ""
    df["confirmed_ss"] = np.where(c1, df["rd_ss"], df["confirmed_ss"])

    # *========changelist,如果用了替代料，直接修改bom_change表相应位置号的料号==============
    if source == "change":
        dfx = df.loc[df["confirmed_ss"] != ""]
        if not dfx.empty:
            dfx = dfx[["confirmed_ss", "designno"]]
            dfx = dfx.rename(columns={"confirmed_ss": "deltapn"})
            dfx = df_add_checkcode(dfx)
            dfx["bom_id"] = bom_id
            dfx["designno"] = dfx["designno"].str.split(",")
            dfx = dfx.explode("designno")
            sql = "update ssp.bom_change set deltapn=%s,checkcode=%s,\
                des=%s,mfgname=%s,mfgpn=%s where bom_id=%s and designno=%s"
            dfx = dfx[
                [
                    "deltapn",
                    "checkcode",
                    "des",
                    "mfgname",
                    "mfgpn",
                    "bom_id",
                    "designno",
                ]
            ]
            db.execute_many(sql, dfx.values.tolist())

    df = df.reindex(
        columns=[
            "confirmed_ss",
            "rd_ss",
            "rd_confirm",
            "rd_remark",
            "apply_main_source",
            "id",
        ]
    )
    df_update("ssp.bom_shortage", df)
    db.update(
        "ssp.bom_record",
        {"id": bom_id, "processingmode": "4", "confirmed_date": datetime.now()},
    )
    return notice("上传成功"), "close()"


@callback(
    Output(id("js"), "jsString"),
    Input(id("ongoing3-next"), "n_clicks"),
    State("url", "search"),
)
def ongoing3_next(n_clicks, search):
    if not n_clicks:
        raise PreventUpdate

    url = parse_search(search)
    bom_id = url.get("bom_id")

    db.update(
        "ssp.bom_record",
        {"id": bom_id, "processingmode": "4", "confirmed_date": datetime.now()},
    )
    return "close()"


clientside_callback(
    """
    function updateLoadingState(n_clicks) {
        return true
    }
    """,
    Output(id("ongoing4-submit"), "loading"),
    Input(id("ongoing4-submit"), "n_clicks"),
    prevent_initial_call=True,
)


@callback(
    Output("global-notice", "children"),
    Output(id("ongoing4-download"), "data"),
    Output(id("ongoing4-submit"), "disabled"),
    Input(id("ongoing4-submit"), "n_clicks"),
    State("url", "search"),
    State("user", "data"),
)
def ongoing4_submit(n_clicks, search, user):
    if not n_clicks:
        raise PreventUpdate
    url = parse_search(search)
    bom_id = int(url.get("bom_id"))
    prt_id = int(url.get("prt_id"))
    source = url.get("source")
    prtno = url.get("prtno")
    nt_name = user.get("nt_name")

    if source == "change":
        res = db.find_one(
            "ssp.bom_record",
            {"prt_id": prt_id, "source": "bom", "status": "processing"},
        )
        if res:
            return notice("请先完成BOM扣库,再执行change扣库", "error"), no_update, False
    df, start_date = bom_outbound_result(prt_id, bom_id, nt_name, source)
    # todo 解锁后的变更邮件提醒样制，锁定
    # res = task_bom_result(df, start_date, source, prtno, prt_id, bom_id)

    # * ===========扣库结果excel=========
    if source == "change":
        sql = "select deltapn,checkcode,des,mfgname,mfgpn,designno,source,bomtype \
            from ssp.bom_change where bom_id=%s"
    else:
        sql = "select deltapn,checkcode,des,mfgname,mfgpn,designno,source,bomtype \
            from ssp.bom_initial where bom_id=%s"

    df1 = read_sql(sql, params=[bom_id])
    df1.dropna(axis=1, how="all", inplace=True)

    sql = "select deltapn,des,mfgname,mfgpn,qty,start_date,mat_remark,\
        pur_status from ssp.pur where prt_id=%s and application=%s"
    df2 = read_sql(sql, params=[prt_id, "project"])
    df2 = df2.loc[df2["start_date"] == start_date]

    sql = "select deltapn,checkcode,des,mfgname,mfgpn,designno,stockno,packaging,\
        up_date from ssp.smbom where prt_id=%s"
    df3 = read_sql(sql, params=[prt_id])

    if not df2.empty:
        designno = df3.groupby("deltapn", as_index=False).agg(
            {"designno": lambda x: ",".join(set(x))}
        )
        df2 = df2.merge(designno, on="deltapn", how="left")

    xls = [
        {"sheet_name": "原始BOM", "data": df1},
        {"sheet_name": "缺料清单", "data": df2},
        {"sheet_name": "样制BOM", "data": df3},
    ]

    folder = BOM_DIR / prtno
    if not folder.exists():
        folder.mkdir()

    df.to_excel(
        folder / f"{prtno}_计算过程{datetime.now():%y%m%d%H%M%S}.xlsx", index=False
    )

    xls_file = folder / f"{prtno}_扣库结果{datetime.now():%y%m%d%H%M%S}.xlsx"
    with pd.ExcelWriter(xls_file, engine="xlsxwriter") as writer:
        for i in xls:
            dfi = i["data"]
            dfi.to_excel(writer, sheet_name=i["sheet_name"], index=False)
            worksheet = writer.sheets[i["sheet_name"]]  # pull worksheet object
            if not dfi.empty:
                for i, col in enumerate(dfi.columns):
                    width = max(dfi[col].apply(lambda x: len(str(x))).max(), len(col))
                    worksheet.set_column(i, i, width)
        writer.save()

    return notice("扣库成功"), dcc.send_file(xls_file), True


@callback(
    Output(id("ongoing4-download"), "data"),
    Output(id("ongoing4-check-stock"), "disabled"),
    Input(id("ongoing4-check-stock"), "n_clicks"),
    State("url", "search"),
)
def check_stock_download_data(n1, search):
    if not n1:
        raise PreventUpdate
    url = parse_search(search)
    prtno = url.get("prtno")
    bom_id = int(url.get("bom_id"))

    sql = "select status,designno,checkcode,deltapn,des,mfgname,mfgpn,qpa,\
        demand_qty,stock_qty,short_qty,area,packaging,gmt_create \
            from ssp.bom_shortage where bom_id=%s"
    df = read_sql(sql, params=[bom_id])
    if df.empty:
        raise PreventUpdate
    df.dropna(axis=1, how="all", inplace=True)

    bio = BytesIO()
    with pd.ExcelWriter(bio, engine="openpyxl") as writer:
        df.to_excel(writer, sheet_name=prtno, index=False)
        writer.save()
        bio.seek(0)
        workbook = bio.read()
        return dcc.send_bytes(workbook, f"{prtno}_提前查库.xlsx"), True
