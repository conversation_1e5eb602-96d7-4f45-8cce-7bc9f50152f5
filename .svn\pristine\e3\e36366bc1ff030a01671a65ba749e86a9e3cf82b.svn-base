# -*- coding: utf-8 -*-
import os
from pathlib import Path

import duckdb as dk
import MySQLdb
import MySQLdb.cursors
import psycopg2.extras
import zen
from alchemical import Alchemical
from datafiles import auto
from dbutils.pooled_db import PooledDB
from diskcache import Cache
from dotenv import load_dotenv

load_dotenv()
SSP_DIR = Path(os.getenv("SSP_DIR"))
UPLOAD_FOLDER_ROOT = SSP_DIR / "3. Document backup" / "upload"
CE_UPLOAD_FOLDER = SSP_DIR / "3. Document backup" / "ce"
BOM_DIR = SSP_DIR / "3. Document backup" / "BOM_Record"
ALCHEMICAL_DATABASE_URL = os.getenv("ALCHEMICAL_DATABASE_URL")
SECRET_KEY = os.getenv("SECRET_KEY")
cfg = auto("./utils/config/config.yml")
# dk.sql("install 'C:\\Users\\<USER>\\mysql_scanner.duckdb_extension'")
# dk = duckdb.connect("~/duck.db")
# dk.sql("install mysql")
dk.sql("attach '' as my (type mysql)")


def to_dict(self):
    return {c.name.lower(): getattr(self, c.name, None) for c in self.__table__.columns}


def loader(key):
    with open("./utils/" + key, "r") as f:
        return f.read()


rule = zen.ZenEngine({"loader": loader})
db = Alchemical(ALCHEMICAL_DATABASE_URL, engine_options={"pool_pre_ping": True})
db.Model.to_dict = to_dict
engine = db.get_engine()
cache = Cache("~/cache")
# pool = PooledDB(
#     creator=MySQLdb,  # 使用链接数据库的模块
#     maxconnections=5,  # 连接池允许的最大连接数，0和None表示没有限制
#     mincached=5,  # 初始化时，连接池至少创建的空闲的连接，0表示不创建
#     maxcached=5,  # 连接池空闲的最多连接数，0和None表示没有限制
#     maxshared=3,  # 连接池中最多共享的连接数量，0和None表示全部共享
#     blocking=True,  # 链接池中如果没有可用共享连接后，是否阻塞等待，True表示等待，False表示不等待然后报错
#     setsession=[],  # 开始会话前执行的命令列表
#     ping=0,  # ping Mysql 服务端，检查服务是否可用
#     host=MYSQL_HOST,
#     port=MYSQL_PORT,
#     user=MYSQL_USER,
#     password=MYSQL_PASSWORD,
#     database=MYSQL_DB,
#     cursorclass=MySQLdb.cursors.DictCursor,
#     charset="utf8mb4",
# )

pool = PooledDB(
    creator=MySQLdb,
    host=os.getenv("MYSQL_HOST"),
    port=int(os.getenv("MYSQL_TCP_PORT")),
    user=os.getenv("MYSQL_USER"),
    password=os.getenv("MYSQL_PASSWORD"),
    database=os.getenv("MYSQL_DATABASE"),
    cursorclass=MySQLdb.cursors.DictCursor,
    charset="utf8mb4",
)

# persist = PersistentDB(
#     creator=MySQLdb,
#     host=MYSQL_HOST,
#     port=MYSQL_PORT,
#     user=MYSQL_USER,
#     password=MYSQL_PASSWORD,
#     database=MYSQL_DB,
#     cursorclass=MySQLdb.cursors.DictCursor,
#     charset="utf8mb4",
# )

pg_conn = {
    "host": "*************",
    "port": 5432,
    "user": "postgres",
    "password": "mydata",
    "dbname": "mydata_common_db",
    "cursor_factory": psycopg2.extras.RealDictCursor,
}

styles = {
    "root": {
        "display": "flex",
        "flexDirection": "row",
        "alignItems": "center",
    },
    "label": {
        "width": 100,
        "fontSize": 13,
    },
    "input": {
        "width": 180,
    },
}

IMG_TYPE = (
    "tiff",
    "bmp",
    "gif",
    "png",
    "jpeg",
    "jpg",
    "webp",
    "ico",
    "tif",
    "TIFF",
    "BMP",
    "GIF",
    "PNG",
    "JPEG",
    "JPG",
    "WEBP",
    "ICO",
    "TIF",
)
