# -*- coding: utf-8 -*-
import re
from datetime import datetime
from pathlib import Path

import chinese_calendar as cal
import dash_bootstrap_components as dbc
import dash_uploader as du
import feffery_utils_components.alias as fuc
import numpy as np
import pandas as pd
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    ClientsideFunction,
    Input,
    Output,
    Serverside,
    State,
    callback,
    callback_context,
    clientside_callback,
    dash,
    dash_table,
    dcc,
    html,
    no_update,
)
from pony.orm import db_session

from common import get_temp_pn, read_sql
from config import UPLOAD_FOLDER_ROOT, pool
from db.ssp import Dept
from db.ssp_ext import Mag, Mag_stock_out
from tasks import bg_access_record, bg_label_print, bg_mail

dash.register_page(__name__, title="Material", redirect_from=["/apps/material"])

upload_excel = du.Upload(
    id="dash-uploader",
    text="Click to upload Excel file(The column header for the Delta part number must be [DELTAPN], and the column header for the vendor part number must be [MFGPN])",
    filetypes=["xls", "xlsx"],
    default_style={"border-color": "rgba(26, 188, 156)", "min-height": "100%"},
)

search_form = dbc.Row(
    [
        dbc.Col(
            dbc.InputGroup(
                [
                    dbc.Button("Part Number", id="btn-deltapn", color="secondary"),
                    dbc.Input(id="deltapn", size=10),
                    dbc.Tooltip("Click to clear content", target="btn-deltapn"),
                ]
            )
        ),
        dbc.Col(
            dbc.InputGroup(
                [
                    dbc.Button("Part Description", id="btn-des", color="secondary"),
                    dbc.Input(id="des", size=10),
                    dbc.Tooltip("Click to clear content", target="btn-des"),
                ]
            )
        ),
        dbc.Col(
            dbc.InputGroup(
                [
                    dbc.Button("Vendor Name", id="btn-mfgname", color="secondary"),
                    dbc.Input(id="mfgname", size=10),
                    dbc.Tooltip("Click to clear content", target="btn-mfgname"),
                ]
            )
        ),
        dbc.Col(
            dbc.InputGroup(
                [
                    dbc.Button("Vendor Part Number", id="btn-mfgpn", color="secondary"),
                    dbc.Input(id="mfgpn", size=10),
                    dbc.Tooltip("Click to clear content", target="btn-mfgpn"),
                ]
            )
        ),
        dbc.Col(dbc.Button("Search", color="primary", id="search")),
    ],
    className="g-0",
)

search_form1 = dbc.Row(
    [
        dbc.Col(
            dcc.Dropdown(
                id="category-1",
                placeholder="First Category",
                style={"border-color": "#1abc9c"},
                # clearable=False,
                options=[
                    {"label": i, "value": i}
                    for i in ("Active", "Passive", "Magnetic", "ME_Stock")
                ],
            ),
            width=2,
            id="cat-col-1",
        ),
        dbc.Col(
            dcc.Dropdown(
                id="category-2",
                placeholder="Second Category",
                style={"border-color": "#1abc9c"},
                # clearable=False,
            ),
            width=2,
            id="cat-col-2",
            style={"display": "none"},
        ),
        dbc.Col(
            dcc.Dropdown(
                id="category-3",
                placeholder="Third Category",
                style={"border-color": "#1abc9c"},
                # clearable=False,
            ),
            width=2,
            id="cat-col-3",
            style={"display": "none"},
        ),
        dbc.Col(
            dcc.Dropdown(
                id="category-4",
                placeholder="Fourth Category",
                style={"border-color": "#1abc9c"},
                # clearable=False,
            ),
            width=2,
            id="cat-col-4",
            style={"display": "none"},
        ),
        dbc.Col(
            dcc.Dropdown(
                id="category-5",
                placeholder="Fifth Category",
                style={"border-color": "#1abc9c"},
                # clearable=False,
            ),
            width=2,
            id="cat-col-5",
            style={"display": "none"},
        ),
        dbc.Col(
            dbc.Button(
                "Search",
                color="primary",
                id="category-search",
                style={"display": "none"},
            ),
            width=2,
        ),
    ],
    className="g-0",
)

style_header = {
    "backgroundColor": "rgba(52, 73, 94)",
    "color": "white",
    "fontWeight": "bold",
    "fontSize": "12px",
    # 'border': '1px solid',
    "textTransform": "uppercase",
    "font-family": "Helvetica",
    "textAlign": "left",
}


def tab1_content(display: str):
    dept = dbc.InputGroup(
        [
            dbc.InputGroupText("Department"),
            dcc.Dropdown(
                id="dept",
                style={"border-color": "#1abc9c", "width": "150px"},
                clearable=False,
            ),
        ]
    )
    customer = dbc.InputGroup(
        [
            dbc.InputGroupText("Customer"),
            dcc.Dropdown(
                id="customer",
                style={"border-color": "#1abc9c", "width": "150px"},
                value="ALL",
                clearable=False,
            ),
        ]
    )

    search_type = dbc.InputGroup(
        [
            dbc.InputGroupText("Search"),
            dcc.Dropdown(
                id="search-type",
                style={"border-color": "#1abc9c", "width": "130px"},
                options=[
                    {"label": "by Keyword", "value": 1},
                    {"label": "by Category", "value": 2},
                    {"label": "by File", "value": 3},
                ],
                value=1,
                clearable=False,
            ),
        ]
    )
    database_type = dbc.InputGroup(
        [
            dbc.InputGroupText("Data Source"),
            dcc.Dropdown(
                id="database-type",
                style={"border-color": "#1abc9c", "width": "130px"},
                options=[
                    {"label": "ALL", "value": "sap"},
                    {"label": "Stock", "value": "stock"},
                    {"label": "CommonPart", "value": "common", "disabled": True},
                ],
                value="sap",
                clearable=False,
            ),
        ]
    )
    knowledge = dbc.Button(
        "材料知识",
        href=r"https://ideltacn.deltaww.com/desh/desh/SDCHDC/DocLib1/Forms/AllItems.aspx?RootFolder=%2Fdesh%2Fdesh%2FSDCHDC%2FDocLib1%2FTraining%20Materials%2F%E6%9D%90%E6%96%99%E7%9F%A5%E8%AF%86%E7%B1%BB&FolderCTID=0x0120002651ACAA94268C428DD0F230FA04E76D&View=%7BC7BFD93D%2DBDD0%2D4F17%2DB1AE%2D91056F6EBFB9%7D",
        color="success",
        target="_blank",
        outline=True,
    )
    div = html.Div(
        [
            dcc.Store(id="mat_cat"),
            dcc.Store(id="search_result"),
            html.Pre(),
            dbc.Row(
                [
                    dbc.Col(dept, width="auto"),
                    dbc.Col(customer, width="auto"),
                    dbc.Col(search_type, width="auto"),
                    dbc.Col(database_type, width="auto"),
                    dbc.Col(knowledge, width="auto", style={"display": display}),
                ],
            ),
            html.Pre(),
            dbc.Row(
                [dbc.Col([search_form], width=12)],
                id="keyword-form",
            ),
            dbc.Row(
                [dbc.Col([search_form1], width=12)],
                id="category-form",
                style={"display": "none"},
            ),
            dbc.Row(
                [dbc.Col([upload_excel], width=12)],
                id="upload-form",
                style={"display": "none"},
            ),
            html.Pre(),
            dbc.Spinner(
                html.Div(
                    [
                        dash_table.DataTable(
                            columns=[
                                {"name": "id", "id": "id"},
                                {
                                    "name": "action",
                                    "id": "action",
                                    "presentation": "dropdown",
                                },
                                {"name": "block", "id": "block"},
                                {"name": "deltapn", "id": "deltapn"},
                                {"name": "des", "id": "des"},
                                {"name": "mfgname", "id": "mfgname"},
                                {"name": "mfgpn", "id": "mfgpn"},
                                {"name": "sh_qty", "id": "sh_stock"},
                                {"name": "hz_qty", "id": "hz_stock"},
                                {"name": "wh_qty", "id": "wh_stock"},
                                {"name": "remark", "id": "remark"},
                                {"name": "warning", "id": "warning"},
                                {"name": "checkcode", "id": "checkcode"},
                                {"name": "antis", "id": "antis"},
                                {"name": "双85", "id": "double85"},
                                {"name": "ai", "id": "ai"},
                                {"name": "source", "id": "source"},
                                {"name": "common_db", "id": "common_db"},
                                {"name": "block_grade", "id": "block_grade"},
                                {"name": "remark_grade", "id": "remark_grade"},
                                {"name": "lf", "id": "lf"},
                                {"name": "hf", "id": "hf"},
                                # {"name": "", "id": ""},
                            ],
                            editable=True,
                            sort_action="native",
                            filter_action="native",
                            # page_action='none',
                            # fixed_rows={'headers': True},
                            id="query-table",
                            hidden_columns=[
                                "id",
                                "checkcode",
                                "source",
                                "common_db",
                                "block_grade",
                                "remark_grade",
                            ],
                            page_current=0,
                            # fixed_rows={"headers": True},
                            row_selectable="multi",
                            page_size=50,
                            export_format="xlsx",
                            export_headers="display",
                            style_header=style_header,
                            # style_table={"overflowX": "auto"},
                            style_data={
                                "whiteSpace": "normal",
                                "height": "auto",
                                "textAlign": "left",
                                "textOverflow": "ellipsis",
                                "font-family": "Helvetica",
                                "font-size": "10px",
                                "maxWidth": 0,
                            },
                            style_data_conditional=[
                                {
                                    "if": {
                                        "filter_query": "{block_grade} = 1",
                                        "column_id": "block",
                                    },
                                    "backgroundColor": "rgba(231, 76, 60)",
                                },
                                {
                                    "if": {
                                        "filter_query": "{block_grade} = 2",
                                        "column_id": "block",
                                    },
                                    "backgroundColor": "rgba(241, 196, 15)",
                                },
                                {
                                    "if": {
                                        "filter_query": "{block_grade} = 3",
                                        "column_id": "block",
                                    },
                                    "backgroundColor": "rgba(241, 196, 15)",
                                },
                                {
                                    "if": {
                                        "filter_query": "{remark_grade} = 7",
                                        "column_id": "remark",
                                    },
                                    "backgroundColor": "rgba(46, 204, 113)",
                                },
                                {
                                    "if": {
                                        "filter_query": "{remark_grade} = 6",
                                        "column_id": "remark",
                                    },
                                    "backgroundColor": "rgba(46, 204, 113)",
                                },
                                {
                                    "if": {
                                        "filter_query": '{remark} contains "NewPart"',
                                        "column_id": "remark",
                                    },
                                    "backgroundColor": "rgba(231, 76, 60)",
                                    "color": "black",
                                },
                                {
                                    "if": {
                                        "filter_query": '{remark} contains "多笔记录"',
                                        "column_id": "remark",
                                    },
                                    "backgroundColor": "rgba(231, 76, 60)",
                                    "color": "black",
                                },
                                {
                                    "if": {
                                        "filter_query": "{source} = second",
                                        "column_id": [
                                            "deltapn",
                                            "des",
                                            "mfgname",
                                            "mfgpn",
                                        ],
                                    },
                                    "backgroundColor": "rgba(149, 165, 166)",
                                },
                                {
                                    "if": {
                                        "filter_query": '{warning} != ""',
                                        "column_id": "warning",
                                    },
                                    "textDecoration": "underline",
                                    "color": "#3498db",
                                    "font-size": "14px",
                                },
                            ],
                            css=[
                                {
                                    "selector": ".dash-spreadsheet-menu-item",
                                    "rule": "display:none",
                                },
                                {
                                    "selector": ".export",
                                    "rule": "position:absolute;bottom:-30px;border:none;background-color:#34495e;color:white;font-size: 17px;border-radius:4px;",
                                },
                            ],
                        ),
                    ],
                    style={"display": "none"},
                    id="query-result",
                ),
                spinner_style={"width": "3rem", "height": "3rem"},
                fullscreen=True,
            ),
            html.Div(id="notification-container"),
            fuc.FancyNotification(
                id="notification",
                theme="dark",
                position="bottom-center",
                hideProgressBar=True,
                # key=datetime.now().timestamp(),
                closable=False,
                toastClassName="material-toast",
                closeOnClick=False,
            ),
            dbc.Modal(
                [
                    dbc.ModalHeader(
                        id="modal-header",
                        style={
                            "background-color": "#3498db",
                            "height": "30px",
                        },
                    ),
                    dbc.ModalBody(id="modal-body"),
                ],
                id="modal",
                centered=True,
                size="xl",
                scrollable=True,
            ),
            dbc.Modal(
                [
                    dbc.ModalHeader(
                        "Warning:",
                        className="bg-warning text-dark border-0",
                        style={"height": "30px"},
                    ),
                    dbc.ModalBody(id="notice-msg"),
                ],
                id="notice",
                centered=True,
                className="border-warning",
                style={"border-width": "2px"},
            ),
            dbc.Modal(
                [
                    dbc.ModalHeader("Material Parameters:"),
                    dbc.ModalBody(
                        dash_table.DataTable(
                            id="parameter-table",
                            export_format="xlsx",
                            export_headers="display",
                            cell_selectable=False,
                            style_header={
                                "backgroundColor": "rgba(52, 73, 94)",
                                "color": "white",
                                "fontWeight": "bold",
                                "fontSize": "12px",
                            },
                            style_cell={
                                "whiteSpace": "normal",
                                "height": "auto",
                                "textAlign": "left",
                                "font-family": "Helvetica",
                                "font-size": "10px",
                            },
                            style_data_conditional=[
                                {
                                    "if": {"row_index": "odd"},
                                    "backgroundColor": "rgb(248, 248, 248)",
                                }
                            ],
                            css=[
                                {
                                    "selector": ".dash-spreadsheet-menu-item",
                                    "rule": "display:none",
                                }
                            ],
                        ),
                    ),
                    dbc.ModalFooter(
                        [
                            dbc.Alert(
                                "记录已复制到剪贴板",
                                color="success",
                                id="copy-alert",
                                is_open=False,
                                duration=3000,
                            ),
                            dbc.Button(
                                "Copy", id="mat-parameter-copy", color="warning"
                            ),
                            dbc.Button("Close", id="mat-parameter-close"),
                        ],
                    ),
                ],
                id="mat-parameter",
                centered=True,
                size="xl",
                scrollable=True,
            ),
        ]
    )
    return div


tab2_table_columns = [
    {"name": "checkcode", "id": "checkcode"},
    {"name": "台达料号", "id": "deltapn"},
    {"name": "描述", "id": "des"},
    {"name": "厂商", "id": "mfgname"},
    {"name": "厂商料号", "id": "mfgpn"},
    {"name": "仓库", "id": "area", "presentation": "dropdown", "editable": True},
    {"name": "库存数量", "id": "stock"},
    {"name": "领用数量", "id": "领用数量", "presentation": "input", "editable": True},
]
tab2_content = html.Div(
    [
        dcc.Store(id="picking-store"),
        html.Br(),
        dash_table.DataTable(
            data=[],
            columns=tab2_table_columns,
            row_deletable=True,
            hidden_columns=["checkcode"],
            id="picking-table",
            style_header=style_header,
            style_cell={
                "whiteSpace": "normal",
                "height": "auto",
                "textAlign": "left",
                "font-family": "Helvetica",
                "font-size": "10px",
            },
            css=[{"selector": ".dash-spreadsheet-menu-item", "rule": "display:none"}],
        ),
        dbc.Row(
            [
                dbc.Col(
                    dbc.Alert(
                        id="picking-alert",
                        color="success",
                        is_open=False,
                        fade=True,
                        duration=10000,
                    ),
                    width=10,
                ),
                dbc.Col(
                    dbc.Button("提交领料", id="submit-picking", color="primary"),
                    width=1,
                ),
            ],
            justify="end",
            # className="mt-1 mr-1",
        ),
    ]
)

pur_form = dbc.Row(
    [
        dbc.Col(
            dbc.InputGroup(
                [
                    dbc.Button("机种名", id="btn-pur-proj", color="secondary"),
                    dbc.Input(id="pur-proj", size=10),
                    dbc.Tooltip("点击清除内容", target="btn-pur-proj"),
                ]
            ),
        ),
        dbc.Col(
            dbc.InputGroup(
                [
                    dbc.Button("年用量", id="btn-pur-annual-qty", color="secondary"),
                    dbc.Input(id="pur-annual-qty", size=10),
                    dbc.Tooltip("点击清除内容", target="btn-pur-annual-qty"),
                ]
            ),
        ),
        dbc.Col(
            dbc.InputGroup(
                [
                    dbc.Button("申请备注", id="btn-pur-remark", color="secondary"),
                    dbc.Input(id="pur-remark", type="text", size=15),
                    dbc.Tooltip("点击清除内容", target="btn-pur-remark"),
                ]
            ),
        ),
        dbc.Col(
            dbc.InputGroup(
                [
                    dbc.InputGroupText("量产日期"),
                    dcc.DatePickerSingle(id="pur-mp-date", display_format="YYYY-M-D"),
                ],
            )
        ),
        dbc.Col(
            dbc.InputGroup(
                [
                    dbc.InputGroupText("需求日期"),
                    dcc.DatePickerSingle(
                        id="pur-required-date", display_format="YYYY-M-D"
                    ),
                ],
            )
        ),
    ],
)

tab3_content = html.Div(
    [
        dcc.Store(id="pur-store"),
        html.Br(),
        dbc.Row(pur_form, className="mt-1 px-3"),
        html.Pre(),
        dash_table.DataTable(
            data=[],
            columns=[
                {
                    "name": i,
                    "id": i,
                    "presentation": "dropdown"
                    if i in ("主类型", "次类型")
                    else "input",
                }
                for i in (
                    "checkcode",
                    "deltapn",
                    "des",
                    "mfgname",
                    "mfgpn",
                    "主类型",
                    "次类型",
                    "需求数量",
                )
            ],
            # row_selectable='multi',
            editable=True,
            row_deletable=True,
            is_focused=True,
            hidden_columns=["checkcode"],
            id="pur-table",
            style_header=style_header,
            dropdown={
                "主类型": {
                    "options": [
                        {"label": "Active", "value": "Active"},
                        {"label": "Passive", "value": "Passive"},
                        {"label": "Magnetic", "value": "Magnetic"},
                        {"label": "Electro-Mechanical", "value": "Electro-Mechanical"},
                        {"label": "Mechanical", "value": "Mechanical"},
                        {"label": "Other", "value": "Other"},
                    ],
                    "clearable": False,
                },
            },
            style_cell_conditional=[
                #     {'if': {'column_id': 'deltapn'},
                #     'width': '30px'},
                #    {'if': {'column_id': 'des'},
                #     'width': '150px'},
                #     {'if': {'column_id': 'mfgname'},
                #     'width': '50px'},
                #    {'if': {'column_id': 'mfgpn'},
                #     'width': '80px'},
                {"if": {"column_id": "主类型"}, "width": "150px"},
                {"if": {"column_id": "次类型"}, "width": "150px"},
                {"if": {"column_id": "需求数量"}, "width": "80px"},
            ],
            style_cell={
                "whiteSpace": "normal",
                "height": "auto",
                "textAlign": "left",
                "font-family": "Helvetica",
                "font-size": "10px",
            },
            css=[{"selector": ".dash-spreadsheet-menu-item", "rule": "display:none"}],
        ),
        dbc.Row(
            [
                dbc.Col(
                    dbc.Button("AddRow", id="pur-add-row", color="success", size="sm"),
                    width=1,
                ),
                dbc.Col(
                    dbc.Alert(id="pur-alert", is_open=False, fade=True, duration=10000),
                    width=8,
                ),
                dbc.Col(
                    dbc.Button("提交购买", color="primary", id="submit-pur"),
                    width=1,
                ),
            ],
            justify="between",
            # className="mt-1 px-3",
        ),
    ]
)


def layout(user, **kwargs):
    display = "block"
    if user.get("area") in ("DE", "TH", "IN"):
        display = "none"
    return dbc.Container(
        [
            html.Div(id="select-data", style={"display": "none"}),
            dbc.Tabs(
                [
                    dbc.Tab(tab1_content(display), label="Part Query", tab_id="tab-1"),
                    dbc.Tab(
                        tab2_content,
                        label="库存领料",
                        tab_id="tab-2",
                        tab_style={"display": display},
                    ),
                    dbc.Tab(
                        tab3_content,
                        label="申请买料",
                        tab_id="tab-3",
                        id="tab-pur",
                        tab_style={"display": display},
                    ),
                ],
                id="tab-num",
                active_tab="tab-1",
                style={"color": "#16a085"},
            ),
        ],
        fluid=True,
        className="ml-3 pr-5",
    )


# ------------------以下为回调函数部分-----------------
# ----------------视频弹窗控制start-----------------
# @callback(
#     [
#         Output("video-modal", "is_open"),
#         Output("video-modal-store", "data"),
#         Output("video-modal-body", "children"),
#     ],
#     [
#         Input("video-modal-close", "n_clicks"),
#         Input("video-alert", "is_open"),
#     ],
#     [State("video-modal-store", "data"), State("user", "data")],
#     prevent_initial_call=False,
# )
# def video_modal_show(n, alert, data, user):
#     """[点击不显示，关闭弹窗，并保存不显示状态到store,清除浏览数据才可以显示]"""
#     ctx = callback_context
#     id = ctx.triggered[0]["prop_id"].split(".")[0]

#     if id == "video-modal-close":
#         bg_access_record(user, "教程提醒", "以后再看")
#         return False, no_update, no_update

#     elif id == "video-alert":
#         if alert:
#             raise PreventUpdate
#         paths = {i.stem: i.stem for i in Path("./assets/tips/material").glob("*")}
#         return False, paths, no_update
#     else:
#         paths = {
#             i.stem
#             for i in Path("./assets/tips/material").glob("*")
#             if i.stem not in data
#         }
#         if paths:
#             children = dbc.ListGroup(
#                 [
#                     dbc.ListGroupItem(
#                         i,
#                         target="_blank",
#                         id={"type": "video-link", "index": i},
#                         href=f"http://sup.deltaww.com/assets/tips/material/{i}.mp4",
#                     )
#                     for i in paths
#                 ]
#             )
#             return True, no_update, children
#         else:
#             return False, no_update, no_update


# @callback(
#     Output("video-alert", "is_open"),
#     Input("video-modal-not-show", "n_clicks"),
#     [State("user", "data")],
# )
# def video_alert_open(n, user):
#     """点击不弹出，提示再次观看"""
#     bg_access_record(user, "教程提醒", "不需要教程")
#     return True


# @callback(
#     [
#         Output("video-modal-close", "style"),
#         Output("video-modal-not-show", "style"),
#     ],
#     Input("video-modal-not-show", "n_clicks"),
# )
# def video_close_disabled(n):
#     """点击不弹出，禁用关闭按钮"""
#     return {"display": "none"}, {"display": "none"}


# @callback(
#     Output({"type": "video-link", "index": MATCH}, "color"),
#     Input({"type": "video-link", "index": MATCH}, "n_clicks"),
#     [
#         State({"type": "video-link", "index": MATCH}, "children"),
#         State("user", "data"),
#     ],
# )
# def video_link_record(n, children, user):
#     """访问记录点击视频的链接"""
#     bg_access_record(user, "视频教程", children)
#     return "success"


# ----------------视频弹窗控制end-----------------


# ---------start 按材料类型查找-------
@callback(Output("mat_cat", "data"), Input("dept", "value"), memoize=True)
def get_mat_catalogue(dept):
    """[从数据库,获取材料分类表]"""
    sql = (
        "select category_1 as cat1,category_2 as cat2,category_3 as cat3,category_4 as cat4,\
        category_5 as cat5,ce_database_name,ce_keyword_pn as ce_pn,ce_keyword_des as ce_des,\
        ce_keyword_des_exclusive as ce_not_des,ce_keyword_styletp as ce_tp,ce_trigger,\
        Pur_Category_3 as pur_cat3,pur_keyword_pn as pur_pn,pur_keyword_des as pur_des \
            from ssp_ce.a_mat_catalogue"
    )
    df = read_sql(sql)
    df = (
        df.replace("\*", "%", regex=True).replace("%", "").replace("^%", "", regex=True)
    )
    return Serverside(df)


@callback(
    Output("database-type", "value"),
    Output("database-type", "options"),
    Input("search-type", "value"),
    Input("dept", "value"),
)
@db_session
def file_search_database(search, dept_id):
    """[按文件查询时,数据库仅可以选择SAP,非常用料部分,常用料数据库不可选]"""
    ctx = callback_context
    id = ctx.triggered[0]["prop_id"].split(".")[0]

    sql = "select distinct dept from ssp_ce.a_oftenmaterial"
    common_depts = read_sql(sql)

    common_depts = common_depts["dept"].tolist()
    common_depts.remove("LGT")
    d = Dept.get(id=dept_id)
    dept_group = d.dept_group

    if id == "search-type":
        if search == 3:
            options = [
                {"label": "ALL", "value": "sap"},
                {"label": "Stock", "value": "stock", "disabled": True},
                {"label": "CommonPart", "value": "common", "disabled": True},
            ]
            return "sap", options
        else:
            if dept_group in common_depts:
                options = [
                    {"label": "ALL", "value": "sap"},
                    {"label": "Stock", "value": "stock"},
                    {"label": "CommonPart", "value": "common"},
                ]
            else:
                options = [
                    {"label": "ALL", "value": "sap"},
                    {"label": "Stock", "value": "stock"},
                    {"label": "CommonPart", "value": "common", "disabled": True},
                ]
            return no_update, options

    elif id == "dept":
        if dept_group in common_depts:
            options = [
                {"label": "ALL", "value": "sap"},
                {"label": "Stock", "value": "stock"},
                {"label": "CommonPart", "value": "common"},
            ]
        else:
            options = [
                {"label": "ALL", "value": "sap"},
                {"label": "Stock", "value": "stock"},
                {"label": "CommonPart", "value": "common", "disabled": True},
            ]
        return no_update, options


@callback(
    Output("category-1", "value"),
    [
        Input("database-type", "value"),
        Input("search-type", "value"),
    ],
)
def category1_value_reset(v1, v2):
    """重置category1_value"""
    return None


@callback(
    Output("category-2", "value"),
    Input("category-1", "value"),
)
def category2_value_reset(v):
    """重置category2_value"""
    return None


@callback(
    Output("category-3", "value"),
    Input("category-2", "value"),
)
def category3_value_reset(v):
    """重置category3_value"""
    return None


@callback(
    Output("category-4", "value"),
    Input("category-3", "value"),
)
def category4_value_reset(v):
    """重置category4_value"""
    return None


@callback(
    Output("category-5", "value"),
    Input("category-4", "value"),
)
def category5_value_reset(v):
    """重置category5_value"""
    return None


@callback(
    [
        Output("category-1", "options"),
        Output("category-2", "options"),
        Output("category-3", "options"),
        Output("category-4", "options"),
        Output("category-5", "options"),
        Output("cat-col-1", "style"),
        Output("cat-col-2", "style"),
        Output("cat-col-3", "style"),
        Output("cat-col-4", "style"),
        Output("cat-col-5", "style"),
        Output("category-search", "style"),
    ],
    [
        Input("category-1", "value"),
        Input("category-2", "value"),
        Input("category-3", "value"),
        Input("category-4", "value"),
        Input("category-5", "value"),
        Input("database-type", "value"),
        Input("search-type", "value"),
    ],
    [
        State("mat_cat", "data"),
    ],
)
def category_dropdown(v1, v2, v3, v4, v5, database, search, mat_cat):
    """[动态显示材料类型选择下拉菜单]"""
    if search != 2:
        raise PreventUpdate

    ctx = callback_context
    id = ctx.triggered[0]["prop_id"].split(".")[0]
    display = {"border-color": "#1abc9c", "display": "block"}
    hide = {"display": "none"}
    style1, style2, style3, style4, style5 = [hide] * 5
    options1, options2, options3, options4, options5 = [no_update] * 5
    search_style = hide
    cats = [v1, v2, v3, v4, v5]

    if database == "common":
        options1 = [
            "Active",
            "Passive",
            "Electro-Mechanical",
            "Magnetic",
            "Mechanical",
            "Other",
        ]
        df = mat_cat.loc[mat_cat["ce_database_name"].notnull()]
    else:
        options1 = ["Active", "Passive", "Magnetic", "ME_Stock"]
        df = mat_cat.loc[mat_cat["pur_pn"].notnull()]

    options1 = [{"label": i, "value": i} for i in options1]
    style1 = display

    if (id == "search-type") or (id == "database-type"):
        return (
            options1,
            options2,
            options3,
            options4,
            options5,
            style1,
            style2,
            style3,
            style4,
            style5,
            search_style,
        )

    elif id == "category-1":
        cats = [v1, None, None, None, None]
        df = df.query("(cat1==@v1)")
        if df.empty:
            if v1:
                search_style = {"display": "block"}
        else:
            options2 = [
                {"label": i, "value": i}
                for i in df["cat2"].dropna().sort_values().unique()
            ]
            style2 = display

    elif id == "category-2":
        cats = [v1, v2, None, None, None]
        df = df.query("(cat1==@v1)&(cat2==@v2)")
        if database == "common":
            options3 = [
                {"label": i, "value": i}
                for i in df["cat3"].dropna().sort_values().unique()
            ]
            if options3:
                style3 = display
        else:
            options3 = [
                {"label": i, "value": i}
                for i in df["pur_cat3"].dropna().sort_values().unique()
            ]
            if not (df["pur_cat3"] == v2).all():
                style3 = display
        style2 = display

    elif id == "category-3":
        cats = [v1, v2, v3, None, None]
        if database == "common":
            df = df.query("(cat1==@v1)&(cat2==@v2)&(cat3==@v3)")
            options4 = [
                {"label": i, "value": i}
                for i in df["cat4"].dropna().sort_values().unique()
            ]
        else:
            df = df.query("(cat1==@v1)&(cat2==@v2)&(pur_cat3==@v3)")
            options4 = []
        style2 = display
        style3 = display
        if options4:
            style4 = display

    elif id == "category-4":
        cats = [v1, v2, v3, v4, None]
        df = df.query("(cat1==@v1)&(cat2==@v2)&(cat3==@v3)&(cat4==@v4)")
        options5 = [
            {"label": i, "value": i} for i in df["cat5"].dropna().sort_values().unique()
        ]
        style2 = display
        style3 = display
        style4 = display
        if options5:
            style5 = display

    elif id == "category-5":
        cats = [v1, v2, v3, v4, v5]
        df = mat_cat.query(
            "(cat1==@v1)&(cat2==@v2)&(cat3==@v3)&(cat4==@v4)&(cat5==@v5)"
        )
        style2 = display
        style3 = display
        style4 = display
        style5 = display

    df1 = mat_cat.copy()
    if database != "common":
        df1["cat3"] = df1["pur_cat3"]
    cats = [i for i in cats if i]
    for i, j in enumerate(cats):
        df1 = df1.query(f"cat{i + 1}==@j")
    trigger = df1["ce_trigger"].min()
    if len(cats) >= trigger:
        search_style = {"display": "block"}

    # if not df.empty:
    #     idx=df['ce_trigger'].min()
    #     if all(cats[:idx]):
    #         search_style={'display':'block'}

    return (
        options1,
        options2,
        options3,
        options4,
        options5,
        style1,
        style2,
        style3,
        style4,
        style5,
        search_style,
    )


@callback(
    [
        Output("keyword-form", "style"),
        Output("category-form", "style"),
        Output("upload-form", "style"),
    ],
    [Input("search-type", "value")],
)
def search_type(value):
    """[选择查询类型时,动态显示查询条件表单]"""
    if value == 1:
        return {"display": "block"}, {"display": "none"}, {"display": "none"}
    elif value == 2:
        return {"display": "none"}, {"display": "block"}, {"display": "none"}
    elif value == 3:
        return {"display": "none"}, {"display": "none"}, {"display": "block"}
    else:
        raise PreventUpdate


# ---------end 按材料类型查找-------
@callback(
    Output("select-data", "children"),
    [Input("query-table", "selected_rows")],
    [State("query-table", "data")],
)
def select_to_clipboard(rows, data):
    """复选的数据暂存到select-data,客户端回调到剪贴板"""
    if (rows is None) or (data is None):
        raise PreventUpdate

    df = pd.DataFrame(data)[
        [
            "block",
            "deltapn",
            "des",
            "mfgname",
            "mfgpn",
            "remark",
            "antis",
            "double85",
            "ai",
            "lf",
            "hf",
        ]
    ]
    df = df.fillna("")
    df = df.astype(str)
    df = df.iloc[rows]
    data = "\n".join("\t".join(x for x in j) for i, j in df.iterrows())
    return data


clientside_callback(
    ClientsideFunction(namespace="clientside", function_name="clipboard"),
    Output("select-data", "none"),
    [Input("select-data", "children")],
)


@callback(
    Output("tab-pur", "style"),
    [Input("tab-num", "active_tab")],
    [State("user", "data")],
)
@db_session
def display_purchasing(at, user):
    """purchasing_permit=='Y'时不显示申请买料"""
    di = Dept.get(id=user.get("dept_id"))
    if di.purchasing_permit == "Y":
        raise PreventUpdate
    return {"display": "none"}


@callback(
    Output("query-table", "page_current"),
    [
        Input("search", "n_clicks"),
        Input("category-search", "n_clicks"),
        Input("dash-uploader", "isCompleted"),
    ],
)
def page_current(n1, n2, n3):
    """[重新搜索时返回第一页]"""
    return 0


# --------部门客户下拉菜单控制----------
@callback(
    [
        Output("dept", "options"),
        Output("dept", "value"),
    ],
    [
        Input("dept", "id"),
    ],
    [State("user", "data")],
    prevent_initial_call=False,
)
def dept_options(id, user):
    """[部门选择下拉菜单]"""
    sql = "select id,dept_group,dept_name from ssp.dept"
    df = read_sql(sql)
    # df['dept']=np.where(df['dept_group']==df['dept_name'],df['dept_group'],
    #                     df['dept_group']+'_'+df['dept_name'])

    df["dept"] = df["dept_group"] + "_" + df["dept_name"]
    df = df.sort_values(by="dept")
    dept_id = user.get("dept_id")

    if dept_id != 10:  # ? 如果不是SUP部门
        dept_group = df.loc[df["id"] == dept_id, "dept_group"].iloc[0]
        df = df.loc[df["dept_group"] == dept_group]

    options = [{"label": i.dept, "value": i.id} for i in df.itertuples()]
    return options, dept_id


@callback(Output("customer", "options"), Input("dept", "value"))
def customer_options(dept_id):
    """[根据部门显示客户]"""
    sql = "select distinct con_cust as customer \
        from ssp_ce.a_blockrule where dept_id=%s and con_cust!=%s"
    df2 = read_sql(sql, params=[dept_id, "ALL"])
    cust = ["ALL"] + df2["customer"].dropna().str.upper().unique().tolist()
    options = [{"label": i, "value": i} for i in cust]
    return options


# ---------------------input自适应长度等--------------------------

for i in ["deltapn", "des", "mfgname", "mfgpn", "pur-proj", "pur-remark"]:
    clientside_callback(
        ClientsideFunction(namespace="clientside", function_name="flex_size"),
        Output(f"{i}", "size"),
        [Input(f"{i}", "value")],
    )

for i in [
    "deltapn",
    "des",
    "mfgname",
    "mfgpn",
    "pur-proj",
    "pur-remark",
    "pur-annual-qty",
]:
    clientside_callback(
        ClientsideFunction(namespace="clientside", function_name="btn_color"),
        Output(f"btn-{i}", "color"),
        [Input(f"{i}", "value")],
    )

for i in [
    "deltapn",
    "des",
    "mfgname",
    "mfgpn",
    "pur-proj",
    "pur-remark",
    "pur-annual-qty",
]:

    @callback(
        Output(f"{i}", "value"),
        [Input(f"btn-{i}", "n_clicks")],
        [State(f"{i}", "value")],
    )
    def clear_input(n, value):
        if n:
            return ""
        return value


@callback(
    Output("query-table", "hidden_columns"),
    Output("query-table", "style_cell_conditional"),
    Input("dept", "value"),
    State("user", "data"),
)
def hide_tab1_table_qty_column(_, user):
    """不同部门数量栏位显示不同"""
    area = user.get("area")
    dept_id = user.get("dept_id")
    cols = [
        "id",
        "checkcode",
        "source",
        "common_db",
        "block_grade",
        "remark_grade",
    ]
    style_cell_conditional = [
        {"if": {"column_id": "action"}, "width": "6%"},
        {"if": {"column_id": "deltapn"}, "word-wrap": "break-word", "width": "8%"},
        {"if": {"column_id": "des"}, "word-wrap": "break-word", "width": "24%"},
        {"if": {"column_id": "mfgname"}, "word-wrap": "break-word", "width": "6%"},
        {"if": {"column_id": "mfgpn"}, "word-wrap": "break-word", "width": "15%"},
        {"if": {"column_id": "sh_stock"}, "width": "4.5%"},
        {"if": {"column_id": "hz_stock"}, "width": "4.5%"},
        {"if": {"column_id": "wh_stock"}, "width": "4.5%"},
        {"if": {"column_id": "remark"}, "width": "7%", "overflow": "hidden"},
        {"if": {"column_id": "block"}, "width": "7%", "overflow": "hidden"},
        {"if": {"column_id": "warning"}, "width": "7%", "overflow": "hidden"},
        {"if": {"column_id": "antis"}, "width": "4%", "textAlign": "center"},
        {"if": {"column_id": "double85"}, "width": "3%", "textAlign": "center"},
        {"if": {"column_id": "ai"}, "width": "5%"},
    ]

    # *NBE、IDC、UPS、TPS、PQC、SAFETY、MSBU、PMSBD、HPRT、DPEC、ESPBD、EISBG_PCSBD、EISBG_WPBU
    if dept_id in (3, 30, 7, 19, 20, 14, 9, 8, 16, 31, 11, 12, 32, 13, 15):
        style_cell_conditional = [
            {"if": {"column_id": "action"}, "width": "8%"},
            {"if": {"column_id": "deltapn"}, "word-wrap": "break-word", "width": "10%"},
            {"if": {"column_id": "des"}, "word-wrap": "break-word", "width": "30%"},
            {"if": {"column_id": "mfgname"}, "word-wrap": "break-word", "width": "10%"},
            {"if": {"column_id": "mfgpn"}, "word-wrap": "break-word", "width": "20%"},
            {"if": {"column_id": "sh_stock"}, "width": "5%"},
            {"if": {"column_id": "hz_stock"}, "width": "5%"},
            {"if": {"column_id": "wh_stock"}, "width": "5%"},
        ]
        cols.extend(["antis", "double85", "ai", "lf", "hf", "block", "warning"])

    if area == "SH":
        if dept_id == 1:
            cols.append("hz_stock")
        else:
            cols.extend(["hz_stock", "wh_stock"])
    elif area == "HZ":
        if dept_id != 21:
            cols.append("wh_stock")
    elif area == "WH":
        if dept_id == 1:
            cols.append("hz_stock")

    return cols, style_cell_conditional


# ---------------------材料查询--------------------------
@callback(
    [
        Output("notice", "is_open"),
        Output("notice-msg", "children"),
        Output("query-table", "data"),
        Output("query-result", "style"),
        Output("query-table", "dropdown_data"),
    ],
    [
        Input("search", "n_clicks"),
        Input("query-table", "data_timestamp"),
        Input("category-search", "n_clicks"),
        Input("dash-uploader", "isCompleted"),
    ],
    [
        State("query-table", "data"),
        State("deltapn", "value"),
        State("des", "value"),
        State("mfgname", "value"),
        State("mfgpn", "value"),
        State("dept", "value"),
        State("customer", "value"),
        State("query-table", "data_previous"),
        State("query-table", "columns"),
        State("dash-uploader", "fileNames"),
        State("dash-uploader", "upload_id"),
        State("user", "data"),
        State("category-1", "value"),
        State("category-2", "value"),
        State("category-3", "value"),
        State("category-4", "value"),
        State("category-5", "value"),
        State("category-1", "options"),
        State("category-2", "options"),
        State("category-3", "options"),
        State("category-4", "options"),
        State("category-5", "options"),
        State("database-type", "value"),
        State("mat_cat", "data"),
    ],
)
@db_session
def material_search(
    n1,
    n2,
    n3,
    completed,
    data,
    deltapn,
    des,
    mfgname,
    mfgpn,
    dept_id,
    customer,
    data_previous,
    columns,
    fileNames,
    upload_id,
    user,
    c1,
    c2,
    c3,
    c4,
    c5,
    o1,
    o2,
    o3,
    o4,
    o5,
    database,
    mat_cat,
):
    """材料查询,文件查询,关键字查询,替代料查询三部分"""
    ctx = callback_context
    id = ctx.triggered[0]["prop_id"].split(".")[0]
    not_found = [
        html.P("No matching data found. Please change the conditions and try again."),
        html.P(
            "Tip: To search for multiple keywords, separate them with an asterisk (*)"
        ),
    ]
    user_area = user.get("area")

    if id == "dash-uploader":
        if not completed:
            raise PreventUpdate
        bg_access_record(user, "文件查询", "上传", fileNames[0])
        xlsfile = Path(UPLOAD_FOLDER_ROOT) / upload_id / fileNames[0]
        df = pd.read_excel(xlsfile, dtype=str, keep_default_na=False)
        df.columns = df.columns.str.lower().str.replace("\s+", "", regex=True)
        if ("deltapn" not in df.columns) or ("mfgpn" not in df.columns):
            msg = "格式要求:1.第一个Sheet,2.料号列标题【DELTAPN】,3.厂商料号列标题【MFGPN】,请修改后,重新上传"
            return True, msg, no_update, {"display": "none"}, no_update
        df = df[["deltapn", "mfgpn"]]
        df["deltapn"] = df["deltapn"].str.strip()
        df["mfgpn"] = df["mfgpn"].str.strip()
        df = df.fillna("")
        params = df["deltapn"].unique().tolist()
        params = [i for i in params if i]
        ph = ",".join(["%s"] * len(params))
        cond = f"deltapn in ({ph})"

        df3 = mat_info(dept_id, customer, cond, params)

        deltapn_counts = df3["deltapn"].value_counts().rename("deltapn_counts")
        df3 = df3.rename(columns={"mfgpn": "mfgpn_new"})
        df3 = df3.drop_duplicates("deltapn")
        df = df.merge(df3, on="deltapn", how="left").merge(
            deltapn_counts, how="left", left_on="deltapn", right_index=True
        )
        df["mfgpn"] = np.where(df["mfgpn_new"].isna(), df["mfgpn"], df["mfgpn_new"])
        c1 = df["deltapn_counts"] > 1
        df["des"] = np.where(c1, "", df["des"])
        df["mfgname"] = np.where(c1, "", df["mfgname"])
        df["mfgpn"] = np.where(c1, "", df["mfgpn"])
        df["remark"] = np.where(c1, "多笔记录", df["remark"])

        df2 = df.loc[df["mfgpn_new"].isnull()]
        if not df2.empty:
            params = df2["mfgpn"].unique().tolist()
            params = [i for i in params if i]
            if params:
                ph = ",".join(["%s"] * len(params))
                cond = f"mfgpn in ({ph})"
                df4 = mat_info(dept_id, customer, cond, params)
                mfgpn_counts = df4["mfgpn"].value_counts().rename("mfgpn_counts")
                df4 = df4.drop_duplicates("mfgpn")
                df2 = df2[["mfgpn"]].reset_index().merge(df4, on="mfgpn", how="left")
                df2 = df2.drop_duplicates("index")
                df2 = df2.set_index("index")
                df.update(df2)
                df = df.merge(
                    mfgpn_counts, how="left", left_on="mfgpn", right_index=True
                )
                c2 = df["mfgpn_counts"] > 1
                df["deltapn"] = np.where(c2, "", df["deltapn"])
                df["des"] = np.where(c2, "", df["des"])
                df["mfgname"] = np.where(c2, "", df["mfgname"])
                c3 = df["deltapn_counts"].isna()
                df["remark"] = np.where(c2 & c3, "多笔记录", df["remark"])

        df["remark"] = np.where(df["checkcode"].isna(), "NewPart", df["remark"])
        df = mat_stock(df, dept_id, user_area)
        df["id"] = range(df.shape[0])
        dropdown_data = action_dropdown_data(user, dept_id, df)
        return (
            no_update,
            no_update,
            df.to_dict(orient="records"),
            {"display": "block"},
            dropdown_data,
        )

    elif id == "search":
        cond = []
        params = []
        dfx = pd.DataFrame()
        if deltapn:
            deltapns1 = re.split(r"\s+", str(deltapn))
            if len(deltapns1) > 1:
                deltapns1 = [i for i in deltapns1 if i]
                dfx = pd.DataFrame(deltapns1, columns=["deltapn"])
                params = dfx["deltapn"].unique().tolist()
                ph = ",".join(["%s"] * len(params))
                cond = [f"deltapn in ({ph})"]
            else:
                deltapn1 = re.split(r"\*", str(deltapn))
                deltapn1 = [f"%{i}%" for i in deltapn1]
                deltapn1[0] = deltapn1[0][1:]
                p1 = " and ".join("deltapn like %s" for i, j in enumerate(deltapn1))
                cond.append(p1)
                params.extend(deltapn1)
        if des:
            param = re.split(r"\*", str(des))
            param = [f"%{i}%" for i in param if i]
            p2 = " and ".join("des like %s" for _ in param)
            cond.append(p2)
            params.extend(param)

        if mfgname:
            mfgname = re.split(r"\*", str(mfgname))
            mfgname = [f"%{i}%" for i in mfgname]
            p3 = " and ".join("mfgname like %s" for i, j in enumerate(mfgname))
            cond.append(p3)
            params.extend(mfgname)

        if mfgpn:
            mfgpn = re.split(r"\*", str(mfgpn))
            mfgpn = [f"%{i}%" for i in mfgpn]
            mfgpn[0] = mfgpn[0][1:]
            p4 = " and ".join("mfgpn like %s" for i, j in enumerate(mfgpn))
            cond.append(p4)
            params.extend(mfgpn)

        if not cond:
            raise PreventUpdate

        bg_access_record(
            user,
            "关键字查询",
            "搜索",
            f"{database}|{deltapn}|{des}|{mfgname}|{mfgpn}"[:50],
        )
        cond = " and ".join(cond)

        if database == "stock":
            cond, params = query_stock_by_keywords(cond, params)
        elif database == "common":
            cond, params = query_common_by_keywords(dept_id, cond, params)
        else:
            cond = cond + " order by qty desc limit 500"

        if not cond:
            return True, not_found, no_update, {"display": "none"}, no_update
        df = mat_info(dept_id, customer, cond, params)

        if df.empty:
            return True, not_found, no_update, {"display": "none"}, no_update

        df = mat_stock(df, dept_id, user_area)

        if dfx.empty:  # ? 单料号查询时
            area = user.get("area").lower()
            by = ["remark_grade", f"{area}_stock"]
            df = df.sort_values(by=by, ascending=False)

            if database == "stock":  # ? 选库存时,仅显示库存>0
                dept_group = Dept.get(id=dept_id).dept_group
                if dept_group in ("DES", "APE", "SPA", "RTP"):
                    df = df.query("(sh_stock>0)|(hz_stock>0)")
                else:
                    df = df.query(f"{area}_stock>0")

                df = df.drop_duplicates("checkcode")
                if df.empty:
                    return True, not_found, no_update, {"display": "none"}, no_update

        else:  # ? 多料号查询时
            df = dfx.merge(df, on="deltapn", how="left")

        df["id"] = range(df.shape[0])
        dropdown_data = action_dropdown_data(user, dept_id, df)
        return (
            no_update,
            no_update,
            df.to_dict(orient="records"),
            {"display": "block"},
            dropdown_data,
        )

    elif id == "category-search":
        x = [o1 and c1, o2 and c2, o3 and c3, o4 and c4, o5 and c5]
        x = [i for i in x if i]
        bg_access_record(user, "类别查询", "搜索", f"{database}|{x}")
        if database == "common":
            dfm = mat_cat.loc[mat_cat["ce_database_name"].notnull()]
        else:
            dfm = mat_cat.loc[mat_cat["pur_pn"].notnull()]
            dfm["cat3"] = dfm["pur_cat3"]
        for i, j in enumerate(x):
            dfm = dfm.query(f"cat{i + 1}==@j")

        commons = pd.DataFrame(columns=["deltapn", "common_db"])

        if database == "stock":
            if c1 == "ME_Stock":
                cond = "deltapn like %s or deltapn like %s or deltapn like %s or deltapn like %s"
                params = ["3%", "N3%", "DC%", "H%"]
            else:
                cond = []
                params = []
                for i in dfm[["pur_pn", "pur_des"]].drop_duplicates().itertuples():
                    cond1 = []
                    params1 = []
                    if i.pur_pn:
                        cond1.append("deltapn like %s")
                        params1.append(i.pur_pn)
                    if i.pur_des:
                        cond1.append("des like %s")
                        params1.append(i.pur_des)
                    cond1 = " and ".join(cond1)
                    if cond1:
                        cond.append("(" + cond1 + ")")
                        params.extend(params1)
                cond = " or ".join(cond)
            cond, params = query_stock_by_keywords(cond, params)

        elif database == "common":
            dbs = dfm["ce_database_name"].unique()
            for db in dbs:
                df1 = dfm.query("ce_database_name==@db")
                params = []
                cond = []
                for i in df1[["ce_pn", "ce_des", "ce_not_des", "ce_tp"]].itertuples():
                    cond1 = []
                    if i.ce_pn:
                        cond1.append("Delta_PN like %s")
                        params.append(i.ce_pn)
                    if i.ce_des:
                        cond1.append("Description like %s")
                        params.append(i.ce_des)
                    if i.ce_not_des:
                        cond1.append("Description not like %s")
                        params.append(i.ce_des)
                    # if i.ce_tp:
                    #     cond1.append('CE_KeyWord_StyleTP like %s')
                    #     params.append('%'+i.ce_tp+'%')
                    if cond1:
                        cond1 = " and ".join(cond1)
                        cond.append("(" + cond1 + ")")
                cond = " or ".join(cond)
                if cond:
                    sql = f"select Delta_PN as deltapn from ssp_ce.{db} where {cond}"
                else:
                    sql = f"select Delta_PN as deltapn from ssp_ce.{db}"

                dfx = read_sql(sql, params=params)
                dfx["common_db"] = db
                commons = commons.append(dfx)

            if commons.empty:
                cond = ""
                params = []
            else:
                params = commons["deltapn"].tolist()
                ph = ",".join(["%s"] * len(params))
                cond = f"deltapn in ({ph})"
                cond, params = query_common_by_keywords(dept_id, cond, params)
        else:
            cond = []
            params = []
            for i in dfm[["pur_pn", "pur_des"]].drop_duplicates().itertuples():
                cond1 = []
                params1 = []
                if i.pur_pn:
                    cond1.append("deltapn like %s")
                    params1.append(i.pur_pn)
                if i.pur_des:
                    cond1.append("des like %s")
                    params1.append(i.pur_des)
                cond1 = " and ".join(cond1)
                if cond1:
                    cond.append("(" + cond1 + ")")
                    params.extend(params1)
            cond = " or ".join(cond)
            cond = cond + " order by qty desc limit 500"

        if (not cond) or (cond == " order by qty desc limit 500"):
            return True, not_found, no_update, {"display": "none"}, no_update

        df = mat_info(dept_id, customer, cond, params)
        if df.empty:
            return True, not_found, no_update, {"display": "none"}, no_update

        commons = commons.drop_duplicates(["deltapn"])
        df = df.merge(commons, on="deltapn", how="left")

        df = mat_stock(df, dept_id, user_area)
        area = user.get("area").lower()
        if area == "sh":
            by = ["common_db", "sh_stock"]
        else:
            by = ["common_db", "hz_stock"]

        if database == "stock":  # 选库存时,仅显示库存>0
            df = df.query(f"{area}_stock>0")
            df = df.drop_duplicates("checkcode")
            if df.empty:
                return True, not_found, no_update, {"display": "none"}, no_update

        df = df.sort_values(by=by, ascending=False)
        df["id"] = range(df.shape[0])
        dropdown_data = action_dropdown_data(user, dept_id, df, database)
        return (
            no_update,
            no_update,
            df.to_dict(orient="records"),
            {"display": "block"},
            dropdown_data,
        )

    elif id == "query-table":
        columns = [i["name"] for i in columns]
        df = pd.DataFrame(data, columns=columns)
        dfp = pd.DataFrame(data_previous, columns=columns)

        c1 = df["action"].fillna("") != dfp["action"].fillna("")
        c2 = df["action"] == "替代料"
        df1 = df.loc[c1 & c2]

        if df1.empty:
            raise PreventUpdate
        bg_access_record(user, "替代料查询", "替代料", f"{df1['deltapn']}")
        df1 = add_second_source(df1, dept_id, customer, user_area)
        if df1.empty:
            return (
                True,
                "No alternative material found.",
                no_update,
                no_update,
                no_update,
            )
        # df = df.append(df1)
        df = pd.concat([df, df1])
        df = df.sort_values(["id", "source"])
        df = df.drop_duplicates(["id", "deltapn"])
        dropdown_data = action_dropdown_data(user, dept_id, df, database)
        df["id"] = range(df.shape[0])  # ? 重新索引id,用于active_cell定位
        return (
            no_update,
            no_update,
            df.to_dict(orient="records"),
            {"display": "block"},
            dropdown_data,
        )
    else:
        raise PreventUpdate


@callback(
    Output("search_result", "data"),
    [
        Input("query-result", "style"),
    ],
    [State("query-table", "data")],
)
def cache_table_data(n1, data):
    """[暂存table数据，用于过滤器筛选]"""
    df = pd.DataFrame(data)
    return Serverside(df)


@callback(
    Output("query-table", "filter_query"),
    [
        Input("search", "n_clicks"),
        Input("category-search", "n_clicks"),
    ],
)
def clear_filter_query(filter, df):
    """[重新搜索时，清空过滤器]"""
    return ""


@callback(
    [
        Output("mat-parameter", "is_open"),
        Output("parameter-table", "data"),
        Output("parameter-table", "columns"),
    ],
    [
        Input("query-table", "data_timestamp"),
        Input("mat-parameter-close", "n_clicks"),
    ],
    [
        State("query-table", "data"),
        State("query-table", "data_previous"),
        State("query-table", "columns"),
    ],
)
def display_parameter(t, n, data, data_previous, columns):
    """action选择参数时显示参数"""
    ctx = callback_context
    id = ctx.triggered[0]["prop_id"].split(".")[0]
    if id == "mat-parameter-close":
        return False, no_update, no_update

    columns = [i["name"] for i in columns]
    df = pd.DataFrame(data, columns=columns)
    dfp = pd.DataFrame(data_previous, columns=columns)
    c1 = df["action"].fillna("") != dfp["action"].fillna("")
    c2 = df["action"] == "参数"
    df1 = df.loc[c1 & c2]
    if df1.empty:
        raise PreventUpdate
    df2 = df.loc[df["action"].isin(["替代料", "参数"])]
    df2["common_db"] = df2["common_db"].fillna(method="ffill")

    db = df2["common_db"].iloc[0]
    params = df2["deltapn"].tolist()
    ph = ",".join(["%s"] * len(params))
    sql = f"select * from ssp_ce.{db} where delta_pn in ({ph})"
    df3 = read_sql(sql, params=params)

    df3 = df3.drop(["ID"], axis=1)
    data = df3.to_dict("records")
    columns = [{"name": i, "id": i} for i in df3.columns]
    return True, data, columns


@callback(
    [Output("select-data", "children"), Output("copy-alert", "is_open")],
    [
        Input("mat-parameter-copy", "n_clicks"),
    ],
    [
        State("parameter-table", "data"),
    ],
)
def copy_parameter(t, data):
    """复选的数据暂存到select-data"""
    df = pd.DataFrame(data)
    df = df.fillna("")
    df = df.astype(str)
    # df=df.iloc[rows]
    data = "\n".join("\t".join(x for x in j) for i, j in df.iterrows())
    return data, True


@callback(
    Output("select-data", "children"),
    [
        Input("query-table", "selected_cells"),
    ],
    [
        State("query-table", "data"),
    ],
)
def selected_cells_to_clipboard(ac, data):
    """表格选中的数据复制到剪贴板"""
    if (ac is None) or (data is None):
        raise PreventUpdate
    df = pd.DataFrame(data)
    clip_data = []
    rows = sorted(set(i["row_id"] for i in ac))
    for r in rows:
        aci = [i for i in ac if i["row_id"] == r]
        d = "\t".join(str(df.loc[i["row_id"], i["column_id"]]) for i in aci)
        clip_data.append(d)
    clip_data = "\n".join(clip_data)
    return clip_data


@callback(
    Output("notification", "children"),
    Output("notification", "visible"),
    Output("notification", "key"),
    Input("query-table", "active_cell"),
    State("query-table", "data"),
)
def open_notification(ac, data):
    """点击备注信息时,通过notification显示"""
    if not ac:
        raise PreventUpdate
    if ac["column_id"] not in ("remark", "block"):
        raise PreventUpdate

    df = pd.DataFrame(data)
    message = df.loc[ac["row_id"], ac["column_id"]]

    if not message:
        raise PreventUpdate

    return message, True, datetime.now().timestamp()


@callback(
    [
        Output("modal", "is_open"),
        Output("modal-header", "children"),
        Output("modal-body", "children"),
    ],
    [
        Input("query-table", "active_cell"),
    ],
    [
        State("query-table", "data"),
        State("dept", "value"),
        State("customer", "value"),
        State("user", "data"),
    ],
)
def ce_block_info(ac, data, dept_id, customer, user):
    """包含“QualityIssue"或“LimitUse"或”CE_Alert“时，弹窗显示禁用信息"""
    if not ac:
        raise PreventUpdate
    if ac["column_id"] not in ("remark", "block", "warning"):
        raise PreventUpdate

    df = pd.DataFrame(data)
    value = df.loc[ac["row_id"], ac["column_id"]]

    keyword = (
        "QualityIssue",
        "LimitUse",
        "CE_Alert",
        "HighRisk",
        "MediumRisk",
        "LowRisk",
    )

    if not any(map(lambda x: x in value, keyword)):
        raise PreventUpdate

    deltapn = df.loc[ac["row_id"], "deltapn"]
    sql = (
        "SELECT dept,type,vendor,model,reason,block_source,block_proj,block_type,dept_id, \
        con_cust FROM ssp_ce.a_blockrule WHERE (con_pn=%s or result2 in\
        (SELECT DISTINCT Result2 FROM ssp_br.z_basicdata_blocklist_filter \
        where DeltaPN=%s and Result2>%s)) and csgreview=%s"
    )

    params = [deltapn, deltapn, 0, "csg"]
    df1 = read_sql(sql, params=params)

    sql = "SELECT id FROM ssp.dept where dept_group=(SELECT dept_group FROM dept where id=%s)"
    params = [dept_id]
    df2 = read_sql(sql, params=params)

    df1["dept"] = df1["dept"].str.upper()
    df1["con_cust"] = df1["con_cust"].str.upper()

    if ac["column_id"] != "warning":
        c1 = df1["dept_id"].isin(df2["id"])
        c2 = df1["con_cust"] == customer
        c3 = df1["dept"] == "ALL"
        c4 = df1["con_cust"] == "ALL"
        df1 = df1.loc[(c1 & c2) | (c1 & c4) | c3]

    df1 = df1.sort_values(by=["block_type"], ascending=False)

    sql = "select deltapn from ssp_br.z_basicdata_nrpeol where deltapn=%s and status in (%s,%s)"
    params = [deltapn, "eol", "nrp"]
    eol = read_sql(sql, params=params)

    if not eol.empty:
        data = [["ALL", "EOL", None, None, "即将EOL", "厂商", "ALL", "EOL", None, None]]
        dfeol = pd.DataFrame(data, columns=df1.columns)
        df1 = pd.concat([dfeol, df1])

    accordion = [
        make_accordion_item(df1, i, j) for i, j in enumerate(df1["reason"].unique())
    ]
    modal_body = dbc.Container(accordion)
    h = df.loc[ac["row_id"], ["deltapn", "des", "mfgname", "mfgpn"]]
    modal_header = dbc.ModalTitle(
        html.H5(html.Pre((" " * 5).join(h), style={"color": "white"})),
        class_name="pt-3",
    )
    bg_access_record(user, "材料", "查看禁用信息")
    return True, modal_header, modal_body


# -------------------领料--------------------
@callback(
    [
        Output("picking-store", "data"),
        Output("pur-store", "data"),
    ],
    [Input("query-table", "data_timestamp")],
    [
        State("query-table", "data"),
        State("picking-store", "data"),
        State("pur-store", "data"),
        State("pur-table", "columns"),
    ],
)
def query_table_select(n, data, picking_store, pur_store, pur_columns):
    """[材料查询，选择领料的记录暂存到store]"""
    picking_store = picking_store or [{}]
    pur_store = pur_store or [{}]
    df = pd.DataFrame(data)
    if "action" not in df.columns:
        raise PreventUpdate
    deltapn = [i.get("deltapn") for i in picking_store]
    c1 = df["action"] == "领料"
    c2 = ~df["deltapn"].isin(deltapn)
    df1 = df.loc[c1 & c2]
    if df1.empty:
        picking_store = no_update
    else:
        picking_store.extend(df1.to_dict(orient="records"))
    deltapn = [i.get("deltapn") for i in pur_store]
    c1 = df["action"] == "买料"
    c2 = ~df["deltapn"].isin(deltapn)
    df2 = df.loc[c1 & c2]
    if df2.empty:
        pur_store = no_update
    else:
        df2 = df2.reindex(columns=[i["name"] for i in pur_columns])
        df2["mfgpn"] = np.where(df2["mfgpn"] == "", "NA", df2["mfgpn"])
        df2, _ = mat_category(df2)
        pur_store.extend(df2.to_dict(orient="records"))
    return picking_store, pur_store


@callback(
    [
        Output("picking-table", "data"),
        Output("picking-table", "dropdown"),
        Output("picking-alert", "children"),
        Output("picking-alert", "is_open"),
        Output("picking-alert", "color"),
    ],
    [
        Input("picking-store", "data"),
        Input("picking-table", "data_timestamp"),
        Input("submit-picking", "n_clicks"),
    ],
    [
        State("user", "data"),
        State("picking-table", "data"),
        State("picking-table", "data_previous"),
    ],
)
@db_session
def submit_picking(store, t, n, user, data, pre_data):
    """[领料store有更新时，或领料表有修改，显示到领料表]
    一天领一次2开头限定20颗
    """
    ctx = callback_context
    id = ctx.triggered[0]["prop_id"].split(".")[0]
    area = user.get("area")
    dept = user.get("dept")
    owner = user.get("nt_name")
    dept_id = user.get("dept_id")

    if area == "HZ":
        options = [
            {"label": "杭州仓", "value": "HZ"},
            {"label": "上海仓", "value": "SH"},
        ]
        if dept_id == 21:
            options.append({"label": "武汉仓", "value": "WH"})

    elif area == "WH":
        options = [
            {"label": "武汉仓", "value": "WH"},
            {"label": "上海仓", "value": "SH"},
        ]
        if dept_id == 21:
            options.append({"label": "杭州仓", "value": "HZ"})

    elif area == "SH":
        options = [{"label": "上海仓", "value": "SH"}]
        if dept_id == 1:
            options.append({"label": "武汉仓", "value": "WH"})
    elif area == "CQ":
        options = [{"label": "上海仓", "value": "SH"}]
    elif area == "NJ":
        options = [{"label": "上海仓", "value": "SH"}]
    else:
        options = [{"label": area, "value": area}]
    dropdown = {"area": {"options": options, "clearable": False}}

    if id == "picking-store":
        if area in ("CQ", "NJ"):
            area = "SH"
        df = pd.DataFrame(store)
        df = df.dropna(how="all")
        df = df.reindex(
            columns=[
                "checkcode",
                "deltapn",
                "des",
                "mfgname",
                "mfgpn",
                "area",
                f"{area.lower()}_stock",
                "领用数量",
            ]
        )
        df["area"] = area
        df = df.rename(columns={f"{area.lower()}_stock": "stock"})
        df = df.drop_duplicates("checkcode")
        data = df.to_dict(orient="records")
        return data, dropdown, no_update, no_update, no_update

    elif id == "picking-table":
        df = pd.DataFrame(data)
        dfp = pd.DataFrame(pre_data)
        if df.empty:
            raise PreventUpdate
        dfp = dfp.loc[df.index]
        df1 = df.loc[df["area"] != dfp["area"]]
        if df1.empty:
            raise PreventUpdate

        params = df["checkcode"].tolist()
        ph = ",".join(["%s"] * len(params))
        dept_group = dept.split("_")[0]
        sql = f"select checkcode,area,qty as stock_qty from ssp.stock where checkcode in ({ph}) and (limituse=%s or limituse like %s)"
        params = params + ["all", "%" + dept_group + "%"]
        stock = read_sql(sql, params=params)

        df = df.merge(stock, on=["checkcode", "area"], how="left")
        df["stock"] = df["stock_qty"]

        # * 检查磁组库存
        mag = Mag.select(lambda x: x.deltapn in df["checkcode"])[:]
        mag = pd.DataFrame(i.to_dict() for i in mag)
        mag = mag.reindex(columns=["deltapn", "qty"])
        mag = mag.drop_duplicates("deltapn")
        df = df.merge(mag, on="deltapn", how="left")
        c1 = df["qty"] > 0
        c2 = df["area"] == "SH"
        df["stock"] = np.where(c1 & c2, df["qty"], df["stock"])

        df = df.reindex(
            columns=[
                "checkcode",
                "deltapn",
                "des",
                "mfgname",
                "mfgpn",
                "area",
                "stock",
                "领用数量",
            ]
        )
        data = df.to_dict(orient="records")
        return data, dropdown, no_update, no_update, no_update

    elif id == "submit-picking":
        df = pd.DataFrame(data)
        if df.empty:
            children = "请先通过材料查询，添加需领用材料"
            return no_update, no_update, children, True, "danger"
        df.columns = df.columns.str.lower()
        area = user.get("area")
        dept = user.get("dept")
        owner = user.get("nt_name")
        df["picking_qty"] = pd.to_numeric(df["领用数量"], errors="coerce")
        df["stock"] = pd.to_numeric(df["stock"], errors="coerce")
        df["stock"] = df["stock"].fillna(0)

        c1 = (df["deltapn"].str.startswith("31")) & (df["picking_qty"] >= 500)
        c2 = (~df["deltapn"].str.startswith("31")) & (df["picking_qty"] >= 100)
        c3 = (df["deltapn"].str.startswith("2")) & (df["picking_qty"] >= 20)
        c4 = (df["deltapn"].str.startswith("25")) & (df["picking_qty"] > 10)
        c5 = (df["deltapn"].str.startswith("26")) & (df["picking_qty"] > 10)
        c6 = (df["deltapn"].str.startswith("11")) & (df["picking_qty"] > 30)
        c7 = (df["deltapn"].str.startswith("14")) & (df["picking_qty"] > 30)
        c8 = (df["deltapn"].str.startswith("16")) & (df["picking_qty"] > 50)
        c9 = (df["deltapn"].str.startswith("17")) & (df["picking_qty"] > 50)
        excess_qty = df.loc[c1 | c2 | c3 | c4 | c5 | c6 | c7 | c8 | c9]

        # !磁组材料不限领
        c1 = ~excess_qty["deltapn"].isin([i.deltapn for i in Mag.select()])
        excess_qty = excess_qty.loc[c1]

        if df["picking_qty"].isna().any():
            children = "领料数量请完整填写，必须为数字"
            return no_update, no_update, children, True, "danger"
        if (df["picking_qty"] > df["stock"]).any():
            children = "领用数量必须小于库存数量,请修改"
            return no_update, no_update, children, True, "danger"
        if not excess_qty.empty:
            pn = excess_qty["deltapn"].tolist()
            children = (
                f"{pn}领用数量超出限定,请修改数量,或发邮件给**********************核准"
            )
            return no_update, no_update, children, True, "danger"

        # * 检查磁组库存
        mag = Mag.select(lambda x: x.deltapn in df["deltapn"])[:]
        if mag:
            dfm = df.loc[df["deltapn"].isin(i.deltapn for i in mag)]
            df_mag = pd.DataFrame(i.to_dict() for i in mag)
            dfm = dfm.merge(
                df_mag[["deltapn", "id", "stock_no"]], on="deltapn", how="left"
            )
            for i in dfm.itertuples():
                Mag_stock_out(mag_stock_list=i.id, qty=i.picking_qty, owner=owner)

            # ========磁组邮件===============
            body = dfm.reindex(
                columns=[
                    "deltapn",
                    "des",
                    "mfgname",
                    "mfgpn",
                    "area",
                    "stock",
                    "stock_no",
                    "领用数量",
                ]
            )
            body.columns = body.columns.str.upper()
            body = body.to_html()
            to = f"{owner}@deltaww.com;<EMAIL>;<EMAIL>"
            subject = (
                f"【领料通知】{owner.title()},请至8楼磁组仓库找郭士霞,领取如下材料"
            )
            bg_mail(to, subject, body)

            # =======磁组标签===================
            dfm["owner1"] = owner
            dfm["prtno"] = "磁件"
            dfm["type"] = "mag"
            dfm["dept"] = dept
            dfm["qty"] = dfm["picking_qty"]
            dfm["stockno"] = dfm["stock_no"]
            dfm["label"] = (
                dfm["deltapn"].astype(str)
                + "{"
                + dfm["picking_qty"].astype(str)
                + "{Pur"
            )
            dfm["label_template"] = "picking"
            dfm = dfm.drop("领用数量", axis=1)
            bg_label_print(dfm.to_json(orient="records"))

            df = df.loc[~df["deltapn"].isin(dfm["deltapn"])]
            if df.empty:
                return (
                    [],
                    dropdown,
                    "领料提交成功,请至8楼磁组仓库找郭士霞领取",
                    True,
                    "success",
                )

        p1 = df["checkcode"].unique().tolist()
        ph = ",".join(["%s"] * len(p1))
        sql = f"select distinct deltapn from ssp.stockout where type=%s and owner1=%s \
            and to_days(stockoutdate)=to_days(now()) and checkcode in ({ph})"
        df1 = read_sql(sql, params=["debug", owner] + p1)
        if not df1.empty:
            children = f"{','.join(df1['deltapn'])}每日限领一次"
            return no_update, no_update, children, True, "danger"

        df["picking_qty"] = df["picking_qty"].astype(int)
        params = df["checkcode"].tolist()
        ph = ",".join(["%s"] * len(params))
        dept_group = dept.split("_")[0]
        sql = f"select id as stock_id,area,checkcode,stockno,qty as stock_qty \
            from ssp.stock \
            where checkcode in ({ph}) and (limituse=%s or limituse like %s)"
        params = params + ["all", "%" + dept_group + "%"]
        stock = read_sql(sql, params=params)
        df = df.merge(stock, on=["checkcode", "area"], how="left")
        df["stock_qty"] = df["stock_qty"].fillna(0)
        less_stock = df.loc[df["stock_qty"] < df["picking_qty"]]
        if not less_stock.empty:
            pn = less_stock["deltapn"].tolist()
            children = f"{pn}最新库存已不足领用，请修改"
            return no_update, no_update, children, True, "danger"

        # ========电子料架和散料同时存在时，优先出散料===============
        sql = "SELECT stock_id,stockno as stock_no from stockno_list \
            WHERE TYPE!=%s and stock_id in %s"
        params = ["电子料架"] + [tuple(df["stock_id"].tolist())]
        stockno_list = read_sql(sql, params=params)
        dfs = pd.DataFrame()
        if not stockno_list.empty:
            df = df.merge(
                stockno_list.drop_duplicates("stock_id"), on="stock_id", how="left"
            )
            c1 = df["stock_no"].notna()
            c2 = df["stockno"] != df["stock_no"]
            df["stockno"] = np.where(c1 & c2, df["stock_no"], df["stockno"])
            dfs = df.loc[c1 & c2, ["stock_id", "stock_no", "picking_qty"]]
            dfs["picking_qty"] = -dfs["picking_qty"]

        now = datetime.now()
        df["owner1"] = owner
        df["stockoutdate"] = now
        df["prtno"] = owner
        df["type"] = "Debug"
        df["dept"] = dept
        df["owner2"] = df["area"] + ".Material"
        df["lable"] = np.where(df["area"] == area, "picking", "cross_picking")
        df["source"] = "picking"
        df["stockoutdate2"] = now
        df["qty"] = df["picking_qty"]
        df["label"] = (
            df["checkcode"].astype(str)
            + "{"
            + df["picking_qty"].astype(str)
            + "{"
            + df["owner1"].astype(str)
        )
        df["dept_id"] = dept_id
        columns = [
            "stockoutdate",
            "dept",
            "type",
            "prtno",
            "deltapn",
            "qty",
            "owner1",
            "area",
            "stockoutdate2",
            "owner2",
            "lable",
            "checkcode",
            "source",
            "dept_id",
            "stock_id",
            "stockno",
        ]
        stock_out_data = df.reindex(columns=columns)
        fields = ",".join(stock_out_data.columns)
        ph = ",".join(["%s"] * stock_out_data.columns.size)
        stock_update_data = df.reindex(columns=["qty", "checkcode", "area"])

        with pool.connection() as conn:
            with conn.cursor() as cu:
                if not dfs.empty:
                    cu.executemany(
                        "insert into ssp.stockno_qty(stock_id,stockno,qty) values(%s,%s,%s)",
                        dfs.values.tolist(),
                    )

                cu.executemany(
                    f"insert into ssp.stockout({fields}) values({ph})",
                    stock_out_data.values.tolist(),
                )

                cu.executemany(
                    "update stock set qty=qty-%s where checkcode=%s and area=%s",
                    stock_update_data.values.tolist(),
                )
                conn.commit()
                children = "领用成功,领料时间:10:00-11:00,15:00-16:00"

                if (df["area"] != area).any() and datetime.now().hour > 15:
                    children = children + ",15点以后异地领料,不保证当天寄出,请知悉"

                df["label_template"] = "picking"
                df["prtno"] = f"{area}.{owner}"
                df = df.drop("领用数量", axis=1)
                bg_label_print(df.to_json(orient="records"))
                bg_access_record(user, "库存领料", "提交")
                return [], dropdown, children, True, "success"


# ----------------------买料--------------------------
@callback(
    Output("pur-required-date", "min_date_allowed"),
    Input("tab-num", "active_tab"),
)
def pur_date_allowed(active_tab):
    """需求日期需大于当前日期加一周"""
    if active_tab == "tab-3":
        return cal.find_workday(10)
    else:
        raise PreventUpdate


@callback(
    [
        Output("pur-table", "data"),
        Output("pur-table", "dropdown_conditional"),
    ],
    [
        Input("pur-add-row", "n_clicks"),
        Input("pur-table", "data_timestamp"),
        Input("pur-store", "data"),
        Input("pur-alert", "children"),
    ],
    [
        State("pur-table", "data"),
        State("pur-table", "columns"),
        State("pur-table", "active_cell"),
    ],
)
def pur_table(n, t, pur_store, alert, data, columns, active_cell):
    ctx = callback_context
    id = ctx.triggered[0]["prop_id"].split(".")[0]
    df = pd.DataFrame(data)
    columns = [i.get("name") for i in columns]
    df = df.reindex(columns=columns)
    df = df.fillna("")
    df = df.applymap(lambda x: x.strip())
    df["deltapn"] = df["deltapn"].astype(str).str.upper()
    df["mfgpn"] = df["mfgpn"].astype(str).str.upper()
    if id == "pur-add-row":
        empty_line = [{c: "" for c in df.columns}]
        df, dropdown_conditional = mat_category(df)
        df = df.append(empty_line, ignore_index=False)
        # df,dropdown_conditional=mat_category(df)
        return df.to_dict(orient="records"), dropdown_conditional

    elif id == "pur-alert":
        if alert == "买料申请提交成功":
            return [], no_update
        else:
            raise PreventUpdate

    elif id == "pur-store":
        df1 = pd.DataFrame(pur_store)
        df2 = df1.loc[~df1["deltapn"].isin(df["deltapn"])]
        df = pd.concat([df, df2])
        df = df.dropna(how="all")
        df, dropdown_conditional = mat_category(df)
        return df.to_dict(orient="records"), dropdown_conditional

    elif id == "pur-table":
        column_id = active_cell.get("column_id") if active_cell else None

        if column_id in ("deltapn", "mfgpn", "des"):
            column_id = "mfgpn" if column_id == "des" else column_id
            param = [i.strip() for i in df[column_id].tolist() if i]
            ph = ",".join(["%s"] * len(param))
            cond = f"{column_id} in ({ph})"

            sql = f"select checkcode as checkcode1,deltapn as deltapn1,des as des1,\
                mfgname as mfgname1,mfgpn as mfgpn1 \
                from ssp_csg.mat_info where {cond}"
            mat_info = read_sql(sql, params=param)

            mat_info["deltapn1"] = mat_info["deltapn1"].astype(str).str.upper()
            mat_info["mfgpn1"] = mat_info["mfgpn1"].astype(str).str.upper()
            mat_info = mat_info.drop_duplicates([f"{column_id}1"])
            mat_info = mat_info.drop_duplicates(["deltapn1", "mfgpn1"])

            df = df.merge(
                mat_info, how="left", left_on=f"{column_id}", right_on=f"{column_id}1"
            )
            df["deltapn"] = np.where(
                df[f"{column_id}1"].notnull(), df["deltapn1"], df["deltapn"]
            )
            df["des"] = np.where(df[f"{column_id}1"].notnull(), df["des1"], df["des"])
            df["mfgname"] = np.where(
                df[f"{column_id}1"].notnull(), df["mfgname1"], df["mfgname"]
            )
            df["mfgpn"] = np.where(
                df[f"{column_id}1"].notnull(), df["mfgpn1"], df["mfgpn"]
            )
            df["checkcode"] = np.where(
                df[f"{column_id}1"].notnull(), df["checkcode1"], df["checkcode"]
            )

            df["deltapn"] = np.where(df["deltapn"] == "", "NEWPART", df["deltapn"])
            df["mfgpn"] = np.where(df["mfgpn"] == "", "NEWPART", df["mfgpn"])
            df["checkcode"] = np.where(
                df["deltapn"] == "NEWPART", "NEWPART", df["checkcode"]
            )
        df, dropdown_conditional = mat_category(df)
        df = df.reindex(columns=columns)
        return df.to_dict(orient="records"), dropdown_conditional


@callback(
    [
        Output("pur-alert", "children"),
        Output("pur-alert", "is_open"),
        Output("pur-alert", "color"),
    ],
    [
        Input("submit-pur", "n_clicks"),
    ],
    [
        State("pur-table", "data"),
        State("pur-proj", "value"),
        State("pur-annual-qty", "value"),
        State("pur-remark", "value"),
        State("pur-mp-date", "date"),
        State("pur-required-date", "date"),
        State("user", "data"),
    ],
)
def submit_pur(n, data, proj, annual_qty, remark, mp_date, required_date, user):
    if not all((proj, annual_qty, mp_date, required_date)):
        return "请填写完整机种信息", True, "danger"
    df = pd.DataFrame(data)
    if (
        df[["deltapn", "mfgname", "mfgpn", "主类型", "次类型", "需求数量"]]
        .isin([""])
        .any()
        .any()
    ):
        return (
            "如下栏位信息必填(DELTAPN,MFGNAME,MFGPN,主类型,次类型,需求数量)",
            True,
            "danger",
        )
    dept = user.get("dept")
    dept_id = user.get("dept_id")
    params = df["deltapn"].tolist()
    ph = ",".join(["%s"] * len(params))
    cond = f"deltapn in ({ph})"
    df1 = mat_info(dept_id, "ALL", cond, params)
    # * 1类，2类不能买料
    # * 1.排除remark 含有BU_STD_PART/COMMON PART部分
    # * 2.排除block 含有product forbidden的不做库存的屏蔽
    c1 = df1["block_grade"].isin([1, 2])
    c2 = ~df1["remark"].astype(str).str.contains("BU_STD_PART|COMMON_PART", na=False)
    c3 = ~df1["block"].astype(str).str.contains("ProductForbidden", na=False)
    if (c1 & (c2 | c3)).any():
        df1 = df1.loc[c1, ["deltapn", "block"]].drop_duplicates(["deltapn"])
        x = ";".join(f"{i.deltapn}-{i.block}" for i in df1.itertuples())
        return f"如下材料管控购买,请删除{x}", True, "danger"

    df = df.rename(
        columns={"主类型": "mat_catelogue", "次类型": "mat_group", "需求数量": "qty"}
    )

    sql = "select cast(max(item_pur) AS UNSIGNED)+1 as maxid from ssp.pur"
    pur = read_sql(sql)
    df["item_pur"] = range(int(pur["maxid"][0]), int(pur["maxid"][0]) + df.shape[0])
    df["checkcode"] = df["checkcode"].fillna("").str.upper()
    df["deltapn"] = df["deltapn"].fillna("").str.upper()
    c1 = df["checkcode"] == ""
    c2 = df["checkcode"] == "NEWPART"
    c3 = df["deltapn"] == "NEWPART"
    dfce = df.loc[c1 | c2 | c3]
    if not dfce.empty:
        dfce["des"] = dfce["des"].fillna("")
        dfce["des"] = np.where(
            dfce["des"] == "",
            dfce["mat_catelogue"] + " " + dfce["mat_group"],
            dfce["des"],
        )
        dfce = get_temp_pn(dept_id, dfce)
        for i in dfce.itertuples():
            df.loc[i.Index, "checkcode"] = i.temp_pn
            df.loc[i.Index, "deltapn"] = i.temp_pn
            df.loc[i.Index, "des"] = i.des

    df["checkcode"] = np.where(df["checkcode"] == "", df["deltapn"], df["checkcode"])
    df["application"] = "Debug"
    df["dept"] = dept
    df["proj"] = proj
    df["rd"] = user.get("nt_name")
    df["req_date"] = required_date
    df["mat_remark"] = remark
    df["pur_status"] = "pending"
    df["r_qty"] = df["qty"]
    df["start_date"] = datetime.now()
    df["plant_qty"] = annual_qty
    df["location"] = mp_date
    df["dept_id"] = dept_id
    df["dest_area"] = user.get("area")
    fields = ",".join(df.columns)
    ph = ",".join(["%s"] * df.columns.size)
    params = df.values.tolist()

    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = f"insert into ssp.pur({fields}) values({ph})"
            cu.executemany(sql, params)
            conn.commit()

    bg_access_record(user, "申请买料", "提交")
    return "买料申请提交成功", True, "success"


# -------函数----------------
@db_session
def mat_info(dept_id: int, customer: str, cond: str, params: list):
    """[添加材料禁用信息备注]"""
    sql = "select * from ssp_csg.mat_status where dept_id=%s"
    df1 = read_sql(sql, params=[dept_id])
    # d=Dept.get(id=dept_id)
    # dept=d.dept_group# if d.dept_group==d.dept_name else d.dept_group+'_'+d.dept_name
    dept_cust = f"{dept_id}:{customer}"

    sql = (
        f"select deltapn,des,mfgname,mfgpn,checkcode,bg_block,gradeb,eol,nrnd,msl_block,\
        esd_block,antis,non_auto,autom,not_in_bgallpartlist,double85,ai,ssp_block,hf,lf,\
        non_hf->>%s as non_hf,\
        common_part->>%s as common_part,\
        bu_std_part->>%s as bu_std_part,\
        system_block->>%s as system_block,\
        limit_use->>%s as limit_use_dept_cust, \
        limit_use->>%s as limit_use_dept_all, \
        limit_use->>%s as limit_use_all_all,\
        limit_use->>%s as limit_use_all,\
        quality_issue->>%s as quality_issue_dept_cust,\
        quality_issue->>%s as quality_issue_dept_all,\
        quality_issue->>%s as quality_issue_all_all,\
        quality_issue->>%s as quality_issue_all,\
        ce_alert->>%s as ce_alert_dept_cust,\
        ce_alert->>%s as ce_alert_dept_all,\
        ce_alert->>%s as ce_alert_all_all,\
        ce_alert->>%s as ce_alert_all\
        from ssp_csg.mat_info where {cond}"
    )
    params = [
        f'$."{dept_cust}"',
        f'$."{dept_id}"',
        f'$."{dept_id}"',
        f'$."{dept_id}"',
        f'$."{dept_cust}"',
        f'$."{dept_id}:ALL"',
        '$."ALL:ALL"',
        "$.*",
        f'$."{dept_cust}"',
        f'$."{dept_id}:ALL"',
        '$."ALL:ALL"',
        "$.*",
        f'$."{dept_cust}"',
        f'$."{dept_id}:ALL"',
        '$."ALL:ALL"',
        "$.*",
    ] + params
    df = read_sql(sql, params=params)

    df["limit_use"] = np.where(
        df["limit_use_all_all"].notna(), df["limit_use_all_all"], ""
    )
    df["limit_use"] = np.where(
        df["limit_use_dept_all"].notna(), df["limit_use_dept_all"], df["limit_use"]
    )
    df["limit_use"] = np.where(
        df["limit_use_dept_cust"].notna(), df["limit_use_dept_cust"], df["limit_use"]
    )

    df["quality_issue"] = np.where(
        df["quality_issue_all_all"].notna(), df["quality_issue_all_all"], ""
    )
    df["quality_issue"] = np.where(
        df["quality_issue_dept_all"].notna(),
        df["quality_issue_dept_all"],
        df["quality_issue"],
    )
    df["quality_issue"] = np.where(
        df["quality_issue_dept_cust"].notna(),
        df["quality_issue_dept_cust"],
        df["quality_issue"],
    )

    df["ce_alert"] = np.where(
        df["ce_alert_all_all"].notna(), df["ce_alert_all_all"], ""
    )
    df["ce_alert"] = np.where(
        df["ce_alert_dept_all"].notna(), df["ce_alert_dept_all"], df["ce_alert"]
    )
    df["ce_alert"] = np.where(
        df["ce_alert_dept_cust"].notna(), df["ce_alert_dept_cust"], df["ce_alert"]
    )

    # c1=df['quality_issue']!=''
    # c2=df['limit_use']!=''
    # c3=df['ce_alert']!=''
    # df['warning']=np.where(c1|c2|c3,'[None]',None)
    # df['warning']=df['warning'].combine_first(df['quality_issue_all'])
    # df['warning']=df['warning'].combine_first(df['limit_use_all'])
    # df['warning']=df['warning'].combine_first(df['ce_alert_all'])
    # df['warning']=df['warning'].fillna('[None]').map(lambda x:eval(str(x))[0])

    # df['quality_issue_all']=df['quality_issue_all'].fillna('[]')
    # df['limit_use_all']=df['limit_use_all'].fillna('[]')
    # df['ce_alert_all']=df['ce_alert_all'].fillna('[]')

    # df['warning']=df[['quality_issue_all','limit_use_all','ce_alert_all']].applymap(eval).sum(1)\
    #     .apply(lambda x:','.join(set(x)))

    df["warning"] = np.where(df["ce_alert_all"].notna(), "LowRisk", None)
    df["warning"] = np.where(df["limit_use_all"].notna(), "MediumRisk", df["warning"])
    df["warning"] = np.where(df["quality_issue_all"].notna(), "HighRisk", df["warning"])

    if not df.empty:  # ? 是否即将EOL
        params = df["deltapn"].unique().tolist()
        ph = ",".join(["%s"] * len(params))
        sql = f"select deltapn from ssp_br.z_basicdata_nrpeol \
            where deltapn in ({ph}) and status in (%s,%s)"
        params = params + ["eol", "nrp"]
        eol = read_sql(sql, params=params)

        c1 = ~df["des"].str.startswith(("EOL", "NRND"))
        c2 = df["deltapn"].isin(eol["deltapn"])
        df["warning"] = np.where(c1 & c2, "HighRisk", df["warning"])

    df1 = df1.loc[:, df1.columns.isin(df.columns)]
    df1 = df1.astype(int)
    df = df.fillna("")

    dfb = df1.loc[:, df1.iloc[0].isin([1, 2, 3])]
    cols = dfb.sort_values(0, axis=1).columns
    df["block"] = [
        ",".join(getattr(y, i) for i in cols if getattr(y, i)) for y in df.itertuples()
    ]
    # ! 0341486306 DES_CDBU和DES_IMBU不一样
    dfs = df[dfb.columns]
    dfs = dfs.replace("", 5)
    dfs = dfs.fillna(5)
    dfs = dfs.apply(lambda x: x.where(x == 5, dfb[x.name][0]))
    df["block_grade"] = dfs.min(axis=1).astype(int)

    dfr = df1.loc[:, df1.iloc[0].isin([4, 6, 7])]
    cols = dfr.sort_values(0, axis=1).columns
    df["remark"] = [
        ",".join(getattr(y, i) for i in cols if getattr(y, i)) for y in df.itertuples()
    ]

    dfs = df[dfr.columns]
    dfs = dfs.replace("", 5)
    dfs = dfs.fillna(5)
    dfs = dfs.apply(lambda x: x.where(x == 5, dfr[x.name][0]))
    df["remark_grade"] = dfs.max(axis=1).astype(int)

    # df['status']=df[['remark_grade','block_grade']].min(axis=1).astype(int)
    df["source"] = "main"
    columns = [
        "checkcode",
        "deltapn",
        "des",
        "mfgname",
        "mfgpn",
        "block_grade",
        "remark_grade",
        "block",
        "remark",
        "warning",
        "antis",
        "double85",
        "ai",
        "source",
        "lf",
        "hf",
    ]
    df = df.reindex(columns=columns)
    return df


@db_session
def mat_stock(df: pd.DataFrame, dept_id: int, user_area: str):
    """[查询库存数量]

    Parameters
    ----------
    df : pd.DataFrame
        [description]
    dept : str
        [description]

    Returns
    -------
    [type]
        [description]
    """
    params = df["checkcode"].dropna().unique().tolist()
    if not params:
        df["sh_stock"] = None
        df["hz_stock"] = None
        return df

    ph = ",".join(["%s"] * len(params))
    d = Dept.get(id=dept_id)
    dept_group = d.dept_group

    if dept_id == 17:  # EVCS_SYSTEM的无专用料权限
        dept_group = f"{d.dept_group}_{d.dept_name}"

    sql = f'SELECT checkcode,\
        coalesce(sum(case when area = "SH" then qty END), 0) AS sh_stock,\
        coalesce(sum(case when area = "WH" then qty END), 0) AS wh_stock,\
        coalesce(sum(case when area = "CQ" then qty END), 0) AS cq_stock,\
        coalesce(sum(case when area = "NJ" then qty END), 0) AS nj_stock,\
        coalesce(sum(case when area = "HZ" then qty END), 0) AS hz_stock \
        FROM ssp.stock \
        where checkcode in ({ph}) and (limituse=%s or limituse like %s) \
        group BY checkcode'
    params = params + ["all", "%" + dept_group + "%"]
    stock = read_sql(sql, params=params)

    df = df.merge(stock, on="checkcode", how="left")
    # * 1类，2类不显示库存
    # * 1.排除remark 含有BU_STD_PART/COMMON PART部分
    # * 2.排除block 含有product forbidden的不做库存的屏蔽

    # c1 = df["block_grade"] < 3
    # c2 = ~df["remark"].str.contains("BU_STD_PART|COMMON_PART", na=False)
    # c3 = ~df["block"].str.contains("ProductForbidden", na=False)

    # df["sh_stock"] = np.where(c1 & (c2 | c3), None, df["sh_stock"])
    # df["hz_stock"] = np.where(c1 & (c2 | c3), None, df["hz_stock"])
    # df["wh_stock"] = np.where(c1 & (c2 | c3), None, df["wh_stock"])

    # *上海的只能领上海的
    if user_area == "SH":
        df["hz_stock"] = None
        if dept_id != 1:
            df["wh_stock"] = None
    elif user_area == "HZ":
        if dept_id != 21:
            df["wh_stock"] = None
    elif user_area == "WH":
        if dept_id != 21:
            df["hz_stock"] = None
    elif user_area in ("CQ", "NJ"):
        df["hz_stock"] = None
        df["wh_stock"] = None

    # 德国，泰国,印度无库存
    df["de_stock"] = None
    df["th_stock"] = None
    df["in_stock"] = None

    df["qty"] = df[["sh_stock", "hz_stock", "wh_stock", "cq_stock", "nj_stock"]].max(
        axis=1
    )

    # * 检查磁组库存，如果磁组有库存，以磁组库存优先
    mag = Mag.select(lambda x: x.deltapn in df["checkcode"].dropna())[:]
    mag = pd.DataFrame(i.to_dict() for i in mag)
    mag = mag.reindex(columns=["deltapn", "qty"])
    mag = mag.rename(columns={"qty": "mag_stock"})
    mag = mag.drop_duplicates("deltapn")
    df = df.merge(mag, on="deltapn", how="left")
    df["qty"] = np.where(df["mag_stock"] > 0, df["mag_stock"], df["qty"])
    df["sh_stock"] = np.where(df["mag_stock"] > 0, df["mag_stock"], df["sh_stock"])

    return df


@db_session
def query_stock(df: pd.DataFrame, dept_id: int) -> pd.DataFrame:
    """_summary_

    Args:
        df (pd.DataFrame): _description_
        dept_id (int): _description_

    Returns:
        _type_: _description_
    """
    params = df["checkcode"].dropna().unique().tolist()
    if not params:
        df["sh_stock"] = None
        df["hz_stock"] = None
        return df

    ph = ",".join(["%s"] * len(params))
    d = Dept.get(id=dept_id)
    dept_group = d.dept_group
    area = d.area
    if dept_id == 17:  # EVCS_SYSTEM的无专用料权限
        dept_group = f"{d.dept_group}_{d.dept_name}"

    sql = f'SELECT checkcode,\
        coalesce(sum(case when area = "SH" then qty END), 0) AS sh_stock,\
        coalesce(sum(case when area = "WH" then qty END), 0) AS wh_stock,\
        coalesce(sum(case when area = "HZ" then qty END), 0) AS hz_stock \
        FROM ssp.stock \
        where checkcode in ({ph}) and (limituse=%s or limituse like %s) \
        group BY checkcode'
    params = params + ["all", "%" + dept_group + "%"]
    stock = read_sql(sql, params=params)

    df = df.merge(stock, on="checkcode", how="left")

    # * 1类，2类不显示库存
    # * 1.排除remark 含有BU_STD_PART/COMMON PART部分
    # * 2.排除block 含有product forbidden的不做库存的屏蔽

    c1 = df["block_grade"] < 3
    c2 = ~df["remark"].str.contains("BU_STD_PART|COMMON_PART", na=False)
    c3 = ~df["block"].str.contains("ProductForbidden", na=False)

    df["sh_stock"] = np.where(c1 & (c2 | c3), None, df["sh_stock"])
    df["hz_stock"] = np.where(c1 & (c2 | c3), None, df["hz_stock"])

    if area == "SH":
        df["hz_stock"] = None

    # * 检查磁组库存，如果磁组有库存，以磁组库存优先
    mag = Mag.select(lambda x: x.deltapn in df["checkcode"].dropna())[:]
    mag = pd.DataFrame(i.to_dict() for i in mag)
    mag = mag.reindex(columns=["deltapn", "qty"])
    mag = mag.drop_duplicates("deltapn")
    df = df.merge(mag, on="deltapn", how="left")
    df["sh_stock"] = np.where(df["qty"] > 0, df["qty"], df["sh_stock"])

    return df


def mat_category(df: pd.DataFrame):
    """[添加材料主类型,次类型]

    Parameters
    ----------
    df : pd.DataFrame
        [description]

    Returns
    -------
    [type]
        [description]
    """
    sql = "select category_1 as c1,pur_category2 as c2,pur_category_3 as c3,\
        pur_keyword_pn as cond_pn,pur_keyword_des as cond_des \
        from ssp_ce.a_mat_catalogue"
    cat = read_sql(sql)

    cat = cat.dropna(subset=["cond_pn", "cond_des"])
    cat["cond_pn"] = "^" + cat["cond_pn"] + "$"
    cat["cond_des"] = "^" + cat["cond_des"] + "$"
    cat["cond_pn"] = cat["cond_pn"].str.replace("*", ".*", regex=False)
    cat["cond_des"] = cat["cond_des"].str.replace("*", ".*", regex=False)
    for i in df.itertuples():
        for j in cat.itertuples():
            if re.match(j.cond_pn, i.deltapn) and re.match(j.cond_des, i.des):
                df["主类型"][i.Index] = j.c1
                df["次类型"][i.Index] = j.c2

    dropdown_conditional = [
        {
            "if": {"column_id": "次类型", "filter_query": f'{"{主类型}"} eq "{j}"'},
            "options": [
                {"label": i, "value": i} for i in cat.query("c1==@j")["c2"].unique()
            ],
            "clearable": False,
        }
        for j in df["主类型"]
    ]
    return df, dropdown_conditional


@db_session
def add_second_source(df1: pd.DataFrame, dept_id: int, customer: str, user_area: str):
    """
    [添加替代料]
    Parameters
    ----------
    df1 : pd.DataFrame
        [description]
    dept : str
        [description]
    Returns
    -------
    [type]
        [description]
    """

    dfs = []
    df1 = df1[["id", "deltapn"]]
    dept_group = Dept.get(id=dept_id).dept_group
    # a_oftenmaterial
    params = df1["deltapn"].unique().tolist()
    ph = ",".join(["%s"] * len(params))
    sql = f"select deltapn,sub_group as grp from ssp_ce.a_oftenmaterial where dept=%s \
        and sub_group in (select sub_group from ssp_ce.a_oftenmaterial where deltapn in ({ph}))"
    params1 = [dept_group] + params
    common = read_sql(sql, params=params1)
    df = df1.merge(common, on="deltapn").merge(
        common.rename(columns={"deltapn": "2nd"}), on="grp"
    )
    dfs.append(df)

    # z_basicdata_bugp
    sql = f"Select deltapn,bugp as grp from ssp_br.z_basicdata_bugp \
        where bugp in (select bugp from ssp_br.z_basicdata_bugp where deltapn in ({ph}))"
    bugp = read_sql(sql, params=params)
    df = df1.merge(bugp, on="deltapn").merge(
        bugp.rename(columns={"deltapn": "2nd"}), on="grp"
    )
    dfs.append(df)

    # z_basicdata_koagp
    sql = f"select deltapn,koa_group as grp from ssp_br.z_basicdata_koagp \
        where koa_group in (select koa_group from ssp_br.z_basicdata_koagp \
            where deltapn in ({ph})) and koa_group!=%s"
    koagp = read_sql(sql, params=params + [""])
    df = df1.merge(koagp, on="deltapn").merge(
        koagp.rename(columns={"deltapn": "2nd"}), on="grp"
    )
    dfs.append(df)

    # z_basicdata_nrpeol
    sql = f"select deltapn,sub_group as grp from ssp_br.z_basicdata_nrpeol \
        where sub_group in (select sub_group from ssp_br.z_basicdata_nrpeol \
            where deltapn in ({ph}))"
    eolgp = read_sql(sql, params=params)
    df = df1.merge(eolgp, on="deltapn").merge(
        eolgp.rename(columns={"deltapn": "2nd"}), on="grp"
    )
    dfs.append(df)

    # eol_list_group
    sql = f"select deltapn,  grp from ssp_csg.eol_list_group \
        where grp in (select grp from ssp_csg.eol_list_group where deltapn in ({ph}))"
    eolgp2 = read_sql(sql, params=params)
    df = df1.merge(eolgp2, on="deltapn").merge(
        eolgp2.rename(columns={"deltapn": "2nd"}), on="grp"
    )
    dfs.append(df)

    df = pd.concat(dfs)
    df = df[["id", "2nd"]].rename(columns={"2nd": "deltapn"})
    df = df.loc[
        ~(df["id"].astype(str) + df["deltapn"]).isin(
            df1["id"].astype(str) + df1["deltapn"]
        )
    ]
    df = df.drop_duplicates(["id", "deltapn"])
    if df.empty:
        return pd.DataFrame()

    params = df["deltapn"].unique().tolist()
    ph = ",".join(["%s"] * len(params))
    cond = f"deltapn in ({ph})"
    df_info = mat_info(dept_id, customer, cond, params)
    df_info = mat_stock(df_info, dept_id, user_area)
    df = df.merge(df_info, how="left", on="deltapn")

    df = df.loc[
        df["block_grade"].fillna(0).astype(int) > 1
    ]  # ? 禁用等于1类的替代料不显示
    df["source"] = "second"
    return df


@db_session
def action_dropdown_data(user: dict, dept_id: int, df: pd.DataFrame, database=None):
    """[查询结果action下拉菜单]

    Parameters
    ----------
    df : pd.DataFrame
        [description]
    """
    df = df.copy()
    di = Dept.get(id=dept_id)
    user_area = user["area"]
    df["user_area"] = user_area
    # df["dept"] = di.dept

    if di.purchasing_permit == "Y":
        df["dropdown"] = [{"领料", "买料", "替代料"}] * df.shape[0]
    else:
        df["dropdown"] = [{"领料", "替代料"}] * df.shape[0]

    # # ! 1类，2类不能领和买
    # df["dropdown"] = np.where(
    #     df["block_grade"] < 3, df["dropdown"] - {"领料", "买料"}, df["dropdown"]
    # )

    # c1 = df["deltapn"].isin([i.deltapn for i in Mag.select()])  # !磁组材料
    # df["dropdown"] = np.where(
    #     c1, df["dropdown"].apply(lambda x: x | {"领料"}), df["dropdown"]
    # )  # !磁组材料都可以领
    # df["dropdown"] = np.where(
    #     c1, df["dropdown"] - {"买料"}, df["dropdown"]
    # )  # !磁组材料不能买

    # c1 = df["sh_stock"].isna()
    # c2 = df["hz_stock"].isna()
    # c3 = df["area"] == "SH"
    # df["dropdown"] = np.where(
    #     c1 & c2, df["dropdown"] - {"领料"}, df["dropdown"]
    # )  # 无库存不能领料
    # df["dropdown"] = np.where(
    #     c1 & c3, df["dropdown"] - {"领料"}, df["dropdown"]
    # )  # 上海不能异地领料

    # *没库存不能领料
    c1 = df["qty"].isna()
    df["dropdown"] = np.where(c1, df["dropdown"] - {"领料"}, df["dropdown"])

    # *EOL不能买料
    c1 = df["des"].str.contains("EOL|NRND", na=False, case=False)
    df["dropdown"] = np.where(c1, df["dropdown"] - {"买料"}, df["dropdown"])

    df["dropdown"] = np.where(
        df["source"] == "second", df["dropdown"] - {"替代料"}, df["dropdown"]
    )

    # *德国和泰国,印度不能领料和买料
    df["dropdown"] = np.where(
        df["user_area"].isin(["DE", "TH", "IN"]),
        df["dropdown"] - {"领料", "买料"},
        df["dropdown"],
    )
    df["dropdown"] = df["dropdown"].apply(sorted)

    if database == "common":
        df["dropdown"] = df["dropdown"].apply(lambda x: x + ["参数"])

    label_map = {"替代料": "2ndSource", "参数": "Parameter"}

    dropdown_data = [
        {
            "action": {
                "options": [{"label": label_map.get(j, j), "value": j} for j in i],
                "clearable": False,
            }
        }
        for i in df["dropdown"]
    ]

    return dropdown_data


def query_stock_by_keywords(cond: str, params: list):
    cond = cond + " and qty>%s"
    params = params + [0]
    sql = f"select deltapn from ssp.stock where {cond}"
    df = read_sql(sql, params=params)
    if df.empty:
        return "", []
    params = df["deltapn"].tolist()
    ph = ",".join(["%s"] * len(params))
    cond = f"deltapn in ({ph})"
    return cond, params


@db_session
def query_common_by_keywords(dept_id: int, cond: str, params: list):
    dept_group = Dept.get(id=dept_id).dept_group
    cond = cond + " and dept=%s and status in (%s,%s)"
    params = params + [dept_group] + ["common", "tbd"]
    sql = f"select deltapn from ssp_ce.a_oftenmaterial where {cond}"
    df = read_sql(sql, params=params)

    if df.empty:
        return "", []
    params = df["deltapn"].tolist()
    ph = ",".join(["%s"] * len(params))
    cond = f"deltapn in ({ph})"
    return cond, params


def make_accordion_item(df, i, j):
    """禁用信息弹窗"""
    df = df.loc[df["reason"] == j]
    block_typ = ",".join(df["block_type"].unique())
    df1 = df[["dept", "block_proj", "vendor", "model", "block_source"]]
    df1 = df1.rename(
        columns={
            "dept": "部门",
            "block_proj": "适用项目",
            "vendor": "禁用厂商",
            "model": "禁用器件或型号",
            "block_source": "禁用来源",
        }
    )

    accordion = dbc.Accordion(
        dbc.AccordionItem(
            dbc.Table.from_dataframe(df1, striped=True, bordered=True, hover=True),
            title=j,
            item_id=f"{i}",
        ),
        always_open=True,
        active_item=f"{i}",
    )

    if block_typ == "EOL":
        step = html.I(className="fa fa-arrow-circle-right fa-2x text-danger")
    else:
        step = html.I(className="fa fa-arrow-circle-right fa-2x text-warning")

    return dbc.Row(
        [
            dbc.Col(html.B(block_typ), width=2, align="center"),
            dbc.Col(step, width=1, align="center"),
            dbc.Col(accordion, width=9, align="center"),
        ]
    )


clientside_callback(
    ClientsideFunction(namespace="clientside", function_name="materialIntroCopy"),
    Output("material-intro-btn-1", "className"),
    Input("material-intro-btn-1", "n_clicks"),
)
clientside_callback(
    ClientsideFunction(namespace="clientside", function_name="materialIntroBatchQuery"),
    Output("material-intro-btn-2", "className"),
    Input("material-intro-btn-2", "n_clicks"),
)
clientside_callback(
    ClientsideFunction(namespace="clientside", function_name="materialIntroTP"),
    Output("material-intro-btn-3", "className"),
    Input("material-intro-btn-3", "n_clicks"),
)

clientside_callback(
    ClientsideFunction(namespace="clientside", function_name="materialIntroFilter"),
    Output("material-intro-btn-4", "className"),
    Input("material-intro-btn-4", "n_clicks"),
)

clientside_callback(
    ClientsideFunction(namespace="clientside", function_name="materialIntroTableInput"),
    Output("material-intro-btn-5", "className"),
    Input("material-intro-btn-5", "n_clicks"),
)


@callback(
    [
        Input("material-intro-btn-1", "n_clicks_timestamp"),
        Input("material-intro-btn-2", "n_clicks_timestamp"),
        Input("material-intro-btn-3", "n_clicks_timestamp"),
        Input("material-intro-btn-4", "n_clicks_timestamp"),
    ],
    [
        State("material-intro-btn-1", "children"),
        State("material-intro-btn-2", "children"),
        State("material-intro-btn-3", "children"),
        State("material-intro-btn-4", "children"),
        State("user", "data"),
    ],
)
def intro_record(n1, n2, n3, n4, c1, c2, c3, c4, user):
    """访问记录点击视频的链接"""
    nn = (n1 or 0, n2 or 0, n3 or 0, n4 or 0)
    if not any(nn):
        raise PreventUpdate
    cc = (c1, c2, c3, c4)
    x = cc[nn.index(max(nn))]
    bg_access_record(user, "新手指引", x)
