# -*- coding: utf-8 -*-
import logging

from flask import g
from werkzeug.local import LocalProxy

from config import pool
from tasks import bg_mail

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DB:
    def __init__(self, pool):
        self.pool = pool
        self.conn = None
        self.cu = None

    def get_connection(self):
        """
        从连接池中获取一个数据库连接
        :return: 数据库连接对象
        """
        self.conn = self.pool.connection()
        self.conn.begin()
        return self.conn

    def begin(self):
        """
        开始一个新的事务
        """
        if self.conn is None:
            self.get_connection()
        self.conn.begin()

    def rollback(self):
        """
        回滚当前事务
        """
        if self.conn:
            self.conn.rollback()

    def commit(self):
        if self.conn:
            self.conn.commit()

    def close(self):
        if self.cu:
            self.cu.close()
        if self.conn:
            self.conn.close()
        self.conn = None
        self.cu = None

    def execute(self, sql: str, params: list = None, type: str = "select"):
        try:
            self.cu = self.conn.cursor()
            self.cu.execute(sql, params)
            if type == "insert":
                return self.cu.lastrowid
            elif type == "select":
                res = self.cu.fetchall()
                res = [{x.lower(): y for x, y in i.items()} for i in res]
                return res
            else:
                return self.cu.rowcount
        except Exception as e:
            logger.error(f"SQL execute failed: {e}{sql}{params}")
            bg_mail(
                "<EMAIL>",
                "【DBTOOL execute ERROR】",
                f"{e}{sql}{params}",
            )
            self.rollback()
            # raise e

    def execute_many(self, sql: str, params: list[list] = None):
        try:
            self.cu = self.conn.cursor()
            self.cu.executemany(sql, params)
            return self.cu.rowcount
        except Exception as e:
            logger.error(f"SQL execute_many failed: {e}{sql}{params}")
            bg_mail(
                "<EMAIL>",
                "【DBTOOL execute_many ERROR】",
                f"{e}{sql}{params}",
            )
            self.rollback()
            # raise e

    def execute_fetchone(self, sql, params=None):
        try:
            self.cu = self.conn.cursor()
            self.cu.execute(sql, params)
            res = self.cu.fetchone()
            if res:
                res = {x.lower(): y for x, y in res.items()}
            return res
        except Exception as e:
            logger.error(f"SQL execute_fetchone failed: {e}{sql}{params}")
            bg_mail(
                "<EMAIL>",
                "【DBTOOL execute_fetchone ERROR】",
                f"{e}{sql}{params}",
            )
            self.rollback()
            # raise e

    def find_one(self, table: str, filters: dict) -> dict:
        """find one row by query.
        :param table: the table name or entity class
        :param filters: the query conditions
        """
        if not filters:
            return
        where = " AND ".join(f"{i}=%s" for i in filters.keys())
        if where:
            where = "WHERE " + where
        sql = f"SELECT * FROM {table} {where}"
        params = tuple(filters.values())
        return self.execute_fetchone(sql, params)

    def find(self, table: str, filters: dict) -> list[dict]:
        """find one row by query.
        :param table: the table name or entity class
        :param filters: the query conditions
        """
        if not filters:
            return
        where = " AND ".join(f"{i}=%s" for i in filters.keys())
        if where:
            where = "WHERE " + where
        sql = f"SELECT * FROM {table} {where}"
        params = tuple(filters.values())
        return self.execute(sql, params)

    def insert(self, table: str, data: dict):
        """
        插入单条数据
        """
        columns = ", ".join(data.keys())
        values_template = ", ".join(["%s"] * len(data))
        sql = f"INSERT INTO {table} ({columns}) VALUES ({values_template})"
        params = tuple(data.values())
        return self.execute(sql, params, type="insert")

    def update(self, table: str, data: dict):
        """update one row by id.
        :param data: the data of row, allow types dict or object instant
        :param table: the table name or entity class
        :return: returns effective rows counts
        """
        id = data.pop("id", None)
        if id:
            fields = ", ".join(f"{i}=%s" for i in data.keys())
            sql = f"update {table} set {fields} where id=%s"
            params = tuple(data.values()) + (id,)
            self.execute(sql, params, type="update")

    def delete(self, table: str, filters: dict):
        """delete rows by id.
        :param table: the table name or entity class
        :param filters: the query conditions
        :return: returns effective rows counts
        """
        where = " AND ".join(f"{i}=%s" for i in filters.keys())
        if where:
            where = "WHERE " + where
        sql = f"DELETE FROM {table} {where}"
        params = tuple(filters.values())
        self.execute(sql, params, type="delete")


_db = DB(pool)


def get_db():
    if "db" not in g:
        g.db = _db
        g.db.get_connection()
    return g.db


db: DB = LocalProxy(get_db)
