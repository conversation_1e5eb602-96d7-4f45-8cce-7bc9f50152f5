# -*- coding: utf-8 -*-
from config import pool


class DB:
    def __init__(self):
        self.conn = pool.connection()
        self.cu = self.conn.cursor()

    def execute(self, sql: str, params: list = None, insert: bool = False):
        """
        执行，返回的为 list
        """
        try:
            self.conn.begin()
            self.cu.execute(sql, params)
            if insert:
                return self.cu.lastrowid
            else:
                res = self.cu.fetchall()
                res = [{x.lower(): y for x, y in i.items()} for i in res]
                return res
        except Exception as e:
            self.conn.rollback()
            self.cu.close()
            self.conn.close()
            raise Exception(e)

    def commit(self):
        self.conn.commit()

    def close(self):
        self.cu.close()
        self.conn.close()

    def execute_many(self, sql: str, params: list[list] = None):
        """
        批量执行
        """
        try:
            self.conn.begin()
            self.cu.executemany(sql, params)
            res = self.cu.fetchall()
            res = [{x.lower(): y for x, y in i.items()} for i in res]
            return res
        except Exception as e:
            self.conn.rollback()
            raise Exception(e)

    def execute_fetchone(self, sql, params=None):
        """
        执行，返回的为 dict
        """
        try:
            self.conn.begin()
            self.cu.execute(sql, params)
            res = self.cu.fetchone()
            if res:
                res = {x.lower(): y for x, y in res.items()}
            return res
        except Exception as e:
            self.conn.rollback()
            raise Exception(e)

    def find_one(self, table: str, filters: dict) -> dict:
        """find one row by query.
        :param table: the table name or entity class
        :param filters: the query conditions
        """
        if not filters:
            return
        where = " AND ".join(f"{i}=%s" for i in filters.keys())
        if where:
            where = "WHERE " + where
        sql = f"SELECT * FROM {table} {where}"
        params = tuple(filters.values())
        return self.execute_fetchone(sql, params)

    def find(self, table: str, filters: dict) -> list[dict]:
        """find one row by query.
        :param table: the table name or entity class
        :param filters: the query conditions
        """
        if not filters:
            return
        where = " AND ".join(f"{i}=%s" for i in filters.keys())
        if where:
            where = "WHERE " + where
        sql = f"SELECT * FROM {table} {where}"
        params = tuple(filters.values())
        return self.execute(sql, params)

    def insert(self, table: str, data: dict):
        """
        插入单条数据
        """
        columns = ", ".join(data.keys())
        values_template = ", ".join(["%s"] * len(data))
        sql = f"INSERT INTO {table} ({columns}) VALUES ({values_template})"
        params = tuple(data.values())
        return self.execute(sql, params, insert=True)

    def update(self, table: str, data: dict):
        """update one row by id.
        :param data: the data of row, allow types dict or object instant
        :param table: the table name or entity class
        :return: returns effective rows counts
        """
        id = data.pop("id", None)
        if id:
            fields = ", ".join(f"{i}=%s" for i in data.keys())
            sql = f"update {table} set {fields} where id=%s"
            params = tuple(data.values()) + (id,)
            self.execute(sql, params)

    def delete(self, table: str, filters: dict):
        """delete rows by id.
        :param table: the table name or entity class
        :param filters: the query conditions
        :return: returns effective rows counts
        """
        where = " AND ".join(f"{i}=%s" for i in filters.keys())
        if where:
            where = "WHERE " + where
        sql = f"DELETE FROM {table} {where}"
        params = tuple(filters.values())
        self.execute(sql, params)


connect = DB
