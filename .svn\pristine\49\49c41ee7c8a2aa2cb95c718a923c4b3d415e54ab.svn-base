import os
from datetime import date, datetime
from enum import Enum

import instructor
from openai import OpenAI
from pydantic import BaseModel, Field


class Result(BaseModel):
    result: str = Field(description="结果")
    thinking: str = Field(description="模型思考过程")


def call_llm(prompt):
    api_key = os.getenv("QWEN3_API_KEY")
    base_url = "https://llmgateway.deltaww.com/v1/"
    model = "openai/Qwen/Qwen3-235B-A22B-FP8"

    client = instructor.from_openai(
        OpenAI(api_key=api_key, base_url=base_url),
        mode=instructor.Mode.JSON,
    )
    r = client.chat.completions.create(
        model=model,
        response_model=Result,
        messages=[{"role": "user", "content": prompt}],
    )
    print(r)
    return r
