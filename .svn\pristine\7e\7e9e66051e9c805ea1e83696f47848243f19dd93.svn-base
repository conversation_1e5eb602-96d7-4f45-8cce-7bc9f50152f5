from datetime import datetime

import dash_bootstrap_components as dbc
import pandas as pd
from config import pool
from dash_extensions.enrich import (
    MATCH,
    Input,
    Output,
    State,
    callback,
    dash,
    html,
    no_update,
)
from tasks import bg_access_record, bg_label_print, bg_mail, bg_tools_short
import feffery_antd_components as fac
from common import read_sql

dash.register_page(__name__, title="杂项")


def new_children(df1, children, df2):
    i = 0
    for tup in df1.itertuples():
        num = getattr(tup, "数量")
        btn_content = "领取"
        card_color = "primary"
        disabled = False
        if num == 0:
            btn_content = "无库存"
            card_color = "dark"
            disabled = True
        if getattr(tup, "id") in df2["料号ID"].values:
            btn_content = "今日已领"
            disabled = True
        card_content = [
            dbc.CardImg(
                src="/assets/tools/{toolpn}.jpg".format(toolpn=getattr(tup, "料号")),
                top=True,
                className="card-filter",
                id={"type": "card-img", "index": i},
                alt="工具",
            ),
            dbc.CardBody(
                [
                    html.H4(
                        getattr(tup, "品名"),
                        className="card-title",
                        id={"type": "product-name", "index": i},
                    ),
                    dbc.Badge(
                        getattr(tup, "领用提示"), color="warning", className="mb-1 mr-1"
                    ),
                    dbc.Badge(
                        getattr(tup, "区域"), color="success", className="mb-1 mr-1"
                    ),
                    dbc.Badge(
                        getattr(tup, "部门专用"), color="danger", className="mb-1 mr-1"
                    ),
                    dbc.Badge(
                        getattr(tup, "数量"), color="info", className="mb-1 mr-1"
                    ),
                    html.P(
                        [
                            getattr(tup, "描述"),
                        ],
                        className="card-text",
                        style={"font-size": "15px"},
                    ),
                    dbc.Button(
                        btn_content,
                        outline=True,
                        color="light",
                        style={"width": "100%"},
                        id={"type": "btn-num", "index": i},
                        disabled=disabled,
                    ),
                    dbc.Modal(
                        [
                            dbc.ModalHeader(getattr(tup, "品名")),
                            dbc.ModalBody(
                                [
                                    html.P(
                                        getattr(tup, "id"),
                                        id={"type": "delta-pn", "index": i},
                                        style={"display": "none"},
                                    ),
                                    html.P(
                                        "当前库存余量：{leftnum}".format(
                                            leftnum=getattr(tup, "数量")
                                        ),
                                        id={"type": "left-a", "index": i},
                                    ),
                                    dbc.Input(
                                        placeholder="请输入领取数量",
                                        type="number",
                                        min=0,
                                        max=min(getattr(tup, "限领数量"), num),
                                        step=1,
                                        id={"type": "input-num", "index": i},
                                    ),
                                    dbc.Alert(
                                        "输入数量超过限制数量或库存",
                                        id={"type": "input-alert", "index": i},
                                        dismissable=True,
                                        is_open=False,
                                        color="warning",
                                    ),
                                ]
                            ),
                            dbc.ModalFooter(
                                dbc.InputGroup(
                                    [
                                        dbc.Button(
                                            btn_content,
                                            color="success",
                                            style={"width": "205px"},
                                            id={"type": "get-tool", "index": i},
                                        ),
                                        dbc.Button(
                                            "关闭",
                                            id={"type": "btn-close", "index": i},
                                            className="ml-auto",
                                            color="danger",
                                            style={"width": "205px"},
                                        ),
                                    ],
                                    className="mb-3",
                                ),
                            ),
                        ],
                        id={"type": "modal-num", "index": i},
                        centered=True,
                    ),
                    # dbc.Button(btn_content, outline=True,color="light",style={'width':'110px'}),
                ]
            ),
        ]
        # card_col=dbc.Card(card_content, color=card_color, inverse=True)
        # children.append(card_col)
        card_col = dbc.Col(
            dbc.Card(
                card_content,
                color=card_color,
                inverse=True,
                style={"height": "420px", "border": "none"},
            ),
            className="mt-3",
            lg=2,
            xs=6,
        )
        children.append(card_col)
        i = i + 1
    return children


layout = dbc.Container(
    [
        dbc.Tabs(
            [
                # dbc.Tab(label="全部", id='calog-1',tab_id="tool-tab-1",labelClassName='px-2 py-1'),
                dbc.Tab(label="焊接用品", id="calog-2", tab_id="tool-tab-2"),
                dbc.Tab(label="工具箱", id="calog-3", tab_id="tool-tab-3"),
                dbc.Tab(label="其他易耗品", id="calog-4", tab_id="tool-tab-4"),
                dbc.Tab(label="线材及端子", id="calog-5", tab_id="tool-tab-5"),
                dbc.Tab(label="仪器设备配件", id="calog-6", tab_id="tool-tab-6"),
                dbc.Tab(label="劳防用品", id="calog-7", tab_id="tool-tab-7"),
            ],
            id="tool-tabs",
            active_tab="tool-tab-2",
            style={"color": "#1abc9c"},
            className="mx-3",
        ),
        html.Div(dbc.Row([], id="tool-cardx"), className="ml-3 pr-3"),
    ],
    fluid=True,
)


@callback(
    Output("tool-cardx", "children"),
    Input("tool-tabs", "active_tab"),
    State("user", "data"),
    prevent_initial_call=False,
)
def calog1_child(a, data):
    c = []
    sql = "select * from ssp_asset.tool_list"
    # time='领用时间'
    sql1 = "select * from ssp_asset.tool_stockout where datediff(领用时间,now()) = 0"
    sql2 = "select id from ssp.dept where toolbox_permit=%s"
    params = ["Y"]
    df = read_sql(sql)
    df1 = read_sql(sql1)
    df2 = read_sql(sql2, params=params)

    area = data.get("area")
    dept = data.get("dept")
    dept_id = data.get("dept_id")
    owner = data.get("nt_name")

    df1 = df1[df1["领用人"].isin([owner])]
    if dept_id in df2["id"].values:
        df = df[df["区域"].isin([area])]
        df = df[(df["部门专用"].isin(["ALL"])) | (df["部门专用"].str.contains(dept))]
    else:
        df = df[df["部门专用"].str.contains(dept)]
    if df.empty:
        return fac.AntdEmpty(
            description="工具仅支持本地领用,如有特殊需求请联系:Yingzhi.Zhang 張鶯枝"
        )
    if a == "tool-tab-2":
        df = df[df["类别"].isin(["焊接用品"])]
    elif a == "tool-tab-3":
        df = df[df["类别"].isin(["工具箱"])]
    elif a == "tool-tab-4":
        df = df[df["类别"].isin(["其他易耗品"])]
    elif a == "tool-tab-5":
        df = df[df["类别"].isin(["线材及端子"])]
    elif a == "tool-tab-6":
        df = df[df["类别"].isin(["仪器设备配件"])]
    elif a == "tool-tab-7":
        df = df[df["类别"].isin(["劳防用品"])]
    else:
        df = df[df["类别"].isin(["焊接用品"])]
    child = new_children(df, c, df1)
    return child


@callback(
    Output({"type": "modal-num", "index": MATCH}, "is_open"),
    [
        Input({"type": "btn-num", "index": MATCH}, "n_clicks"),
        Input({"type": "btn-close", "index": MATCH}, "n_clicks"),
    ],
    [State({"type": "modal-num", "index": MATCH}, "is_open")],
)
def toggle_modal(n1, n2, is_open):
    if n1 or n2:
        return not is_open
    return is_open


@callback(
    [
        Output({"type": "input-alert", "index": MATCH}, "is_open"),
        Output({"type": "input-alert", "index": MATCH}, "children"),
        Output({"type": "input-alert", "index": MATCH}, "color"),
        Output({"type": "btn-num", "index": MATCH}, "children"),
        Output({"type": "btn-num", "index": MATCH}, "disabled"),
        Output({"type": "get-tool", "index": MATCH}, "disabled"),
    ],
    [Input({"type": "get-tool", "index": MATCH}, "n_clicks")],
    [
        State({"type": "input-num", "index": MATCH}, "value"),
        State({"type": "input-num", "index": MATCH}, "max"),
        State({"type": "delta-pn", "index": MATCH}, "children"),
        State({"type": "product-name", "index": MATCH}, "children"),
        State("user", "data"),
    ],
    prevent_initial_call=False,
)
def stock_out(n, value, maxn, pn, product, data):
    if value:
        if value > maxn:
            return (
                True,
                "输入数量超过限制数量或库存",
                "warning",
                no_update,
                no_update,
                False,
            )
        else:
            conn = pool.connection()
            cu = conn.cursor()
            owner = data.get("nt_name")
            dept = data.get("dept")
            area = data.get("area")
            deltapn = pn
            num = value
            now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            # keys = ['领用人','部门','区域','料号ID','领用数量','领用时间']
            params = [owner, dept, area, deltapn, num, now]
            sql = "insert into ssp_asset.tool_stockout\
            (领用人,部门,区域,料号ID,领用数量,领用时间) values(%s,%s,%s,%s,%s,%s)"
            cu.execute(sql, params)
            # qty='数量'
            sql1 = "update ssp_asset.tool_list set 数量=数量-%s where id=%s"
            params1 = [num, deltapn]
            cu.execute(sql1, params1)
            sql2 = "select * from ssp_asset.tool_list where (id=%s)"
            params2 = [deltapn]
            df = read_sql(sql2, params=params2)
            conn.commit()
            cu.close()
            conn.close()
            left_qty = df.loc[0, "数量"]
            tool_des = df.loc[0, "描述"]
            # ---------email--------------
            if area == "HZ":
                mailto = "<EMAIL>"
                location = "请至2楼样制间领取"

            elif area == "SH":
                mailto = "<EMAIL>"
                location = "请至3楼材料间领取"

            elif area == "WH":
                mailto = "<EMAIL>;<EMAIL>"
                location = "请至1楼样制间领取"

            df1 = pd.DataFrame(
                [
                    {
                        "品名": product,
                        "描述": tool_des,
                        "领用数量": num,
                        "领用人": owner,
                        "部门": dept,
                        "领用时间": now,
                        "库存余量": left_qty,
                    }
                ]
            )
            df1 = df1.reindex(
                columns=[
                    "品名",
                    "描述",
                    "领用数量",
                    "领用人",
                    "部门",
                    "领用时间",
                    "库存余量",
                ]
            )
            body = df1.to_html()
            bg_mail(
                mailto + f";{owner}@deltaww.com",
                f"工具领用:{owner}-{product}-{num}",
                body,
            )

            df2 = pd.DataFrame(
                [
                    {
                        "dept": dept,
                        "owner1": owner,
                        "checkcode": product,
                        "des": tool_des,
                        "qty": num,
                        "area": area,
                        "label_template": "picking",
                    }
                ]
            )
            # bg_label_print(area, df2)
            bg_label_print(df2.to_json(orient="records"))
            # --------补货-----------
            mailto1 = "<EMAIL>;<EMAIL>"
            bg_tools_short(deltapn, area, mailto1)
            bg_access_record(data, "工具领用", "提交", product)

            return True, f"领取成功,{location}", "success", "今日已领", True, True
    else:
        return False, "error", "warning", no_update, no_update, False
