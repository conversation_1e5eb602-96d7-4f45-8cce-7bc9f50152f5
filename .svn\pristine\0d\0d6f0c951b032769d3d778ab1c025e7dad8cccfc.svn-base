from pocketflow import Flow, Node

from ..utils import call_llm


class QueryNode(Node):
    def prep(self, shared):
        return shared["prompt"]

    def exec(self, prompt):
        response = call_llm(prompt)
        return response

    def post(self, shared, prep_res, exec_res):
        shared["result"] = exec_res


def part_query(shared: dict):
    plan_node = QueryNode()
    flow = Flow(start=plan_node)
    flow.run(shared)
    return shared
