# -*- coding: utf-8 -*-
import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
import feffery_antd_components as fac
from dash import dash_table, dcc

from common import id_factory
from components import CeCancelAIO, CeRejectAIO

# from dbtool import db
from utils import db

id = id_factory(__name__)


tab_add_row = dbc.<PERSON><PERSON>(
    className="fa fa-plus",
    size="sm",
    color="light",
    id="tab_add_row",
)


def task_information(task):
    dept = db.find_one("ssp.dept", {"id": task.get("dept_id")})
    settings = db.find_one("ce.settings", {"dept_id": task.get("dept_id")})
    return fac.AntdDescriptions(
        [
            fac.AntdDescriptionItem(task.get("dept"), label="部门"),
            fac.AntdDescriptionItem(task.get("applicant"), label="申请人"),
            fac.AntdDescriptionItem(task.get("type"), label="类型"),
            fac.AntdDescriptionItem(task.get("status"), label="状态"),
            fac.AntdDescriptionItem(dept.get("product_code"), label="产品代码"),
            fac.AntdDescriptionItem(settings.get("project_name"), label="项目名称"),
        ],
        labelStyle={"fontWeight": "bold"},
    )


columns = [
    {"name": "厂商代码", "id": "mfg_code", "presentation": "input"},
    {"name": "新台达料号", "id": "new_deltapn", "presentation": "input"},
    {"name": "新描述", "id": "new_des", "presentation": "input"},
    {"name": "新厂商", "id": "new_mfgname", "presentation": "input"},
    {"name": "新厂商型号", "id": "new_mfgpn", "presentation": "input"},
    {"name": "CE备注", "id": "ce_remark", "presentation": "input"},
]


def table(data):
    return dash_table.DataTable(
        data=data,
        columns=columns,
        editable=True,
        row_deletable=True,
        is_focused=True,
        id=id("table"),
        style_cell={
            "whiteSpace": "normal",
            "height": "auto",
            "textAlign": "left",
            "font-family": "Helvetica",
            "font-size": "10px",
        },
        css=[{"selector": ".dash-spreadsheet-menu-item", "rule": "display:none"}],
    )


title = dmc.Center(dmc.Text("IC+软体组合件申请单",fw=700))


def form_part_1(pn):
    form_part_1 = dmc.Stack(
        [
            dmc.Group([
                dmc.TextInput(
                    label="台达料号",
                    size="xs",
                    withAsterisk=True,
                    id=id("deltapn"),
                    value=pn.get("deltapn"),
                ),
                dmc.TextInput(
                    label="描述",
                    size="xs",
                    id=id("des"),
                    value=pn.get("des"),
                ),
                dmc.TextInput(
                    label="厂商",
                    size="xs",
                    id=id("mfgname"),
                    value=pn.get("mfgname"),
                ),
                dmc.TextInput(
                    label="厂商料号",
                    size="xs",
                    id=id("mfgpn"),
                    value=pn.get("mfgpn"),
                ),
                dmc.Select(
                    label="材料类别1",
                    placeholder="Select one",
                    size="xs",
                    withAsterisk=True,
                    id=id("cat1"),
                    data=[
                        "Active",
                        "Passive",
                        "Magnetic",
                        "Electro-Mechanical",
                        "Mechanical",
                        "Other",
                    ],
                    value=pn.get("cat1"),
                ),
                dmc.Select(
                    label="材料类别2",
                    placeholder="Select one",
                    size="xs",
                    withAsterisk=True,
                    id=id("cat2"),
                    value=pn.get("cat2"),
                    data=[pn.get("cat2")] if pn.get("cat2") else [],
                ),
                dmc.Select(
                    label="材料类别3",
                    placeholder="Select one",
                    size="xs",
                    withAsterisk=True,
                    id=id("cat3"),
                    value=pn.get("cat3"),
                    data=[pn.get("cat3")] if pn.get("cat3") else [],
                ),
            ],gap=0,align="end",
            grow=True,),
        ]
    )
    return form_part_1


def form_part_2(pn):
    form_part_2 = dmc.Group(
        [
            dmc.Anchor(
                "申请表附件",
                href=f"/upload/{pn.get('application_attachment')}",
                target="_blank",
                variant="link",
            ),
            dmc.Anchor(
                "台达软体管控表单附件",
                href=f"/upload/{pn.get('controlling_attachment')}",
                target="_blank",
                variant="link",
            ),
        ]
    )
    return form_part_2


# submit = dmc.Button("提交", id=id("submit"))
download = dmc.Group([
    dmc.Button(
        "下载",
        variant="subtle",
        color="orange",
        size="xs",
        id=id("download-btn"),
    ),
    dcc.Download(id=id("download")),
],justify="right")


def layout(**query):
    task_id = query.get("task")

    task = db.find_one("ce.task", {"id": task_id})
    pn = db.find_one("ce.pn_sw", {"task_id": task_id})
    layout = dmc.Container(
        dmc.Stack(
            [
                download,
                title,
                dmc.Divider(),
                task_information(task),
                form_part_1(pn),
                form_part_2(pn),
                table([pn]),
                dmc.Group(
                    [
                        dmc.Button("提交", id=id("submit")),
                        CeRejectAIO(__name__),
                        CeCancelAIO(__name__),
                    ],
                    grow=True,
                ),
            ]
        )
    )
    return layout
