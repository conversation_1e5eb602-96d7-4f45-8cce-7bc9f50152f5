import os
from datetime import date, datetime
from enum import Enum

import instructor
from openai import OpenAI
from pydantic import BaseModel, Field


# class Result(BaseModel):
#     result: str = Field(description="结果")
#     thinking: str = Field(description="模型思考过程")


class MaterialInfo1(BaseModel):
    type: str = Field(description="材料类型")
    power: float = Field(description="功率,单位转换成W")
    voltage: float = Field(description="标称电压,单位转换成V")
    min_voltage: float = Field(description="电压下限,单位转换成V")
    max_voltage: float = Field(description="电压上限,单位转换成V")
    mounting: str = Field(description="安装方式(SMD或DIP)")
    package: str = Field(description="封装")
    resistance: float = Field(description="阻值,单位转换成欧姆")


class MaterialInfo(BaseModel):
    result: str = Field(description="材料信息")


def call_llm(prompt):
    api_key = os.getenv("QWEN3_API_KEY")
    base_url = "https://llmgateway.deltaww.com/v1/"
    model = "openai/Qwen/Qwen3-235B-A22B-FP8"

    # api_key = os.getenv("OPENROUTER_API_KEY")
    # base_url = "https://openrouter.ai/api/v1"
    # model = "google/gemini-2.5-flash-preview-05-20"

    client = instructor.from_openai(
        OpenAI(api_key=api_key, base_url=base_url),
        mode=instructor.Mode.JSON,
    )
    r = client.chat.completions.create(
        model=model,
        response_model=list[MaterialInfo],
        messages=[{"role": "user", "content": prompt}],
    )
    return r
