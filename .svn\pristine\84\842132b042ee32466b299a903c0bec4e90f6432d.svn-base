from components import create_sidebar

menu_items = [
    {
        "label": "样制",
        "icon": "material-symbols:home",
        "href": "?page=0",
        "font-weight": "bolder",
        "color": "rgb(0, 159, 232)",
        "page": "0",
    },
    {
        "label": "产能评估",
        "icon": "material-symbols:chart-data",
        "href": "?page=1",
        "page": "1",
    },
    {
        "label": "流程时间",
        "icon": "ic:outline-access-time",
        "href": "?page=2",
        "page": "2",
    },
    {
        "label": "生产数据",
        "icon": "material-symbols:database",
        "href": "?page=3",
        "page": "3",
    },
    {
        "label": "样制信息",
        "icon": "material-symbols:data-info-alert",
        "href": "?page=4",
        "page": "4",
    },
    {
        "label": "材料分类",
        "icon": "mingcute:classify-fill",
        "href": "?page=5",
        "page": "5",
    },
    {
        "label": "样制BOM",
        "icon": "material-symbols:format-list-bulleted",
        "href": "?page=6",
        "page": "6",
    },
    {
        "key": "5",
        "title": "项目管理",
        "label": "项目管理",
        "icon": "mdi:account-eye",
        "href": "?page=project",
        "page": "project",
    },
    {
        "label": "贴片计划",
        "icon": "icon-park-outline:plan",
        "href": "?page=smd",
        "page": "smd",
    },
    {
        "label": "插件计划",
        "icon": "icon-park-outline:plan",
        "href": "?page=dip",
        "page": "dip",
    },
]


def sidebar(page):
    return create_sidebar(page, menu_items)
