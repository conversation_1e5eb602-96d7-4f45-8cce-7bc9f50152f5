# -*- coding: utf-8 -*-
import base64
import io
import math
from datetime import datetime, timedelta

import dash_mantine_components as dmc
import numpy as np
import pandas as pd
import plotly.express as px
import polars as pl
from dash import Patch, ctx
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    Input,
    Output,
    Serverside,
    State,
    callback,
    dash,
    dcc,
    no_update,
)
from dash_extensions.javascript import Namespace
from sqlalchemy import and_, or_, select, update
from sqlalchemy.sql import func

from common import (
    df_insert,
    df_to_html,
    get_nt_name,
    get_temp_pn,
    mat_category,
    read_db,
    read_sql,
    safety_stock_handler,
)
from components import notice
from config import SSP_DIR, cache, db, pool
from db.ssp_ce import AMatCatalogue
from db.ssp_model import (
    Dept,
    Pur,
    PurPlant,
    Safetystock,
    User,
    Vendor,
)
from tasks import bg_label_print, bg_mail, task_pur_update

from . import layout

layout = layout.layout
dash.register_page(__name__, path="/pur", title="采购")
ns = Namespace("myNamespace", "tabulator")

project_application = {
    "NBE_HVG": "医疗电源",
    "NBE_IPS": "医疗电源",
    "ADP_ADP": "便携式电源",
    "DCBU_DCBU": "DC-DC",
    "AMP_AMP": "OBCM",
    "ATI_ATI": "OBCM",
    "APE_APE": "OBCM",
    "IDC_IDC": "网络电源",
    "DES_IMBU": "网络电源",
    "DES_CDBU": "网络电源",
    "SPA_PVIBU": "PV inverter",
    "SPA_EVCSBU": "PV inverter",
    "RTP_PCSBD": "储能",
    "LGT_LGT": "照明",
}

product_place = {
    "NBE_HVG": "吴江",
    "NBE_IPS": "吴江",
    "ADP_ADP": "吴江",
    "AMP_AMP": "吴江",
    "ATI_ATI": "吴江",
    "IDC_IDC": "吴江",
    "SPA_PVIBU": "吴江",
    "SPA_EVCSBU": "吴江",
    "APE_APE": "泰国",
    "DCBU_DCBU": "泰国",
    "DES_IMBU": "泰国",
    "DES_CDBU": "泰国",
    "LGT_LGT": "芜湖",
    "RTP_PCSBD": "吴江",
}


# =======新增模块=================
@callback(
    Output("pur_home_line", "data"),
    Output("pur_home_line", "xField"),
    Input("pur_home_segment", "value"),
)
def pur_home_segment(value):
    sql = "select gmt_create,rate from pur_etd_rate"
    df = read_db(sql).with_columns(
        day=pl.col("gmt_create").dt.date(),
        week=pl.col("gmt_create").dt.week(),
        rate=pl.col("rate").cast(pl.Float64),
    )
    if value == "day":
        df = df.with_columns(pl.col("day").dt.strftime("%m-%d")).sort(by="day")
        return df.to_dicts(), value
    elif value == "week":
        df = (
            df.group_by(["week"])
            .agg(pl.col("rate").mean())
            .with_columns(pl.col("week").cast(pl.String))
            .sort(by="week")
        )
        return df.to_dicts(), value
    else:
        raise PreventUpdate


@callback(
    Output("pur_home_card", "visible"),
    Output("pur_home_table", "rowData"),
    Input("pur_home_bar", "recentlyBarClickRecord"),
    State("pur_home_store", "data"),
)
def pur_pop_card(data, store):
    if not data:
        raise PreventUpdate
    pur = data.get("data").get("pur")
    store = [i for i in store if i["pur"] == pur]
    return True, store


@callback(
    Output("tab1_table", "data"),
    Input("tab1_add_row", "n_clicks"),
)
def tab1_table_add_row(n_clicks):
    """create_meeting_table插入行"""
    if not n_clicks:
        raise PreventUpdate
    patch = Patch()
    patch.append({})
    return patch


@callback(
    Output("tab1_table", "dropdown_conditional"),
    Input("tab1_table", "id"),
    prevent_initial_call=False,
)
def initial_tab1_table_columns(id):
    """初始化采购新增表格列下拉菜单"""
    sql = "select dept_group,dept_name,id from dept"
    depts = read_sql(sql)

    sql = "select nt_name,dept,dept_id from user where termdate is null"
    users = read_sql(sql)
    users["nt_name"] = users["nt_name"].str.title()
    users["dept_id"] = users["dept_id"].astype(str)

    sql = "select distinct category_1 as cat1,pur_category2 as cat2 \
        from ssp_ce.a_mat_catalogue"
    cats = read_sql(sql)

    dc_dept = [
        {
            "if": {"column_id": "dept"},
            "options": [
                {
                    "label": f"{i.dept_group}_{i.dept_name}",
                    "value": f"{i.dept_group}_{i.dept_name}",
                }
                for i in depts.itertuples()
            ],
        },
    ]

    dc_rd = [
        {
            "if": {
                "column_id": "rd",
                "filter_query": f"{{dept}} eq {f'{x.dept_group}_{x.dept_name}'}",
            },
            "options": [
                {"label": i, "value": i}
                for i in users.query(f'dept_id=="{x.id}"').nt_name.tolist()
            ],
        }
        for x in depts.itertuples()
    ]

    dc_cat1 = [
        {
            "if": {"column_id": "mat_catelogue"},
            "options": [{"label": i, "value": i} for i in cats["cat1"].unique()],
        },
    ]

    dc_cat2 = [
        {
            "if": {
                "column_id": "mat_group",
                "filter_query": f"{{mat_catelogue}} eq {x}",
            },
            "options": [
                {"label": i, "value": i}
                for i in cats.query(f'cat1=="{x}"').cat2.unique()
            ],
        }
        for x in cats["cat1"].unique()
    ]
    dropdown_conditional = dc_dept + dc_rd + dc_cat1 + dc_cat2
    return dropdown_conditional


@callback(
    Output("tab1_table", "data"),
    Input("tab1_table", "data_timestamp"),
    State("tab1_table", "data"),
    State("tab1_table", "active_cell"),
    State("tab1_table", "columns"),
)
def completion_information(new_data, data, ac, columns):
    """根据料号或厂商料号补全信息"""
    if not (new_data and ac):
        raise PreventUpdate

    column_id = ac.get("column_id")
    if column_id not in ("deltapn", "mfgpn"):
        raise PreventUpdate

    df = pd.DataFrame(data)
    df = df.reindex(columns=[i["id"] for i in columns])
    df = df.fillna("")
    df["deltapn"] = df["deltapn"].astype(str).str.strip()
    df["mfgpn"] = df["mfgpn"].astype(str).str.strip()
    df["checkcode"] = df["deltapn"]
    df = df.replace({"": None}).replace({np.nan: None})
    df = df.reset_index()
    dfx = df.loc[df["in_db"] != 1]

    if not dfx.empty:
        if column_id == "deltapn":
            params = dfx["deltapn"].dropna().tolist()
            sql = "select distinct deltapn,des,mfgname,mfgpn,checkcode \
                from ssp_csg.mat_info where deltapn in %s"

        elif column_id == "mfgpn":
            params = dfx["mfgpn"].dropna().tolist()
            sql = "select distinct deltapn,des,mfgname,mfgpn,checkcode \
                from ssp_csg.mat_info where mfgpn in %s"

        dfx1 = read_sql(sql, params=[params])

        if not dfx1.empty:
            dfx1 = mat_category(dfx1)
            dfx1 = dfx1.drop_duplicates(column_id)
            dfx1["in_db"] = 1

            dfx[column_id] = dfx[column_id].str.upper()
            dfx1[column_id] = dfx1[column_id].str.upper()
            dfx = dfx.merge(dfx1, on=column_id, how="left", suffixes=["", "_y"])
            cols = dfx1.columns.difference([column_id]).tolist()
            for i in cols:
                dfx[i] = dfx[i].combine_first(dfx[f"{i}_y"])
            df = df.set_index("index")
            df.update(dfx.set_index("index"))
            return df.to_dict(orient="records")
        else:
            raise PreventUpdate
    else:
        raise PreventUpdate


@callback(
    Output("tab1_notice", "is_open"),
    Output("tab1_notice", "children"),
    Output("tab1_notice", "color"),
    Input("tab1_print_btn", "n_clicks"),
    State("tab1_table", "data"),
    State("user", "data"),
)
def tab1_print_label(n_clicks, data, user):
    """打印条码"""
    if not n_clicks:
        raise PreventUpdate

    df = pd.DataFrame(data)
    columns_needed = ["deltapn", "mfgname"]
    dfx = df.reindex(columns=columns_needed).fillna("")

    if (dfx == "").any().any():
        return True, "台达料号,厂商不能为空", "danger"

    nt_name = user.get("nt_name").lower()
    area = user.get("area").upper()

    if "qty" in df.columns:
        qty = df["qty"].astype(str)
    else:
        qty = ""

    df["station"] = nt_name
    df["label_template"] = "stock_out"
    df["id"] = df["deltapn"] + "{" + qty + "{Pur"
    df["area"] = area
    df["owner"] = nt_name
    df["des"] = df["mfgpn"]
    df["stockno"] = df["rd"]
    bg_label_print(df.to_json(orient="records"))
    return True, "打印提交成功", "success"


@callback(
    Output("tab1_notice", "is_open"),
    Output("tab1_notice", "children"),
    Output("tab1_notice", "color"),
    Input("tab1_submit_btn", "n_clicks"),
    State("tab1_table", "data"),
    State("tab1_table", "columns"),
    State("user", "data"),
)
def submit_add_new(n_clicks, data, columns, user):
    """提交新记录"""
    if not n_clicks:
        raise PreventUpdate

    df = pd.DataFrame(data)
    if df.empty:
        return True, "请填写完整相关栏位信息", "danger"

    columns_needed = [
        "deltapn",
        "mfgname",
        "qty",
        "req_date",
        "dept",
        "rd",
        "mat_catelogue",
        "mat_group",
    ]
    if (df.reindex(columns=columns_needed).fillna("") == "").any().any():
        return True, "请填写完整相关栏位信息", "danger"
    df = df.reindex(columns=[i["id"] for i in columns])
    df["checkcode"] = np.where(df["checkcode"].isna(), df["deltapn"], df["checkcode"])

    mfgname = {i.lower().strip() for i in df["mfgname"].unique()}
    sql = "select vendor_name from ssp.vendor where vendor_name in %s"
    params = [list(mfgname)]
    vendor = read_sql(sql, params=params)

    mfgname_db = set(vendor["vendor_name"].str.lower().str.strip().unique())
    mfgname_set = mfgname - mfgname_db
    if mfgname_set:
        return True, f"{','.join(mfgname_set)}厂商不存在", "danger"

    nt_name = user.get("nt_name").lower()
    sql = "select nt_name as rd,dept_id,dept as dept_y,area as dest_area\
        from ssp.user where nt_name in %s"
    params = [df["rd"].unique().tolist()]
    users = read_sql(sql, params=params)
    users["rd"] = users["rd"].str.title()

    df = df.merge(users, on="rd", how="left")
    df["application"] = "Debug"
    df["pur_status"] = "pending"
    df["start_date"] = datetime.now()
    df["item_pur"] = int(f"{datetime.now():%y%m%d%H%M%S%f}")
    df["item_pur"] = df["item_pur"] + range(df.shape[0])
    df["pur"] = nt_name
    df["dept"] = np.where(df["dept"] != df["dept_y"], df["dept_y"], df["dept"])
    df["r_qty"] = df["qty"]

    # *---------台达料号输入addce时，编写临时料号-----------------
    dfce = df.loc[df["deltapn"].str.lower().str.startswith("addce")]
    if not dfce.empty:
        dfce["des"] = dfce["des"].fillna("")
        dfce["mfgpn"] = dfce["mfgpn"].fillna("").astype(str).str.strip()
        dfce["des"] = np.where(
            dfce["des"] == "",
            dfce["mat_catelogue"] + " " + dfce["mat_group"],
            dfce["des"],
        )
        dept_id = dfce["dept_id"].unique()[0]
        dfce = get_temp_pn(dept_id, dfce)
        for i in dfce.itertuples():
            df.loc[i.Index, "deltapn"] = i.temp_pn
            df.loc[i.Index, "checkcode"] = i.temp_pn
            df.loc[i.Index, "des"] = i.des

    df_insert("ssp.pur", df)

    # ======修改记录======
    df["up_type"] = "PR_Create"
    df["remark1"] = df["deltapn"]
    task_pur_update(df, nt_name)

    return True, "提交成功", "success"


@callback(
    Output("tab1_table", "data"),
    Input("tab1_upload", "contents"),
    State("tab1_table", "columns"),
)
def upload_new_data(content, columns):
    if not content:
        raise PreventUpdate

    content_type, content_string = content.split(",")
    decoded = base64.b64decode(content_string)
    df = pd.read_excel(io.BytesIO(decoded), dtype={"台达料号": str})
    col = {i["name"]: i["id"] for i in columns}
    df = df.rename(columns=col)
    df = df.fillna("")
    df["deltapn"] = df["deltapn"].astype(str).str.upper()
    df["mfgpn"] = df["mfgpn"].astype(str).str.upper()
    df["rd"] = df["rd"].astype(str).str.strip().str.title()
    df["checkcode"] = df["deltapn"]
    df["in_db"] = None

    params1 = [i for i in df["deltapn"].unique() if i]
    params2 = [i for i in df["mfgpn"].unique() if i]
    params3 = [i for i in df["rd"].unique() if i]
    cond = []
    params = []

    if params1:
        cond.append("deltapn in %s")
        params.append(params1)

    if params2:
        cond.append("mfgpn in %s")
        params.append(params2)

    if params3:
        sql = "select nt_name as rd,(select concat_ws('_',dept_group,dept_name) \
        from dept where id=dept_id) as dept from ssp.user where nt_name in %s"
        user = read_sql(sql, params=[params3])
        user["rd"] = user["rd"].astype(str).str.title()

    cond = " or ".join(cond)
    sql = f"select distinct deltapn,des,mfgname,mfgpn,checkcode \
    from ssp_csg.mat_info where {cond}"
    df1 = read_sql(sql, params=params)

    for i in df.itertuples():
        df1["deltapn"] = df1["deltapn"].str.upper()
        df1["mfgpn"] = df1["mfgpn"].str.upper()
        df1 = df1.drop_duplicates("deltapn").drop_duplicates("mfgpn")
        df1 = mat_category(df1)
        df1["in_db"] = 1

        if not df1.empty:
            if i.deltapn:
                res = df1.loc[df1["deltapn"] == i.deltapn]
            elif i.mfgpn:
                res = df1.loc[df1["mfgpn"] == i.mfgpn]
            else:
                res = pd.DataFrame()
            if not res.empty:
                df["deltapn"][i.Index] = res["deltapn"].iloc[0]
                df["des"][i.Index] = res["des"].iloc[0]
                df["mfgname"][i.Index] = res["mfgname"].iloc[0]
                df["mfgpn"][i.Index] = res["mfgpn"].iloc[0]
                df["checkcode"][i.Index] = res["checkcode"].iloc[0]
                df["mat_catelogue"][i.Index] = res["mat_catelogue"].iloc[0]
                df["mat_group"][i.Index] = res["mat_group"].iloc[0]
                df["in_db"][i.Index] = 1

        if not user.empty:
            dfu = user.loc[user["rd"] == i.rd]
            if not dfu.empty:
                df["dept"][i.Index] = dfu["dept"].iloc[0]

    return df.to_dict(orient="records")


@callback(
    Output("tab1_table", "data"),
    Input("tab1_notice", "color"),
)
def reflesh_tab1_table(color):
    if color != "success":
        raise PreventUpdate
    return []


@callback(
    Output("tab2_table", "data"),
    Input("tab2_dro_1", "value"),
    # prevent_initial_call=False,
)
def load_pur_data(owner: str):
    """加载处理数据"""
    if not owner:
        raise PreventUpdate
    if owner == "ALL":
        sql = "select * from ssp.pur \
        where (pur_status in (%s,%s,%s) or (pur_status=%s and application in (%s,%s)))"
        params = [
            "wait for material",
            "pending",
            "received",
            "transferred",
            "project",
            "debug",
        ]
    else:
        sql = "select * from ssp.pur where pur=%s and \
            (pur_status in (%s,%s,%s) or (pur_status=%s and application in (%s,%s)))"
        params = [
            owner,
            "wait for material",
            "pending",
            "received",
            "transferred",
            "project",
            "debug",
        ]
    df = read_sql(sql, params=params)
    if df.empty:
        return []

    df["mfgname"] = df["mfgname"].str.upper()
    sql = "select distinct vendor_name as mfgname from ssp.vendor"
    vendor = read_sql(sql)
    vendor["vendor_exists"] = 1
    vendor = vendor["mfgname"].str.upper()
    df["vendor_exists"] = df["mfgname"].isin(vendor)

    prtno = df["prtno"].dropna().unique().tolist()
    if prtno:
        ph = ",".join(["%s"] * len(prtno))
        sql = f"select distinct prtno,checkcode,packaging as smd_dip \
        from ssp.smbom where prtno in ({ph})"
        df1 = read_sql(sql, params=prtno)
        df1 = df1.drop_duplicates(["prtno", "checkcode"])

        if not df1.empty:
            df = df.merge(df1, on=["prtno", "checkcode"], how="left")

        sql = f"select distinct prtno,pcbstatus as pcb_date \
        from ssp.prt where prtno in ({ph})"
        prt = read_sql(sql, params=prtno)
        if not prt.empty:
            df = df.merge(prt, on="prtno", how="left")
            df["pcb_date"] = df["pcb_date"].str.partition()[0].tolist()

    df["pur_status"] = df["pur_status"].str.lower()
    df["status"] = df["pur_status"]
    df["es_date"] = pd.to_datetime(df["es_date"], errors="coerce")
    today = datetime.now().date()

    for i in df.itertuples():
        if i.status == "wait for material":
            if (i.es_date is pd.NaT) or (not i.es_date):
                df["status"][i.Index] = "待交期"
            else:
                if i.es_date > i.req_date:
                    df["status"][i.Index] = "待追踪"
                if i.es_date.date() < today:
                    df["status"][i.Index] = "逾期提醒"
                if i.es_date.date() == today:
                    df["status"][i.Index] = "到货提醒"
        elif i.status == "pending":
            df["status"][i.Index] = "待处理"
            # df.loc[i.Index, "status"] = "待处理"
        elif i.status == "transferred":
            df["status"][i.Index] = "调料中"
            # df.loc[i.Index, "status"] = "调料中"
        elif i.status == "received":
            df["status"][i.Index] = "待验收"
            # df.loc[i.Index, "status"] = "待验收"

    df["status"] = np.where(df["status"] == "wait for material", "发货中", df["status"])
    c1 = df["price"].fillna(0) > 0
    c2 = df["pr_no"].isna()
    df["status"] = np.where(c1 & c2, "待下单", df["status"])

    sql = "SELECT checkcode,SUM(IF(area= 'SH',qty,0)) AS 'sh_stock',\
    SUM(IF(area= 'HZ',qty,0)) AS 'hz_stock' FROM stock GROUP BY checkcode"
    stock = read_sql(sql)
    df = df.merge(stock, on="checkcode", how="left")
    df["total_price"] = (df["price"].fillna(0) * df["r_qty"].fillna(0)).round(2)
    df["received_qty"] = None  # 到货数量清零，按照key in的数量
    return df.to_dict(orient="records")


@callback(
    Output("tab2_table", "data"),
    Input("tab2_status", "value"),
    State("tab2_table", "data"),
)
def pur_data_filter_by_status(status: str, data):
    if not status:
        raise PreventUpdate
    df = pd.DataFrame(data)
    if df.empty:
        raise PreventUpdate
    if status in ["pending", "received", "confirm_etd"]:
        df = df[df["pur_status"] == status]
    elif status == "over_demand":
        df = df[df["pur_status"] == "over_demand"]
    elif status == "transferred":
        df = df[df["pur_status"] == "transferred"]
    return df.to_dict(orient="records")


@callback(
    Output("tab2_notice", "is_open"),
    Output("tab2_notice", "children"),
    Output("tab2_notice", "color"),
    Input("tab2_update_btn", "n_clicks"),
    State("tab2_table", "multiRowsClicked"),
    State("tab2_table", "dataFiltered"),
    State("user", "data"),
)
def update_pur_data(n_clicks, data, data_filtered, user):
    """采购处理更新按钮"""
    if not n_clicks:
        raise PreventUpdate
    if not data:
        return True, "请先选择需更新的记录", "danger"

    now = datetime.now()
    df = pd.DataFrame(data)
    dff = pd.DataFrame(i for i in data_filtered["rows"] if i)
    df = df.loc[df["item_pur"].isin(dff["item_pur"])]

    df["es_date"] = df["es_date"].replace({"Invalid date": None})
    df["received_qty"] = pd.to_numeric(df["received_qty"], errors="coerce")
    df["r_qty"] = pd.to_numeric(df["r_qty"], errors="coerce")
    df["price"] = df["price"].fillna("")
    # df["pur_status"] = np.where(
    #     df["pur_status"].isin(["over_demand", "confirm_etd"]),
    #     "wait for material",
    #     df["pur_status"],
    # )

    c1 = df["received_qty"] == -1
    c2 = df["pur_remark"].fillna("") == ""
    if not df.loc[c1 & c2].empty:
        return True, "采购备注不能为空", "danger"

    c1 = df["plant"].fillna("") != ""  # ! 是否需要与数据库对比，待确认
    c2 = ~df["pur_status"].isin(["received", "closed"])
    df["pur_status"] = np.where(c1 & c2, "transferred", df["pur_status"])

    c1 = df["received_qty"] >= df["r_qty"]
    c2 = df["price"] != ""
    c3 = df["price"] == ""
    c4 = df["application"].str.lower() != "project"
    df["pur_status"] = np.where(c1 & c2 & c4, "received", df["pur_status"])
    df["pur_status"] = np.where(c1 & c3 & c4, "closed", df["pur_status"])

    c1 = df["received_qty"] == -1
    c2 = df["pur_remark"].fillna("") != ""
    df["pur_status"] = np.where(c1 & c2, "closed", df["pur_status"])

    if "signed" in df.columns:
        df["pur_status"] = np.where(df["signed"] == "Y", "closed", df["pur_status"])

    df["mat_receiveddate"] = np.where(
        df["pur_status"].isin(["received", "closed"]), now, ""
    )
    nt_name = user.get("nt_name")
    area = user.get("area")
    df = df.replace({"": None}).replace({np.nan: None})
    df["es_date_previous"] = df["es_date"]
    status = {
        "pending": 0,
        "wait for material": 1,
        "transferred": 1,
        "received": 2,
        "closed": 3,
    }

    with db.Session() as s:
        for i in df.itertuples():
            pur = s.get(Pur, i.id)
            if (not pur.es_date) and i.es_date:
                pur.es_maint_date = now
                up_type = "ETD_Update"
            else:
                up_type = "Record_Update"
            df["es_date_previous"][i.Index] = pur.es_date
            pur.prtno = i.prtno
            pur.plant = i.plant
            pur.plant_qty = i.plant_qty
            pur.location = i.location
            pur.lot = i.lot
            pur.r_qty = i.r_qty
            pur.price = i.price
            pur.pur_remark = i.pur_remark
            pur.es_date_lasttime = pur.es_date
            pur.es_date = i.es_date
            pur.po_no = i.po_no
            pur.pr_no = i.pr_no
            pur.inv_no = i.inv_no
            pur.mfgname = i.mfgname
            pur.mfgpn = i.mfgpn
            pur.qissue = i.qissue
            pur.sub_deltapn = i.sub_deltapn
            if status.get(i.pur_status, 0) >= status.get(pur.pur_status, 0):
                pur.pur_status = i.pur_status
            pur.mat_catelogue = i.mat_catelogue  # 添加可以修改材料类别
            pur.mat_group = i.mat_group
            pur.dest_area = i.dest_area
            pur.rd = i.rd
            if i.mat_receiveddate:
                pur.mat_receiveddate = i.mat_receiveddate

            # ======更新记录======
            dfi = df.loc[df.index == i.Index]
            dfi["up_type"] = up_type
            dfi["remark1"] = dfi["pur_status"]
            task_pur_update(dfi, nt_name)
        s.commit()

    dfp = df.loc[df["received_qty"] > 0]  # ===打印标签
    if not dfp.empty:
        dfp["limituse"] = dfp["dept"]
        dfp["type"] = dfp["application"]
        dfp["stockno"] = dfp["rd"]
        dfp["designno"] = dfp["mat_remark"]
        dfp["qty"] = dfp["received_qty"]
        dfp["station"] = nt_name
        dfp["label_template"] = "stock_out"
        dfp["id"] = dfp["deltapn"] + "{" + dfp["received_qty"].astype(str) + "{Pur"
        dfp["area"] = area
        dfp["owner"] = nt_name
        dfp["des"] = dfp["mfgpn"]
        dfp["checkcode"] = dfp["deltapn"]
        bg_label_print(dfp.to_json(orient="records"))

    c1 = df["mat_group"].str.strip().str.upper() == "PCB"  # ----更新到板日期
    c2 = df["prtno"].str.startswith(("SH", "HZ"))

    df_plant = df.loc[
        df["pur_status"].isin(["transferred", "closed", "received"])
    ]  # 调料逻辑
    if not df_plant.empty:
        df_plant = df_plant.drop("id", axis=1)
        with db.Session() as s:
            for i in df_plant.itertuples():
                sql = select(PurPlant).where(PurPlant.item_pur == i.item_pur)
                res = s.scalar(sql)
                if res:
                    if i.pur_status == "transferred":
                        if res.plant != i.plant:
                            res.plant = i.plant
                    elif i.pur_status in ("received", "closed"):
                        res.pur_status = i.pur_status
                else:
                    if i.pur_status == "transferred":
                        value = i._asdict()
                        value = {
                            i: value[i] for i in value if getattr(PurPlant, i, None)
                        }
                        value["pur_status"] = "pending"
                        new_plant = PurPlant(**value)
                        s.add(new_plant)
            s.commit()

    df["es_date"] = pd.to_datetime(df["es_date"]).dt.date
    df["es_date_previous"] = pd.to_datetime(df["es_date_previous"]).dt.date
    c1 = df["received_qty"] > 0  # 邮件逻辑
    c2 = df["prtno"].isnull()
    c3 = df["application"].str.lower() == "debug"
    c4 = df["es_date"] > df["es_date_previous"]
    c5 = df["mat_group"].str.strip().str.upper() == "PCB"
    c6 = df["application"].str.lower() == "project"
    c7 = df["prtno"].notnull()

    df_m1 = df.loc[c1 & c2 & c3]
    if not df_m1.empty:
        for rd in df_m1["rd"].unique():
            df_m1i = df_m1.query(f"rd=='{rd}'")
            subject = "您申请的材料已到，请到9楼领取"
            df_m1i = df_m1i.reindex(
                columns=["deltapn", "des", "mfgname", "mfgpn", "received_qty", "r_qty"]
            )
            df_m1i.columns = [
                "台达料号",
                "描述",
                "厂商",
                "厂商料号",
                "到货数量",
                "采购数量",
            ]
            title = f"<b>尊敬的 {rd} 先生/小姐：</b><br>您好！<br>您申请的如下材料已到，请前往九楼（发件人）处领取，谢谢！"
            body = df_to_html(df_m1i, title, link_text="")
            cc = f"{nt_name}@deltaww.com"
            to = f"{rd}@deltaww.com"
            bg_mail(to, subject, body, cc)

    df_m2 = df.loc[(c2 & c3 & c4) | (c6 & c4) | (c3 & c5 & c7 & c4)]
    if not df_m2.empty:
        params = df_m2["prtno"].unique().tolist()
        sql = "select prtno,pm from ssp.prt where prtno in %s"
        prt = read_sql(sql, params=[params])
        df_m2 = df_m2.merge(prt, on="prtno", how="left")
        df_m2["pm"] = np.where(df_m2["pm"].isna(), df_m2["rd"], df_m2["pm"])
        for rd in df_m2["rd"].unique():
            df_m2i = df_m2.query(f"rd=='{rd}'")
            pm = ";".join(df_m2i["pm"] + "@deltaww.com")
            to = f"{rd}@deltaww.com;{pm}"
            cc = f"{nt_name}@deltaww.com"
            subject = "材料预计交期延迟通知"
            df_m2i = df_m2i.reindex(
                columns=[
                    "prtno",
                    "proj",
                    "deltapn",
                    "mfgname",
                    "qty",
                    "es_date",
                    "pur_remark",
                    "es_date_lasttime",
                ]
            )
            df_m2i.columns = [
                "项目号",
                "机种名",
                "台达料号",
                "厂商",
                "需求数量",
                "预计到货日",
                "采购备注",
                "原始预计日期",
            ]
            title = f"<b>尊敬的 {rd} 先生/小姐：</b><br>您好！<br>对不起，您申请的如下材料预计交期延迟，对此带来的不便敬请谅解！"
            body = df_to_html(df_m2i, title, link_text="")
            bg_mail(to, subject, body, cc)

    return True, "更新成功", "success"


@callback(
    Output("tab2_notice", "is_open"),
    Output("tab2_notice", "children"),
    Output("tab2_notice", "color"),
    Input("tab2_dro_2", "value"),
    State("tab2_table", "multiRowsClicked"),
    State("tab2_table", "dataFiltered"),
    State("user", "data"),
)
def transfer_pur(pur, data, data_filtered, user):
    """转给其他采购"""
    if not pur:
        raise PreventUpdate
    nt_name = user.get("nt_name")

    if not data:
        return True, "请先选择需转单的记录", "danger"

    df = pd.DataFrame(data)
    dff = pd.DataFrame(i for i in data_filtered["rows"] if i)
    df = df.loc[df["id"].isin(dff["id"])]
    with db.Session() as s:
        for i in df.itertuples():
            sql = update(Pur).where(Pur.item_pur == i.item_pur).values(pur=pur)
            s.execute(sql)
        s.commit()

    df["up_type"] = "Update_Pur"
    df["remark1"] = pur
    df["remark2"] = df["pur_remark"]
    task_pur_update(df, nt_name)
    return True, f"转单{pur}成功", "success"


@callback(
    Output("tab2_notice", "is_open"),
    Output("tab2_notice", "children"),
    Output("tab2_notice", "color"),
    Input("tab2_cancel_btn", "n_clicks"),
    State("tab2_table", "multiRowsClicked"),
    State("tab2_table", "dataFiltered"),
    State("user", "data"),
)
def cancel_pur_item(n_clicks, data, data_filtered, user):
    """取消采购记录"""
    if not n_clicks:
        raise PreventUpdate

    if not data:
        return True, "请先选择待取消的记录", "danger"

    nt_name = user.get("nt_name")
    df = pd.DataFrame(data)
    dff = pd.DataFrame(i for i in data_filtered["rows"] if i)
    df = df.loc[df["id"].isin(dff["id"])]

    if (df["pur_remark"].fillna("") == "").any():
        return True, "取消时,采购备注不能为空", "danger"
    df["pur_status"] = "cancel"

    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = "update ssp.pur set pur_status=%s,pur_remark=%s,qissue=%s where id=%s"
            params = df[["pur_status", "pur_remark", "qissue", "id"]].values.tolist()
            cu.executemany(sql, params)

            sql = "update ssp.pur_plant set pur_status=%s where item_pur=%s"
            params = df[["pur_status", "item_pur"]].values.tolist()
            cu.executemany(sql, params)

            sql = "update ssp.ce set status=%s,ce_remark=concat_ws(',',ce_remark,%s) where item_pur=%s"
            params = df[["pur_status", "pur_remark", "item_pur"]].values.tolist()
            cu.executemany(sql, params)

            sql = "update ssp.smbom set status=%s where item_pur=%s"
            params = df[["pur_status", "item_pur"]].values.tolist()
            cu.executemany(sql, params)

            conn.commit()

    df["up_type"] = "Cancel"
    df["remark1"] = df["es_date"]
    df["remark2"] = df["pur_remark"]
    task_pur_update(df, nt_name)

    return True, "取消成功", "success"


@callback(
    Output("tab2_notice", "is_open"),
    Output("tab2_notice", "children"),
    Output("tab2_notice", "color"),
    Input("tab2_order_btn", "n_clicks"),
    State("tab2_table", "multiRowsClicked"),
    State("tab2_table", "dataFiltered"),
    State("user", "data"),
)
def order_pur_item(n_clicks, data, data_filtered, user):
    """下单采购记录"""
    if not n_clicks:
        raise PreventUpdate

    if not data:
        return True, "请先选择需下单的记录", "danger"

    nt_name = user.get("nt_name")
    now = datetime.now()
    df = pd.DataFrame(data)
    dff = pd.DataFrame(i for i in data_filtered["rows"] if i)
    df = df.loc[df["id"].isin(dff["id"])]

    with db.Session() as s:
        for i in df.itertuples():
            sql = (
                update(Pur)
                .where(Pur.id == i.id)
                .values(
                    prtno=i.prtno,
                    pur_status="wait for material",
                    pur_remark=i.pur_remark,
                    price=i.price,
                    pur_date=now,
                    plant=i.plant,
                    plant_qty=i.plant_qty,
                    location=i.location,
                    lot=i.lot,
                    po_no=i.po_no,
                    pr_no=i.pr_no,
                    inv_no=i.inv_no,
                    mfgpn=i.mfgpn,
                    r_qty=i.r_qty,
                    qissue=i.qissue,
                    sub_deltapn=i.sub_deltapn,
                )
            )
            res = s.execute(sql)
        s.commit()

    df["up_type"] = "Order"
    task_pur_update(df, nt_name)
    return True, "下单成功", "success"


@callback(
    Output("tab2_notice", "is_open"),
    Output("tab2_notice", "children"),
    Output("tab2_notice", "color"),
    Output("tab5_mail_store", "data"),
    Input("tab2_mail_btn", "n_clicks"),
    State("tab2_table", "multiRowsClicked"),
    State("tab2_table", "dataFiltered"),
    State("tab5_mail_store", "data"),
)
def mail_pur_item(n_clicks, data, data_filtered, mail_store):
    """添加记录到发件箱"""
    if not n_clicks:
        raise PreventUpdate

    if not data:
        return True, "请先选择需邮件的记录", "danger", no_update

    df = pd.DataFrame(data)
    dff = pd.DataFrame(i for i in data_filtered["rows"] if i)
    df = df.loc[df["item_pur"].isin(dff["item_pur"])]

    df["project_application"] = df["dept"].apply(lambda x: project_application.get(x))
    df["product_place"] = df["dept"].apply(lambda x: product_place.get(x))
    df["rd"] = df["rd"].str.title()
    df["proj"] = df["proj"].str.slice(0, 5)
    df["mfgname"] = df["mfgname"].str.upper()

    with db.Session() as s:
        sql = select(User.nt_name.label("rd"), User.area)
        df1 = pd.DataFrame(i for i in s.execute(sql))

        df1["rd"] = df1["rd"].str.title()
        df1["area"] = df1["area"].replace({"SH": "寄上海", "HZ": "寄杭州"})
        sql = select(
            Vendor.vendor_name.label("mfgname"),
            func.group_concat(Vendor.mail.op("separator")(",")).label("mail"),
        ).group_by(Vendor.vendor_name)
        df2 = pd.DataFrame(i for i in s.execute(sql))
        df2["mfgname"] = df2["mfgname"].str.upper()

    df = df.merge(df1, on="rd", how="left").merge(df2, on="mfgname", how="left")
    c1 = df["application"].str.lower().isin(["project", "stock"])
    c2 = df["des"].str.contains("smd", case=False, na=False)
    df["area"] = np.where(c1 & c2, "寄上海", df["area"])

    mail_store = mail_store or []
    dfm = pd.DataFrame(mail_store)

    df = pd.concat([df, dfm])
    df = df.drop_duplicates("id")

    return True, "添加至发件箱成功", "success", df.to_dict(orient="records")


@callback(
    Output("tab2_dro_1", "value"),
    Input("tab2_notice", "color"),
    State("user", "data"),
)
def reflesh_tab2_table(color, user):
    if color != "success":
        raise PreventUpdate
    nt_name = user.get("nt_name")
    return nt_name.title()


# =======查询=============
@callback(
    Output("tab3_datepicker1", "minDate"),
    Output("tab3_datepicker2", "minDate"),
    Output("tab3_datepicker1", "value"),
    Output("tab3_datepicker2", "value"),
    Input("tab3_datepicker1", "id"),
    prevent_initial_call=False,
)
def initial_pur_release_date(id):
    """初始化材料发起日开始结束时间"""
    now = datetime.now()
    start_value = now - timedelta(days=180)
    end_value = now
    min_date = now - timedelta(days=360 * 3)
    return min_date, min_date, start_value, end_value


# @cache.memoize(expire=3600 * 24 * 30)
def cache_dropdown():
    sql1 = select(Pur.pur_status).distinct()
    sql2 = select(Pur.application).distinct()
    sql3 = select(func.concat(Dept.dept_group, "_", Dept.dept_name)).distinct()
    sql4 = (
        select(User.nt_name)
        .where(
            or_(
                and_(User.role_group == "pur", User.termdate == None),
                User.nt_name.in_(("bo.sm.wang", "weiming.li")),
            )
        )
        .distinct()
    )
    sql5 = select(Pur.mfgname).distinct()
    sql6 = select(User.nt_name).where(User.termdate == None).distinct()
    sql7 = select(AMatCatalogue.category_1).distinct()
    sql8 = select(AMatCatalogue.pur_category2).distinct()

    with db.Session() as s:
        options1 = [
            {"label": i.title(), "value": i.title()} for i in s.scalars(sql1) if i
        ]
        options2 = [
            {"label": i.title(), "value": i.title()} for i in s.scalars(sql2) if i
        ]
        options3 = [{"label": i.upper(), "value": i.upper()} for i in s.scalars(sql3)]
        options4 = [
            {"label": i.title(), "value": i.title()} for i in s.scalars(sql4) if i
        ]
        options5 = [{"label": i.upper(), "value": i} for i in s.scalars(sql5) if i]

        options6 = [
            {"label": i.title(), "value": i.title()} for i in s.scalars(sql6) if i
        ]

        options7 = [
            {"label": i.title(), "value": i.title()} for i in s.scalars(sql7) if i
        ]

        options8 = [
            {"label": i.title(), "value": i.title()} for i in s.scalars(sql8) if i if i
        ]
        return [
            options1,
            options2,
            options3,
            options4,
            options5,
            options6,
            options7,
            options8,
        ]


@callback(
    Output("status_dropdown", "options"),
    Output("application_dropdown", "options"),
    Output("department_dropdown", "options"),
    Output("pur_dropdown", "options"),
    Output("mfgname_dropdown", "options"),
    Output("rd_dropdown", "options"),
    Output("cat1_dropdown", "options"),
    Output("cat2_dropdown", "options"),
    Output("pur_dropdown", "value"),
    Input("open-offcanvas-btn", "n_clicks"),
)
def tab3_dropdown_options(n_clicks):
    if not n_clicks:
        raise PreventUpdate
    nt_name = get_nt_name().title()
    options = cache_dropdown()
    return options + [[nt_name]]


@callback(
    Output("tab3_table_store", "data"),
    Input("tab3_select_btn", "n_clicks"),
    State("database_dropdown", "value"),
    State("status_dropdown", "value"),
    State("application_dropdown", "value"),
    State("department_dropdown", "value"),
    State("pur_dropdown", "value"),
    State("mfgname_dropdown", "value"),
    State("rd_dropdown", "value"),
    State("tab3_datepicker1", "value"),
    State("tab3_datepicker2", "value"),
)
def tab3_query_data(
    n_clicks,
    table,
    pur_status,
    application,
    dept,
    pur,
    mfgname,
    rd,
    start_date,
    end_date,
):
    """加载查询数据"""
    if not n_clicks:
        raise PreventUpdate

    cond = ["start_date >=%s", "start_date <=%s"]
    params = [start_date, end_date]
    if pur_status:
        cond.append("pur_status in %s")
        params.append(pur_status)
    if application:
        cond.append("application in %s")
        params.append(application)
    if dept:
        cond.append("dept in %s")
        params.append(dept)
    if pur:
        if table == "pur":
            cond.append("pur in %s")
            params.append(pur)
    if mfgname:
        cond.append("mfgname in %s")
        params.append(mfgname)
    if rd:
        cond.append("rd in %s")
        params.append(rd)

    cond = " and ".join(cond)
    sql = f"select * from ssp.{table} where {cond}"
    df = read_sql(sql, params=params)
    prt_id = df["prt_id"].dropna().unique().tolist()
    if prt_id:
        sql = "select id as prt_id,pcbstatus as pcb_date from ssp.prt where id in %s"
        prt = read_sql(sql, params=[prt_id])
        prt["pcb_date"] = pd.to_datetime(prt["pcb_date"], errors="coerce").dt.date
        df = df.merge(prt, on="prt_id", how="left")

    if df.empty:
        return Serverside(df)

    df["pur_lead_time"] = pd.to_datetime(df["mat_receiveddate"]) - pd.to_datetime(
        df["pur_date"]
    )

    df["pur_lead_time"] = (df["pur_lead_time"].dt.days.astype("string") + "天").fillna(
        ""
    )
    if table == "pur":
        df["total_price"] = (df["price"].fillna(0) * df["r_qty"].fillna(0)).round(2)

    date_cols = [
        "start_date",
        "pur_date",
        "req_date",
        "es_date",
        "mat_receiveddate",
    ]
    for i in date_cols:
        df[i] = pd.to_datetime(df[i], errors="coerce").dt.date

    return Serverside(df)


@callback(
    Output("tab3_table", "filter_query"),
    Input("tab3_select_btn", "n_clicks"),
)
def reflesh_filter_query(n_clicks):
    return ""


@callback(
    Output("tab3_table", "data"),
    Output("tab3_table", "page_count"),
    Output("tab3_table", "tooltip_data"),
    Input("tab3_table_store", "data"),
    Input("tab3_table", "filter_query"),
    Input("tab3_table", "page_size"),
    Input("tab3_table", "page_current"),
)
def tab3_table_page(df, filter, page_size, page_current):
    """查询模块自定义筛选器"""
    if df is None:
        raise PreventUpdate

    if filter:
        filtering_expressions = filter.split(" && ")
        for filter_part in filtering_expressions:
            fp = filter_part.split(" ")
            col_name = fp[0][1:-1]
            filter_value = str(fp[-1])
            df = df.loc[
                df[col_name]
                .astype(str)
                .str.contains(filter_value, case=False, na=False)
            ]

    page_count = math.ceil(df.shape[0] / page_size)
    dfi = df.iloc[page_current * page_size : (page_current + 1) * page_size]
    data = dfi.to_dict("records")
    tooltip_data = [
        {
            column: {"value": str(value), "type": "markdown"}
            for column, value in row.items()
        }
        for row in data
    ]
    return dfi.to_dict("records"), page_count, tooltip_data


@callback(
    Output("tab3_allcheck_btn", "color"),
    Output("tab3_allcheck_btn", "children"),
    Input("tab3_allcheck_btn", "n_clicks"),
    State("tab3_allcheck_btn", "color"),
)
def tab3_allcheck_btn_style(n_clicks, color):
    if not n_clicks:
        raise PreventUpdate
    if color == "warning":
        return "success", "取消全选"
    else:
        return "warning", "全选"


@callback(
    Output("tab3_table_row_ids_store", "data"),
    Input("tab3_allcheck_btn", "color"),
    Input("tab3_table", "derived_viewport_selected_rows"),
    State("tab3_table_row_ids_store", "data"),
    State("tab3_table", "page_current"),
    State("tab3_table", "page_count"),
)
def tab3_select_all(color, selected_rows, ids_store, page_current, page_count):
    """查询全选功能"""
    id = ctx.triggered_id
    if id == "tab3_allcheck_btn":
        if color == "success":
            lst = {i: list(range(10)) for i in range(page_count)}
            return lst
        else:
            return {}

    elif id == "tab3_table":
        if selected_rows:
            ids_store.update({page_current: selected_rows})
            return ids_store
        else:
            raise PreventUpdate
    else:
        raise PreventUpdate


@callback(
    Output("tab3_table", "selected_rows"),
    Input("tab3_table_row_ids_store", "data"),
    Input("tab3_table", "page_current"),
)
def tab3_selected_rows(ids_store, page):
    """查询全选功能"""
    return ids_store.get(f"{page}", [])


# @callback(
#     Output("tab3_table", "selected_rows"),
#     Input("tab3_table", "page_current"),
#     Input("tab3_allcheck_btn", "color"),
#     State("tab3_table_row_ids_store", "data"),
# )
# def tab3_selected_rows(page, color, selected_rows):
#     """查询全选功能"""
#     if not selected_rows:
#         raise PreventUpdate

#     if ctx.triggered_id == "tab3_allcheck_btn":
#         if color == "warning":
#             return []
#         else:
#             return selected_rows.get(f"{page}")
#     else:
#         if not selected_rows:
#             raise PreventUpdate
#         sr = selected_rows.get(page, [])
#         return sr


@callback(
    Output("pur_download", "data"),
    Input("tab3_dowload_btn", "n_clicks"),
    State("tab3_table_store", "data"),
)
def dowload_query_data(n_clicks, df):
    if not n_clicks:
        raise PreventUpdate

    xlsx = dcc.send_data_frame(
        df.to_excel,
        f"采购记录_{datetime.now():%y%m%d%H%M%S}.xlsx",
        index=False,
    )
    return xlsx


@callback(
    Output("offcanvas-placement", "is_open"),
    Input("tab3_table", "data"),
)
def close_offcanvas(data):
    return False


@callback(
    Output("pur-reopen", "opened"),
    Input("tab3_table", "active_cell"),
    Input("reopen-cancel", "n_clicks"),
    State("tab3_table", "data"),
)
def open_reopen_modal(active_cell, n1, data):
    id = ctx.triggered_id
    if id == "tab3_table":
        if not active_cell:
            raise PreventUpdate
        status = [i for i in data if i["id"] == active_cell["row_id"]][0]["pur_status"]
        if status.lower() not in ("closed", "cancel"):
            raise PreventUpdate
        if active_cell.get("column_id", "") == "pur_status":
            return True
        else:
            raise PreventUpdate
    elif id == "reopen-submit":
        return False
    elif id == "reopen-cancel":
        return False


@callback(
    Output("reopen-submit", "children"),
    Output("reopen-cancel", "children"),
    Output("reopen-submit", "disabled"),
    Input("reopen-submit", "n_clicks"),
    Input("pur-reopen", "opened"),
    State("tab3_table", "active_cell"),
    State("tab3_table", "data"),
    State("database_dropdown", "value"),
)
def close_reopen_modal(n_clicks, opened, active_cell, data, database):
    """释放采购记录"""
    id = ctx.triggered_id
    if id == "reopen-submit":
        row_id = active_cell.get("row_id")
        table = Pur if database == "pur" else PurPlant
        with db.Session() as s:  # 提交时修改数据库
            pur = s.get(table, row_id)
            pur.pur_status = "wait for material"
            pur.received_qty = None
            pur.mat_receiveddate = None
            s.commit()
        return "已修改", "关闭", True
    else:
        return "确定", "取消", False


# =======调料=============
@callback(
    Output("tab4_table", "data"),
    Output("tab4_table", "tooltip_data"),
    Input("tab4_refresh_btn", "n_clicks"),
)
def load_pur_plant_data(n):
    """加载调料数据"""
    if not n:
        raise PreventUpdate

    sql = "select * from ssp.pur_plant where pur_status in (%s,%s,%s,%s)"
    params = ("wait for material", "pending", "received", "transferred")
    df = read_sql(sql, params=params)
    df.columns = df.columns.str.lower()

    c1 = df["pur_status"].str.lower() == "wait for material"
    c2 = df["es_date"].isnull()
    c3 = df["es_date"] > df["req_date"]
    df["pur_status"] = np.where(c1 & c2, "confirm_etd", df["pur_status"])
    df["pur_status"] = np.where(c1 & c3, "over_demand", df["pur_status"])

    date_cols = ["start_date", "pur_date", "req_date", "es_date"]
    for i in date_cols:
        df[i] = pd.to_datetime(df[i], errors="coerce").dt.date

    sql = "SELECT distinct checkcode,SUM(IF(area= 'SH',qty,0)) AS 'sh_stock',SUM(IF(area= 'HZ',qty,0)) AS 'hz_stock' FROM stock GROUP BY checkcode"
    stock = read_sql(sql)

    sql = "select id as dept_id,charge_code from ssp.dept"
    depts = read_sql(sql)
    df = df.merge(stock, on="checkcode", how="left").merge(
        depts, on="dept_id", how="left"
    )

    df = df.replace({"": None}).replace({np.nan: None})
    data = df.to_dict(orient="records")

    tooltip_data = [
        {
            column: {"value": str(value), "type": "markdown"}
            for column, value in row.items()
        }
        for row in data
    ]
    return data, tooltip_data


@callback(
    Output("tab4_notice", "is_open"),
    Output("tab4_notice", "children"),
    Output("tab4_notice", "color"),
    Output("tab4_table", "data"),
    Input("tab4_upload", "contents"),
)
def upload_plant_data(contents):
    if not contents:
        raise PreventUpdate

    content_type, content_string = contents.split(",")
    decoded = base64.b64decode(content_string)
    df = pd.read_excel(io.BytesIO(decoded))
    columns_needed = {"id", "料号", "厂区", "工厂库存", "仓别", "架位", "采购数量"}
    columns_sets = columns_needed - set(df.columns.intersection(columns_needed))

    if columns_sets:
        return True, f"上传文件栏位不符{','.join(columns_needed)}", "danger", no_update

    df = df.rename(
        columns={
            "料号": "deltapn",
            "厂区": "plant",
            "工厂库存": "plant_qty",
            "仓别": "location",
            "架位": "lot",
            "采购数量": "r_qty",
        }
    )
    df = df.replace({"": None}).replace({np.nan: None})
    with db.Session() as s:
        data = []
        for i in df.itertuples():
            plant = s.get(PurPlant, i.id)
            if plant.pur_status.lower() == "pending":
                plant.plant = i.plant
                plant.plant_qty = i.plant_qty
                plant.location = i.location
                plant.lot = i.lot
                plant.r_qty = i.r_qty
            data.append(plant.to_dict())
        s.commit()
    return True, "上传更新成功", "success", data


@callback(
    Output("tab4_notice", "is_open"),
    Output("tab4_notice", "children"),
    Output("tab4_notice", "color"),
    Output("pur_download", "data"),
    Input("tab4_dowload_btn", "n_clicks"),
    State("tab4_table", "derived_virtual_selected_row_ids"),
    State("tab4_table", "data"),
)
def dowload_plant_data(n_clicks, selected_row_ids, data):
    if not n_clicks:
        raise PreventUpdate

    if not selected_row_ids:
        return True, "请先选择需下载的记录", "danger", no_update

    df = pd.DataFrame(data)
    df = df.loc[df["id"].isin(selected_row_ids)]

    columns = [
        "id",
        "dept",
        "application",
        "proj",
        "deltapn",
        "des",
        "mfgname",
        "mfgpn",
        "r_qty",
        "start_date",
        "req_date",
        "plant",
        "plant_qty",
        "location",
        "lot",
        "charge_code",
    ]
    df = df.reindex(columns=columns)
    xlsx = dcc.send_data_frame(
        df.to_excel,
        f"调料_{datetime.now():%y%m%d%H%M%S}.xlsx",
        index=False,
    )
    return True, "下载成功", "success", xlsx


@callback(
    Output("tab4_notice", "is_open"),
    Output("tab4_notice", "children"),
    Output("tab4_notice", "color"),
    Input("tab4_order_btn", "n_clicks"),
    State("tab4_table", "derived_virtual_selected_row_ids"),
    State("tab4_table", "data"),
    State("user", "data"),
)
def order_plant_item(n_clicks, selected_row_ids, data, user):
    """下单采购记录"""
    if not n_clicks:
        raise PreventUpdate

    if not selected_row_ids:
        return True, "请先选择需下单的记录", "danger"

    nt_name = user.get("nt_name")
    now = datetime.now()

    df = pd.DataFrame(data)
    df = df.loc[df["id"].isin(selected_row_ids)]

    if (df["pur_status"].str.lower() != "pending").any():
        return True, "仅pending材料可以下单", "danger"

    with db.Session() as s:
        for i in df.itertuples():
            sql = (
                update(PurPlant)
                .where(PurPlant.id == i.id)
                .values(pur_status="wait for material", pur_date=now)
            )
            res = s.execute(sql)
        s.commit()

    df["up_type"] = "Order"
    task_pur_update(df, nt_name)
    return True, "下单成功", "success"


@callback(
    Output("tab4_notice", "is_open"),
    Output("tab4_notice", "children"),
    Output("tab4_notice", "color"),
    Input("tab4_update_btn", "n_clicks"),
    State("tab4_table", "derived_virtual_selected_row_ids"),
    State("tab4_table", "data"),
    State("user", "data"),
)
def update_plant_data(n_clicks, selected_row_ids, data, user):
    """更新按钮"""
    if not n_clicks:
        raise PreventUpdate

    if not selected_row_ids:
        return True, "请先选择需更新的记录", "danger"

    nt_name = user.get("nt_name")
    now = datetime.now()

    df = pd.DataFrame(data)
    df = df.loc[df["id"].isin(selected_row_ids)]

    with db.Session() as s:
        for i in df.itertuples():
            sql = (
                update(PurPlant)
                .where(PurPlant.id == i.id)
                .values(
                    plant=i.plant,
                    plant_qty=i.plant_qty,
                    location=i.location,
                    lot=i.lot,
                    r_qty=i.r_qty,
                    es_date=i.es_date,
                    pur_remark=i.pur_remark,
                )
            )
            res = s.execute(sql)

            sql = (
                update(Pur)
                .where(and_(Pur.item_pur == i.item_pur, Pur.r_qty != i.r_qty))
                .values(r_qty=i.r_qty)
            )
            res = s.execute(sql)
        s.commit()

    df["up_type"] = "Plant_RecordUpdate"
    task_pur_update(df, nt_name)

    return True, "更新成功", "success"


@callback(
    Output("tab4_notice", "is_open"),
    Output("tab4_notice", "children"),
    Output("tab4_notice", "color"),
    Input("tab4_transfer_btn", "n_clicks"),
    State("tab4_table", "derived_virtual_selected_row_ids"),
    State("tab4_table", "data"),
    State("user", "data"),
)
def transfer_plant_data(n_clicks, selected_row_ids, data, user):
    """转单按钮"""
    if not n_clicks:
        raise PreventUpdate

    if not selected_row_ids:
        return True, "请先选择需转单的记录", "danger"

    nt_name = user.get("nt_name")

    df = pd.DataFrame(data)
    df = df.loc[df["id"].isin(selected_row_ids)]
    df["pur_remark"] = df["pur_remark"].fillna("")

    # if (df["pur_remark"].fillna("") == "").any():
    #     return True, "采购备注不能为空", "danger"

    with db.Session() as s:
        for i in df.itertuples():
            pur_plant = s.get(PurPlant, i.id)
            pur_plant.pur_status = "Cancel"

            query = Pur.select().where(Pur.item_pur == i.item_pur)
            pur = s.scalar(query)
            if pur.pur_status not in ("received", "closed", "cancel"):
                pur.pur_status = "Pending"
                pur.pur_remark = ",".join([pur.pur_remark or "", i.pur_remark])
                pur.plant = None
                pur.plant_qty = None
                pur.location = None
                pur.lot = None
        s.commit()

    df["up_type"] = "Plant_Transfer"
    task_pur_update(df, nt_name)
    return True, "转单成功", "success"


@callback(
    Output("tab4_table", "selected_rows"),
    Input("tab4_allcheck_btn", "n_clicks"),
    State("tab4_table", "derived_virtual_indices"),
    State("tab4_table", "selected_rows"),
)
def tab4_select_all(n_clicks, indices, selected_rows):
    """全选功能"""
    # print(selected_rows)
    if not n_clicks:
        raise PreventUpdate
    if selected_rows:
        return []
    else:
        return indices


@callback(
    Output("tab4_table", "selected_cells"),
    Output("tab4_table", "active_cell"),
    Output("tab4_table", "selected_rows"),
    Input("tab4_notice", "color"),
)
def reflesh_tab4(color):
    if color != "success":
        raise PreventUpdate
    return [], None, []


# @callback(
#     Output(tabs, "active_tab"),
#     Input(tab4_notice, "color"),
# )
# def reflesh_tab4(color):
#     if color != "success":
#         raise PreventUpdate
#     return "tab-4"


# ========发件箱============
# @callback(
#     Output("tab5_table", "data"),
#     Input("tab5_refresh_btn", "n_clicks"),
#     State("tab5_mail_store", "data"),
# )
# def active_tab5(n, data):
#     """切换tab时更新表格数据"""
#     if not n:
#         raise PreventUpdate
#     print(n)
#     return data


@callback(
    Output("tab5_mail_store", "data"),
    Input("tab5_table", "dataChanged"),
)
def mail_delete_row(data):
    """切换tab时更新表格数据"""
    if data is None:
        raise PreventUpdate
    return data


@callback(
    Output("tab5_table", "data"),
    Input("tab5_mail_store", "data"),
)
def mail_store_to_data(data):
    return data


@callback(
    Output("tab5_notice", "is_open"),
    Output("tab5_notice", "children"),
    Output("tab5_notice", "color"),
    Output("tab5_mail_store", "data"),
    Input("tab5_update_btn", "n_clicks"),
    State("tab5_table", "data"),
)
def update_mail_data(n_clicks, data):
    if not n_clicks:
        raise PreventUpdate

    return True, "更新成功", "success", data


@callback(
    Output("tab5_notice", "is_open"),
    Output("tab5_notice", "children"),
    Output("tab5_notice", "color"),
    Output("tab5_mail_store", "data"),
    Input("tab5_send_btn", "n_clicks"),
    State("tab5_table", "multiRowsClicked"),
    State("tab5_table", "dataFiltered"),
    State("tab5_table", "data"),
    State("tab5_table", "columns"),
    State("user", "data"),
)
def send_mail_data(n_clicks, select_data, data_filtered, data, columns, user):
    """发送邮件"""
    if not n_clicks:
        raise PreventUpdate

    nt_name = user.get("nt_name")
    df = pd.DataFrame(select_data)
    if df.empty:
        return True, "请选择待发送记录", "danger", no_update

    dff = pd.DataFrame(i for i in data_filtered["rows"] if i)
    df = df.loc[df["id"].isin(dff["id"])]
    if df["mail"].isna().any() or df["mfgname"].isna().any():
        return True, "厂商,邮箱不能为空", "danger", no_update

    with db.Session() as s:
        for i in df.itertuples():
            pur = s.get(Pur, i.id)
            if pur:
                if pur.pur_status.lower() in ("pending", "transferred"):
                    pur.pur_status = "wait for material"
                    pur.pur_date = datetime.now()
                    s.commit()

    dfa = pd.DataFrame(data)
    dfa = dfa.loc[~dfa["id"].isin(df["id"])]  # 剩下为发送的数据

    cols = {i.get("field"): i.get("title") for i in columns if i.get("field")}
    df = df.reindex(columns=cols).rename(columns=cols)
    df = df.drop(["id"], axis=1)
    cc = f"{nt_name}@deltaww.com"  # 按厂商分组
    for i in df["厂商"].unique():
        dfi = df.loc[df["厂商"] == i]
        to = ";".join(dfi["邮箱"].str.split(",").explode().unique())
        dfi = dfi.drop(["邮箱"], axis=1)
        subject = f"材料申请-{i}-{datetime.now().date()}"
        title = f"<b>尊敬的 {i}厂商：</b><br>您好！<br>如下材料请帮忙申请，另麻烦您告知一下交期，谢谢！"
        body = df_to_html(dfi, title, link_text="")
        bg_mail(to, subject, body, cc)
    return True, "发送成功", "success", dfa.to_dict(orient="records")


# =======供应商=============
@callback(
    Output("tab6_table", "data"),
    Input("tab6_add_row", "n_clicks"),
    State("tab6_table", "data"),
)
def tab6_table_add_row(n_clicks, data):
    """供应商插入行"""
    if n_clicks:
        if not data:
            row = {"id": 1}
        else:
            row = {"id": max(i["id"] for i in data) + 1, "action": "add"}
        data.insert(0, row)
        return data
    else:
        raise PreventUpdate


@callback(
    Output("tab6_table", "data"),
    Input("tab6_refresh_btn", "n_clicks"),  # tabs, "active_tab"
)
def load_vendor_data(n):
    """加载供应商数据"""
    if not n:
        raise PreventUpdate
    sql = select(Vendor)
    with db.Session() as s:
        res = s.scalars(sql)
        data = [i.to_dict() for i in res]
        return data


@callback(
    Output("tab6_notice", "is_open"),
    Output("tab6_notice", "children"),
    Output("tab6_notice", "color"),
    Input("tab6_update_btn", "n_clicks"),
    State("tab6_table", "data"),
)
def vendor_data_changed(n_clicks, data):
    if not n_clicks:
        raise PreventUpdate
    df = pd.DataFrame(data)

    if "action" not in df.columns:
        return True, "action不能为空", "danger"

    df = df.replace({"": None}).replace({np.nan: None})
    df_add = df.loc[df["action"] == "add"]
    df_update = df.loc[df["action"] == "update"]
    df_delete = df.loc[df["action"] == "delete"]

    with db.Session() as s:
        if not df_add.empty:
            df_add["vendor_name"] = df_add["vendor_name"].str.strip().str.upper()
            for i in df_add.itertuples():
                v = i._asdict()
                del v["Index"], v["action"], v["id"]
                new_vendor = Vendor(**v)
                s.add(new_vendor)

        if not df_update.empty:
            df_update["vendor_name"] = df_update["vendor_name"].str.strip().str.upper()
            for i in df_update.itertuples():
                v = i._asdict()
                del v["Index"], v["action"], v["id"]
                sql = update(Vendor).where(Vendor.id == i.id).values(v)
                res = s.execute(sql)

        if not df_delete.empty:
            for i in df_delete.itertuples():
                vendor = s.get(Vendor, i.id)
                s.delete(vendor)
        s.commit()
    return True, "更新成功", "success"


# =======分工职责=============
@callback(
    Output("pur_assign_table", "rowData"),
    Input("tab7_refresh_btn", "n_clicks"),
)
def load_duty_data(n_clicks):
    """加载分工职责数据"""
    if not n_clicks:
        raise PreventUpdate
    # sql = "select a.*,b.cat1,b.cat2_ce from pur_assign a right join \
    #     (select id as cat_id,category_1 as cat1,category_2 as cat2_ce  \
    #         from ssp_ce.a_mat_catalogue) b \
    #     on a.cat_id=b.cat_id"
    # sql = "select * from (select id as id_ce,category_1 as cat1,\
    #     category_2 as cat2_ce from ssp_ce.a_mat_catalogue) a left join pur_assign b \
    #     on a.id_ce=b.cat_id"
    # df = read_sql(sql)
    # df["cat2"] = np.where(df["cat2"].isna(), df["cat2_ce"], df["cat2"])
    # df["cat_id"] = np.where(df["cat_id"].isna(), df["id_ce"], df["cat_id"])
    # df = df.groupby(["cat1", "cat2"], as_index=False).agg(
    #     {
    #         "cat_id": list,
    #         "id": list,
    #         "pur": lambda x: list(set(x)),
    #         "area": lambda x: list(set(x)),
    #     }
    # )
    sql = "select * from  pur_assign"
    df = read_sql(sql)
    return df.to_dict(orient="records")


@callback(
    Output("global-notice", "children"),
    Output("pur_assign_table", "rowData"),
    Input("tab7_update_btn", "n_clicks"),
    State("pur_assign_table", "rowData"),
)
def duty_data_changed(n_clicks, data):
    if not n_clicks:
        raise PreventUpdate
    df = pd.DataFrame(data)

    if "action" not in df.columns:
        return notice("action不能为空", "error"), no_update

    dfu = df.loc[df["action"] == "update"]
    if dfu.empty:
        return notice("无更新", "error"), no_update
    dfx = df.loc[~df.index.isin(dfu.index)]

    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = "update pur_assign set pur=%s where id=%s"
            params = dfu.reindex(columns=["pur", "id"]).values.tolist()
            cu.executemany(sql, params)
            conn.commit()

    return notice("更新成功"), dfx.to_dict(orient="records")


# =======安全库存=============
@callback(
    Output("tab9_table", "data"),
    Input("tab9_add_row", "n_clicks"),
    State("tab9_table", "data"),
)
def tab9_table_add_row(n_clicks, data):
    """tab9_table插入行"""
    if n_clicks:
        data.insert(0, {})
        return data
    else:
        raise PreventUpdate


@callback(
    Output("tab9_dept_select", "treeData"),
    Input("tab9_dept_select", "id"),
    prevent_initial_call=False,
)
def initial_dept_select(id):
    # if active != "5":
    #     raise PreventUpdate
    sql = "select category,dept_group as dept from ssp.dept"
    df = read_sql(sql)
    df["category"] = np.where(
        df["dept"].isin(["AMP", "ATI", "APE"]), "OBC", df["category"]
    )
    tree = [
        {
            "title": x,
            "value": x,
            "key": x,
            "children": [
                {"title": d, "value": d, "key": d} for d in y["dept"].unique()
            ],
        }
        for x, y in df.groupby("category")
    ]
    all_tree = [{"title": "ALL", "value": "ALL", "key": "ALL", "children": tree}]
    return all_tree


@callback(Output("tab9_table", "data"), Input("tab9_dro_1", "value"))
def load_safety_stock_data(value):
    if not value:
        return []
    value = value.lower()
    if value == "cancel":
        sql = "select * from ssp.safetystock where status =%s"
        params = [value]
    elif value == "all":
        sql = "select * from ssp.safetystock where status in (%s,%s)"
        params = ["Processing", "TBD"]
    elif value == "add":
        return [{}]
    else:
        sql = "select * from ssp.safetystock \
        where latestowner=%s and status in (%s,%s)"
        params = [value, "Processing", "TBD"]
    df = read_sql(sql, params=params)
    df.columns = df.columns.str.lower()
    df.drop(["latestdate"], axis=1, inplace=True)
    df = safety_stock_handler(df)

    data = df.to_dict(orient="records")
    return data


@callback(
    Output("global-notice", "children"),
    Input("tab9_update_btn", "n_clicks"),
    State("tab9_table", "multiRowsClicked"),
    State("tab9_table", "dataFiltered"),
    State("tab9_dept_select", "value"),
    State("user", "data"),
)
def safety_stock_update(n_clicks, data, data_filtered, limit_use, user):
    """安全库存更新按钮"""
    if not n_clicks:
        raise PreventUpdate
    if not data:
        return notice("请先选择需更新的记录", "error")

    df = pd.DataFrame(data)
    dff = pd.DataFrame(i for i in data_filtered["rows"] if i)
    df = df.loc[df["id"].isin(dff["id"])]

    # =====取消============
    dfc = df.loc[df["status"] == "Cancel"]
    if not dfc.empty:
        if (df["memo"].fillna("") == "").any():
            return notice("取消时,特殊说明不能为空", "error")

        with db.Session() as ses:
            for i in dfc.itertuples():
                ss = ses.get(Safetystock, i.id)
                ss.limituse = "ALL"
                ss.status = i.status
                ss.memo = i.memo
                ss.canceldate = datetime.now()
            ses.commit()
        df = df.loc[~df.index.isin(dfc.index)]

    if not df.empty:
        if limit_use:
            if "ALL" in limit_use:
                df["limituse"] = "ALL"
            else:
                df["limituse"] = ",".join(limit_use)

        with db.Session() as ses:
            for i in df.itertuples():
                ss = ses.get(Safetystock, i.id)
                ss.limituse = i.limituse
                ss.area = i.area
                ss.status = i.status
                ss.mfgname = i.mfgname
                ss.mfgpn = i.mfgpn
                ss.custom_qty = i.custom_qty
                ss.pur_qty = i.pur_qty
                ss.memo = i.memo
            ses.commit()

    return notice()


@callback(
    Output("global-notice", "children"),
    Input("tab9_cancel_btn", "n_clicks"),
    State("tab9_table", "multiRowsClicked"),
    State("tab9_table", "dataFiltered"),
    State("tab9_dept_select", "value"),
    State("user", "data"),
)
def safety_stock_cancel(n_clicks, data, data_filtered, limit_use, user):
    """安全库存取消按钮"""
    if not n_clicks:
        raise PreventUpdate
    if not data:
        return notice("请先选择需取消的记录", "error")

    df = pd.DataFrame(data)
    dff = pd.DataFrame(i for i in data_filtered["rows"] if i)
    df = df.loc[df["id"].isin(dff["id"])]

    if (df["memo"].fillna("") == "").any():
        return notice("取消时,特殊说明不能为空", "error")

    with db.Session() as ses:
        for i in df.itertuples():
            ss = ses.get(Safetystock, i.id)
            ss.limituse = "ALL"
            ss.status = "Cancel"
            ss.memo = i.memo
            ss.canceldate = datetime.now()
        ses.commit()

    return notice("取消成功")


# @callback(
#     Output("tab9_modal", "is_open"),
#     Output("tab9_modal", "children"),
#     Output("tab9_table", "data"),
#     Input("tab9_calculate_btn", "n_clicks"),
#     State("tab9_table", "multiRowsClicked"),
#     State("tab9_table", "dataFiltered"),
#     State("user", "data"),
# )
def safety_stock_calculate(n_clicks, data, data_filtered, user):
    """安全库存计算按钮"""
    if not n_clicks:
        raise PreventUpdate
    if not data:
        return notice("请先选择需计算的记录", "error"), no_update

    df = pd.DataFrame(data)
    dff = pd.DataFrame(i for i in data_filtered["rows"] if i)
    df = df.loc[df["id"].isin(dff["id"])]
    df = df.tail(1)
    params = df["checkcode"].unique().tolist()
    sql = "select stockoutdate,dept,qty from ssp.stockout \
    where checkcode in %s and qty>0"
    # and \stockoutdate>DATE_SUB(CURDATE(), INTERVAL 1 year)
    stockout = read_sql(sql, params=[params])
    stockout["dept"] = stockout["dept"].str.split("_", expand=True)[0]

    sql = "select pur_date,mat_receiveddate from ssp.pur \
    where checkcode in %s and pur_date is not null \
    and mat_receiveddate is not null"
    pur = read_sql(sql, params=[params])
    pur["days"] = (pur["mat_receiveddate"] - pur["pur_date"]).dt.days

    # stockout_qty = stockout["qty"].tolist() + (-stockout["qty"]).tolist()
    # hist_data = [stockout_qty]
    # group_labels = ["distplot"]  # name of the dataset
    # mean = np.mean(stockout_qty)
    # stdev_pluss = np.std(stockout_qty)
    # stdev_minus = np.std(stockout_qty) * -1
    # fig = ff.create_distplot(hist_data, group_labels)
    # fig.update_layout(title_text=f"{','.join(params)}用量分布")
    # graph0 = dcc.Graph(figure=fig, config={"displayModeBar": False})

    stockout["day"] = stockout["stockoutdate"].dt.day
    stockout["month"] = stockout["stockoutdate"].dt.month
    stockout["year"] = stockout["stockoutdate"].dt.year
    stockout["week"] = stockout["stockoutdate"].dt.isocalendar().week
    stockout["sum_day"] = stockout.groupby(["year", "month", "day"])["qty"].transform(
        "sum"
    )
    stockout["sum_week"] = stockout.groupby(["year", "month", "week"])["qty"].transform(
        "sum"
    )
    stockout["sum_month"] = stockout.groupby(["year", "month"])["qty"].transform("sum")
    # stockout["sum_year"] = stockout.groupby(["year"])["qty"].transform("sum")

    # std = np.std(stockout["sum_day"])
    # ss = dmc.Center(dmc.Text(f"安全库存设定建议值{int(2 * np.sqrt(27)*std)}"))
    # stockout["avg_day"] = np.average(stockout["sum_day"])
    # stockout["avg_week"] = np.average(stockout["sum_week"])
    # stockout["avg_month"] = np.average(stockout["sum_month"])

    # fig.add_shape(
    #     type="line",
    #     x0=mean,
    #     x1=mean,
    #     y0=0,
    #     y1=0.4,
    #     xref="x",
    #     yref="y",
    #     line=dict(color="blue", dash="dash"),
    # )
    # fig.add_shape(
    #     type="line",
    #     x0=stdev_pluss,
    #     x1=stdev_pluss,
    #     y0=0,
    #     y1=0.4,
    #     xref="x",
    #     yref="y",
    #     line=dict(color="red", dash="dash"),
    # )
    # fig.add_shape(
    #     type="line",
    #     x0=stdev_minus,
    #     x1=stdev_minus,
    #     y0=0,
    #     y1=0.4,
    #     xref="x",
    #     yref="y",
    #     line=dict(color="red", dash="dash"),
    # )
    fig = px.ecdf(
        stockout,
        x=["sum_day", "sum_week", "sum_month"],
        title="用料数量经验累积分布函数",
    )
    graph1 = dcc.Graph(figure=fig, config={"displayModeBar": False})

    fig = px.ecdf(pur, x=["days"], title="采购LeadTime经验累积分布函数")
    graph2 = dcc.Graph(figure=fig, config={"displayModeBar": False})

    fig = px.scatter(
        stockout, x="stockoutdate", y="qty", title="出库记录", color="dept"
    )
    graph3 = dcc.Graph(figure=fig, config={"displayModeBar": False})

    fig = px.pie(stockout, values="qty", names="dept", title="部门分布")
    graph4 = dcc.Graph(figure=fig, config={"displayModeBar": False})

    graph = dmc.Stack([graph3, graph4, graph1, graph2])

    return True, graph, no_update  # df.to_dict(orient="records")


@callback(
    Output("global-notice", "children"),
    Input("tab9_dro_2", "value"),
    State("tab9_table", "multiRowsClicked"),
    State("tab9_table", "dataFiltered"),
    State("user", "data"),
)
def safety_stock_transfer(pur, data, data_filtered, user):
    """安全库存转单"""
    if not pur:
        raise PreventUpdate
    if not data:
        return notice("请先选择需转单的记录", "error")

    df = pd.DataFrame(data)
    dff = pd.DataFrame(i for i in data_filtered["rows"] if i)
    df = df.loc[df["id"].isin(dff["id"])]

    with db.Session() as ses:
        for i in df.itertuples():
            ss = ses.get(Safetystock, i.id)
            ss.latestowner = pur
        ses.commit()

    return notice("转单成功")


@callback(
    Output("pur_download", "data"),
    Input("tab9_download_btn", "n_clicks"),
    State("tab9_table", "multiRowsClicked"),
    State("tab9_table", "dataFiltered"),
    State("user", "data"),
)
def safety_stock_download(n_clicks, data, data_filtered, user):
    """安全库存下载"""
    if not n_clicks:
        raise PreventUpdate
    if not data:
        return notice("请先选择需下载的记录", "error")

    df = pd.DataFrame(data)
    dff = pd.DataFrame(i for i in data_filtered["rows"] if i)
    df = df.loc[df["id"].isin(dff["id"])]
    xlsx = dcc.send_data_frame(
        df.to_excel,
        f"安全库存_{datetime.now():%y%m%d%H%M%S}.xlsx",
        index=False,
    )
    return xlsx


@callback(
    # Output("tab9_dro_1", "value"),
    Output("global-notice", "children"),
    Input("tab9_add_btn", "n_clicks"),
    State("tab9_table", "data"),
    State("tab9_dro_1", "value"),
    State("tab9_table", "columns"),
    State("user", "data"),
    State("tab9_dept_select", "value"),
)
def safety_stock_add(n_clicks, data, action: str, columns, user, limit_use):
    """安全库存新增"""
    if not n_clicks:
        raise PreventUpdate

    if action.lower() != "add":
        return notice("查询栏请选择Add", "error")

    df = pd.DataFrame(data)
    if df.empty:
        return notice("新增数据不能为空", "error")
    col = [
        "area",
        "limituse",
        "status",
        "checkcode",
        "deltapn",
        "des",
        "mfgname",
        "mfgpn",
        "custom_qty",
        "pur_qty",
        "memo",
        "latestowner",
        "addtype",
    ]
    df = df.reindex(columns=col)
    if limit_use:
        if "ALL" in limit_use:
            df["limituse"] = "ALL"
        else:
            df["limituse"] = ",".join(limit_use)

    sql = "select deltapn,checkcode as checkcode_csg,des as des_csg,\
    mfgname as mfgname_csg,mfgpn as mfgpn_csg from ssp_csg.mat_info \
    where deltapn in %s"
    params = df["deltapn"].tolist()
    csg = read_sql(sql, params=[params])
    csg = csg.drop_duplicates("deltapn")
    df = df.merge(csg, on="deltapn", how="left")

    df["checkcode"] = np.where(
        df["checkcode_csg"].notna(), df["checkcode_csg"], df["deltapn"]
    )
    df["des"] = np.where(df["des_csg"].notna(), df["des_csg"], df["des"])
    df["mfgname"] = np.where(
        df["mfgname_csg"].notna(), df["mfgname_csg"], df["mfgname"]
    )
    df["mfgpn"] = np.where(df["mfgpn_csg"].notna(), df["mfgpn_csg"], df["mfgpn"])

    df["latestowner"] = user.get("nt_name")
    df["addtype"] = "Pur"
    df["status"] = df["status"].fillna("TBD")
    df = df.reindex(columns=col)
    df = df.replace({np.nan: None})
    try:
        with db.Session() as ses:
            for item in df.itertuples():
                data = item._asdict()
                data.pop("Index")
                new_data = Safetystock(**data)
                ses.add(new_data)
            ses.commit()
        return notice()
    except Exception as e:
        return notice(f"{e._message()}", "error")


@callback(
    Output("tab9_dept_select", "value"),
    Input("global-notice", "children"),
)
def reset_dept_select(notice):
    if notice.get("props").get("type") == "success":
        return []
    else:
        raise PreventUpdate


# =======文档=============
@callback(
    Output("pur_download", "data"),
    Input("tab8_download_btn", "n_clicks"),
)
def dowload_document(n_clicks):
    if not n_clicks:
        raise PreventUpdate
    return dcc.send_file(
        SSP_DIR / "program" / "DOC" / "工程物料模块功能说明-V0.9.2-更新.xlsm"
    )


# ------------------侧边栏的回调---------------
@callback(
    Output("offcanvas-placement", "is_open"),
    Input("open-offcanvas-btn", "n_clicks"),
    State("offcanvas-placement", "is_open"),
)
def toggle_offcanvas(n1, is_open):
    if n1:
        return not is_open
    return is_open


@cache.memoize(tag="std_cache")
def std_cache() -> tuple[pd.DataFrame]:
    sql = "SELECT area,checkcode,STD(qty) AS std_qty,avg(qty) AS avg_qty FROM stockout \
    where qty>0 AND dept_id!='10' \
    and stockoutdate>=DATE_SUB(CURDATE(), INTERVAL 1 year) \
    GROUP BY area,checkcode"
    df1 = read_sql(sql)

    sql = "SELECT checkcode,std(TIMESTAMPDIFF(day,pur_date,mat_receiveddate)) \
    AS std_lt,avg(TIMESTAMPDIFF(day,pur_date,mat_receiveddate)) AS avg_lt \
    FROM pur where pur_date IS NOT null AND mat_receiveddate IS NOT null  \
    GROUP BY checkcode"
    df2 = read_sql(sql)

    return df1, df2
