# -*- coding: utf-8 -*-
import dash
import pandas as pd
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import Input, Output, State, callback

from common import (
    add_mat_category,
    df_add_ce_owner,
    material_information_complete,
    df_to_html,
)
from components.notice import notice

# from dbtool import db
from datetime import datetime
from . import layout
from tasks import bg_mail
from utils import db

id = layout.id
layout = layout.layout
dash.register_page(__name__, path="/ce/pn/customer/rd", title="客户专用料号申请-RD")


@callback(
    Output(id("table"), "data"),
    Input(id("add-row"), "n_clicks"),
    State(id("table"), "data"),
)
def add_empty_row_to_table(n_clicks, data):
    if n_clicks:
        data.append({})
        return data
    else:
        raise PreventUpdate


@callback(
    Output(id("table"), "data"),
    Input(id("table"), "data_timestamp"),
    State(id("table"), "active_cell"),
    State(id("table"), "data"),
    State(id("table"), "columns"),
)
def update_table_when_pasting_data(data_timestamp, active_cell, data, columns):
    if not data_timestamp:
        raise PreventUpdate

    column_id = active_cell.get("column_id") if active_cell else None
    if column_id not in ("deltapn", "mfgpn", "des"):
        raise PreventUpdate

    df = pd.DataFrame(data)
    cols = [i.get("id") for i in columns]
    df = df.reindex(columns=cols)

    column_id = "mfgpn" if column_id == "des" else column_id
    df = material_information_complete(df, column_id)
    df = add_mat_category(df)
    return df.to_dict(orient="records")


@callback(
    Output("global-notice", "children"),
    Output(id("rd-submit"), "disabled"),
    Input(id("rd-submit"), "n_clicks"),
    State(id("table"), "data"),
    State(id("customer"), "value"),
    State(id("attachment"), "lastUploadTaskRecord"),
    State(id("applicant"), "value"),
    State(id("title"), "children"),
)
def rd_submit(n_clicks, data, customer, attachment, applicant, title):
    if not n_clicks:
        raise PreventUpdate

    df = pd.DataFrame(data)
    df = df.replace({"": None})
    df = df.dropna(how="all")

    # 必须deltapn,cat1,cat2，cat3

    if df.empty:
        return notice("材料数据表不能为空", "error"), False
    if not customer:
        return notice("客户不能为空", "warning"), False
    if not attachment:
        return notice("附件不能为空", "warning"), False

    user = db.find_one("ssp.user", {"nt_name": applicant})
    dept = user.get("dept")
    dept_id = user.get("dept_id")
    sub_type = title[:-3]

    cc = ["Ru.Xue", "Ying.Gao"]
    if dept_id in (4, 5, 22):
        cc += ["Yuhan.Wang"]

    df = df_add_ce_owner(df)
    for item in df.itertuples():
        ce = item.owner_ce
        task = {
            "status": "open",
            "urgent": "一般",
            "type": "料号申请",
            "sub_type": sub_type,
            "applicant": applicant,
            "dept": dept,
            "ce": ce,
            "cat1": item.cat1,
            "cat2": item.cat2,
            "cat3": item.cat3,
            "deltapn": item.deltapn,
            "des": item.des,
            "mfgpn": item.mfgpn,
            "mfgname": item.mfgname,
            "dept_id": dept_id,
            "cc": ",".join(cc),
            "start_date": datetime.now(),
        }
        task_id = db.insert("ce.task", task)
        attachment_str = f'{attachment["taskId"]}/{attachment["fileName"]}'

        x = {
            "customer": customer,
            "attachment": attachment_str,
            "task_id": task_id,
            "deltapn": item.deltapn,
            "des": item.des,
            "mfgname": item.mfgname,
            "mfgpn": item.mfgpn,
            "cat1": item.cat1,
            "cat2": item.cat2,
            "cat3": item.cat3,
        }
        db.insert("ce.pn_customer", x)

        # *---------邮件通知开始-----------*
        to = [applicant, ce] + cc
        to = ";".join(f"{i}@deltaww.com" for i in to)
        subject = f"【料号申请】{sub_type}"
        df = pd.DataFrame([task])
        columns = [
            "type",
            "dept",
            "applicant",
            "deltapn",
            "mfgname",
            "mfgpn",
            "start_date",
            "ce",
        ]
        df = df.reindex(columns=columns)
        df["start_date"] = df["start_date"].dt.date
        content = df_to_html(
            df,
            f"您的{sub_type}已提交,{ce}将会处理,请知悉！",
            href="http://sup.deltaww.com/info/rd?page=ongoing",
            link_text="工作进展可至个人中心查询",
        )
        bg_mail(to, subject, content)
        # *---------邮件通知结束-----------*

    return notice("提交成功"), True
