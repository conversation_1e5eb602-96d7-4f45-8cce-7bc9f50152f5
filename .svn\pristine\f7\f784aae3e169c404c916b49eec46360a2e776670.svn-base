# -*- coding: utf-8 -*-
import numpy as np
import pandas as pd
from chinese_calendar import get_workdays

from common import read_sql
from config import cfg


def spec_report_owner(start_date=None, end_date=None):
    sql = "select * from ssp_spec.task where status!=%s and release_date between %s and %s"
    df = read_sql(sql, params=["canceled", start_date, end_date])
    if df.empty:
        return df
    df["spec"] = df["spec"].fillna("NA").str.title()
    df = df.fillna(0)
    df["doc_type"] = df["doc_type"].str.upper()
    df["doc_type"] = df["doc_type"].replace(
        {
            "SM": "SM/ECN",
            "ECN": "SM/ECN",
            "ES": "ES/TS/SN",
            "TS": "ES/TS/SN",
            "SN": "ES/TS/SN",
            "RC": "RC/OTHERS",
            "OTHERS": "RC/OTHERS",
            "DFMS": "DFMS/CM",
            "CM": "DFMS/CM",
        }
    )
    df["qty"] = np.where(df["doc_type"] == "PART COST", df["qty"], 1)

    # *-----人员绩效--------------------
    df1 = df.pivot_table(
        index="spec",
        columns="doc_type",
        values="qty",
        aggfunc="sum",
        margins=True,
    )

    df2 = df.pivot_table(
        index="spec",
        values=["psl_qty", "ecn_qty", "work_time_minute"],
        aggfunc="sum",
        margins=True,
    )
    df2["psl_qty"] = df2["psl_qty"].round(2)
    df2["ecn_qty"] = df2["ecn_qty"].round(2)
    df2["work_time_minute"] = df2["work_time_minute"].round(2)
    df["followup_ontime"] = np.where(df["followup_ontime"] == "Y", 1, 0)
    df["release_ontime"] = np.where(df["release_ontime"] == "Y", 1, 0)

    c1 = df["doc_type"].isin(["SM/ECN", "MODIFY ISSUE", "NEW ISSUE"])
    c3 = df["followup_date"] != 0

    df3 = df.loc[c1 & c3].pivot_table(
        index="spec",
        values=["followup_ontime"],
        aggfunc=lambda x: x.sum() / x.shape[0],
        margins=True,
    )
    df31 = (
        df.loc[c1]
        .pivot_table(
            index="spec",
            values=["act_release_lt"],
            aggfunc="mean",
            margins=True,
        )
        .rename(columns={"act_release_lt": "ecn_lt_mean"})
    )
    df32 = (
        df.loc[c1]
        .pivot_table(
            index="spec",
            values=["release_ontime"],
            aggfunc=lambda x: x.sum() / x.shape[0],
            margins=True,
        )
        .rename(columns={"release_ontime": "ecn_ontime"})
    )

    c1 = df["doc_type"].isin(["SMBOM", "CHANGE", "CHANGE_QTY"])
    df5 = (
        df.loc[c1]
        .pivot_table(
            index="spec",
            values=["work_time_minute"],
            aggfunc="sum",
            margins=True,
        )
        .rename(columns={"work_time_minute": "smbom_wkt"})
    )
    c1 = df["doc_type"] == "SMBOM"
    df6 = (
        df.loc[c1]
        .pivot_table(
            index="spec",
            values=["act_release_lt"],
            aggfunc="mean",
            margins=True,
        )
        .rename(columns={"act_release_lt": "smbom_lt_mean"})
    )
    df61 = (
        df.loc[c1]
        .pivot_table(
            index="spec",
            values=["release_ontime"],
            aggfunc=lambda x: x.sum() / x.shape[0],
            margins=True,
        )
        .rename(columns={"release_ontime": "smbom_ontime"})
    )
    c1 = df["doc_type"] == "CHANGE"
    df7 = (
        df.loc[c1]
        .pivot_table(
            index="spec",
            values=["act_release_lt"],
            aggfunc="mean",
            margins=True,
        )
        .rename(columns={"act_release_lt": "change_lt_mean"})
    )
    df71 = (
        df.loc[c1]
        .pivot_table(
            index="spec",
            values=["release_ontime"],
            aggfunc=lambda x: x.sum() / x.shape[0],
            margins=True,
        )
        .rename(columns={"release_ontime": "change_ontime"})
    )

    df_list = [df1, df2, df3, df31, df32, df5, df6, df61, df7, df71]

    sql = "select * from ssp_spec.index where month between %s and %s"
    df4 = read_sql(sql, params=[start_date, end_date])
    if not df4.empty:
        df4["ratio"] = df4["ratio"].str.strip().str.replace("%", "").astype(float) / 100
        df4["spec"] = df4["spec"].str.title().str.strip()
        df4 = df4.pivot_table(
            index="spec",
            values=["work_days", "ratio"],
            aggfunc="sum",
            margins=True,
        )
        # df4["work_days"] = df4["work_days"].round(3)
        # df4["ratio"] = df4["ratio"].round(3)
        df_list.append(df4)

    dfx = pd.concat(df_list, axis=1)
    workdays = len(get_workdays(pd.to_datetime(start_date), pd.to_datetime(end_date)))
    dfx["loading"] = dfx["work_time_minute"] / (8 * 60 * workdays)
    dfx["smbom_loading"] = dfx["smbom_wkt"] / (8 * 60 * workdays)

    dfx = dfx.replace({0: None})
    dfx = dfx.reset_index()

    dfx.columns = dfx.columns.str.upper()

    return dfx


def spec_report_dept(start_date=None, end_date=None):
    sql = "select * from ssp_spec.task where status!=%s and release_date between %s and %s"
    df = read_sql(sql, params=["canceled", start_date, end_date])
    if df.empty:
        return df
    df = df.fillna(0)
    df["doc_type"] = df["doc_type"].str.upper()
    sql = "select dept,doc_type,followup_lt,ecn_qty,psl_qty,work_time_minute \
        from ssp_spec.due_day"
    dfd = read_sql(sql)
    dfd["doc_type"] = dfd["doc_type"].str.upper()
    df.drop(
        columns=["followup_lt", "ecn_qty", "psl_qty", "work_time_minute"], inplace=True
    )
    df = df.merge(dfd, on=["dept", "doc_type"], how="left")
    df["doc_type"] = df["doc_type"].replace(
        {
            "SM": "SM/ECN",
            "ECN": "SM/ECN",
            "ES": "ES/TS/SN",
            "TS": "ES/TS/SN",
            "SN": "ES/TS/SN",
            "RC": "RC/OTHERS",
            "OTHERS": "RC/OTHERS",
            "DFMS": "DFMS/CM",
            "CM": "DFMS/CM",
        }
    )
    df["qty"] = np.where(df["doc_type"] == "PART COST", df["qty"], 1)
    c1 = df["doc_type"].isin(
        ["SM/ECN", "MODIFY ISSUE", "NEW ISSUE", "SMBOM", "CHANGE", "CHANGE_QTY"]
    )
    df["act_release_lt"] = np.where(c1, df["act_release_lt"], 0)
    df["release_ontime"] = np.where(df["release_ontime"] == "Y", 1, 0)

    # *-------部门分摊-------------
    c1 = df["doc_type"].isin(
        [
            "ES/TS/SN",
            "RC/OTHERS",
            "DFMS/CM",
            "PN",
            "SBOM",
            "CBOM",
            "DFCS",
            "COST QUERY",
            "BOM COST",
        ]
    )
    df["spec_qty"] = np.where(c1, 1, 0)
    c1 = df["doc_type"].isin(
        [
            "SM/ECN",
            "TOOLA",
            "TOOLB",
            "55X",
            "ME PART",
        ]
    )
    df["ecn_qty"] = np.where(c1, 1, 0)

    df1 = df.pivot_table(
        index="dept",
        columns="doc_type",
        values="qty",
        aggfunc="sum",
        margins=True,
    )
    df2 = df.pivot_table(
        index="dept",
        values=["ecn_qty", "work_time_minute"],
        aggfunc="sum",
        margins=True,
    )
    df2["ecn_qty"] = df2["ecn_qty"].round(2)
    df2["work_time_minute"] = df2["work_time_minute"].round(2)

    df3 = df.pivot_table(
        index="dept",
        values=["spec_qty", "ecn_qty"],
        aggfunc="sum",
        margins=True,
    )
    df3["total_apportion"] = df3["spec_qty"] + df3["ecn_qty"]
    total_apportion = df3["total_apportion"].sum() / 2
    df3["ratio_apportion"] = df3["total_apportion"] / total_apportion
    df3["ratio_apportion"] = df3["ratio_apportion"].round(2)
    df3 = df3.drop(columns=["ecn_qty"])

    c1 = df["doc_type"].isin(["SM/ECN", "MODIFY ISSUE", "NEW ISSUE"])
    df4 = df.loc[c1].pivot_table(
        index="dept",
        values=["act_release_lt"],
        aggfunc="mean",
        margins=True,
    )
    df4["act_release_lt"] = df4["act_release_lt"].round(3)

    df5 = df.loc[c1].pivot_table(
        index="dept",
        values=["release_ontime"],
        aggfunc=lambda x: x.sum() / x.count(),
        margins=True,
    )

    c1 = df["doc_type"].isin(["SMBOM", "CHANGE", "CHANGE_QTY"])
    df6 = (
        df.loc[c1]
        .pivot_table(
            index="dept",
            values=["work_time_minute"],
            aggfunc="sum",
            margins=True,
        )
        .rename(columns={"work_time_minute": "smbom_wkt"})
    )

    c1 = df["doc_type"] == "SMBOM"
    df7 = (
        df.loc[c1]
        .pivot_table(
            index="dept",
            values=["act_release_lt"],
            aggfunc="mean",
            margins=True,
        )
        .rename(columns={"act_release_lt": "smbom_lt_mean"})
    )
    df71 = (
        df.loc[c1]
        .pivot_table(
            index="dept",
            values=["release_ontime"],
            aggfunc=lambda x: x.sum() / x.shape[0],
            margins=True,
        )
        .rename(columns={"release_ontime": "smbom_ontime"})
    )
    c1 = df["doc_type"] == "CHANGE"
    df8 = (
        df.loc[c1]
        .pivot_table(
            index="dept",
            values=["act_release_lt"],
            aggfunc="mean",
            margins=True,
        )
        .rename(columns={"act_release_lt": "change_lt_mean"})
    )
    df81 = (
        df.loc[c1]
        .pivot_table(
            index="dept",
            values=["release_ontime"],
            aggfunc=lambda x: x.sum() / x.shape[0],
            margins=True,
        )
        .rename(columns={"release_ontime": "change_ontime"})
    )

    # df5["release_ontime"] = df5["release_ontime"].round(2)
    dfx = pd.concat([df1, df2, df3, df4, df5, df6, df7, df71, df8, df81], axis=1)
    workdays = len(get_workdays(pd.to_datetime(start_date), pd.to_datetime(end_date)))
    dfx["smbom_loading"] = dfx["smbom_wkt"] / (8 * 60 * workdays)

    dfx = dfx.reindex(cfg.kpi_depts)
    # dfx = dfx.replace({0: None})
    dfx = dfx.reset_index()
    dfx.columns = dfx.columns.str.upper()
    return dfx
