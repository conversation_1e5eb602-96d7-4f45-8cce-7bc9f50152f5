# -*- coding: utf-8 -*-
from datetime import datetime

import dash_ag_grid as dag
import dash_mantine_components as dmc
import feffery_antd_components.alias as fac
import pandas as pd
from dash import MATCH, ctx, dcc, html
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import Input, Output, State, callback
from dash_iconify import DashIconify

from common import id_factory, read_sql
from utils import db

id = id_factory(__name__)


def register_layout(user):
    sql = "select * from ssp_ext.trainning where end_date is null"
    df = read_sql(sql)
    sql = "select distinct trainning_id from ssp_ext.trainning_record where applicant = %s"
    df1 = read_sql(sql, params=[user.get("nt_name")])
    registered_id = df1["trainning_id"].tolist()

    df["teacher"] = df["teacher"].str.title()
    card = []
    for i in df.itertuples():
        if i.id in registered_id:
            btn = dmc.Button(
                "已经报名",
                variant="light",
                color="blue",
                fullWidth=True,
                mt="md",
                radius="md",
                id={"type": id("register-btn"), "index": i.id},
                disabled=True,
            )
        else:
            btn = dmc.Button(
                "我要报名",
                variant="light",
                color="blue",
                fullWidth=True,
                mt="md",
                radius="md",
                id={"type": id("register-btn"), "index": i.id},
            )
        if i.skype:
            btn = dmc.Anchor(
                dmc.Button(
                    "加入Skype会议",
                    id={"type": id("skype-btn"), "index": i.id},
                    color="red",
                    variant="light",
                    fullWidth=True,
                    mt="md",
                    radius="md",
                ),
                href=i.skype,
                # target="_blank",
            )
        cardi = dmc.Card(
            children=[
                dmc.CardSection(
                    dmc.Image(
                        src="/assets/course.svg",
                        # height=120,
                        # h=60,
                        # alt="Norway",
                    )
                ),
                dmc.CardSection(
                    [
                        dmc.Text(i.subject, fw=500, td="underline"),
                        fac.Descriptions(
                            [
                                fac.DescriptionItem(i.teacher, label="讲师"),
                                fac.DescriptionItem(i.start_date, label="开课时间"),
                            ],
                            column=1,
                            labelStyle={"color": "orange"},
                        ),
                    ],
                    # withBorder=True,
                    inheritPadding=True,
                    # py="xs",
                    # bg="gray.0",
                ),
                btn,
            ],
            withBorder=True,
            shadow="sm",
            radius="md",
            style={
                "display": "flex",
                "flex-direction": "column",
                "justify-content": "space-between",
            },
        )
        card.append(cardi)
    grid = dmc.Container(dmc.SimpleGrid(card, cols=3))

    return grid


def course_layout():
    sql = "select id,skype,teacher,subject,start_date,\
        end_date,function,owner from ssp_ext.trainning order by id desc"
    df = read_sql(sql)
    columnDefs = [
        {"field": "id", "headerName": "id", "hide": True},
        {"field": "teacher", "headerName": "teacher"},
        {"field": "subject", "headerName": "subject"},
        {"field": "function", "headerName": "function"},
        {"field": "owner", "headerName": "owner"},
        {"field": "start_date", "headerName": "start_date"},
        {"field": "skype", "headerName": "skype"},
        {
            "field": "end_date",
            "headerName": "end_date",
            "cellEditor": "agDateStringCellEditor",
        },
    ]
    div = dag.AgGrid(
        id=id("course-table"),
        className="ag-theme-quartz",
        columnDefs=columnDefs,
        rowData=df.to_dict("records"),
        columnSize="sizeToFit",
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
        },
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
            "editable": True,
        },
        style={"height": "70vh"},
    )
    new = dmc.Button("新增", size="xs", id=id("new_btn"))
    return dmc.Container(html.Div([new, div]), fluid=True)


def record_layout():
    sql = (
        "select b.subject,a.applicant,c.onno,a.start_date,a.end_date from ssp_ext.trainning_record a \
        left join ssp_ext.trainning b on a.trainning_id = b.id \
        left join ssp.user c on a.applicant = c.nt_name"
    )
    df = read_sql(sql)
    columnDefs = [
        # {"field": "id", "headerName": "id", "hide": True},
        {"field": "subject", "headerName": "课程"},
        {"field": "applicant", "headerName": "学员"},
        {"field": "onno", "headerName": "工号"},
        {"field": "start_date", "headerName": "报名日期"},
        {"field": "end_date", "headerName": "完训日期"},
    ]
    div = dag.AgGrid(
        id=id("record-table"),
        className="ag-theme-quartz",
        columnDefs=columnDefs,
        rowData=df.to_dict("records"),
        columnSize="sizeToFit",
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
        },
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
            "editable": True,
        },
        style={"height": "70vh"},
    )
    download_btn = dmc.Button(
        "下载",
        color="green",
        size="xs",
        id=id("download_btn"),
        leftSection=DashIconify(icon="material-symbols:download", height=20),
    )
    download_excel = dcc.Download(id=id("download-excel"))
    return dmc.Container(html.Div([download_btn, download_excel, div]), fluid=True)


def layout(user=None, **kwargs):
    if not user:
        return
    display = "none"
    if user.get("nt_name").lower() in (
        "yan.wy.wang",
        "weiming.li",
        "lilian.wang",
        "siying.meng",
    ):
        display = "block"

    tabs = dmc.Tabs(
        [
            dmc.TabsList(
                [
                    dmc.TabsTab("课程列表", value="1"),
                    dmc.TabsTab("课程维护", value="2", color="green", display=display),
                    dmc.TabsTab("报名记录", value="3", color="blue", display=display),
                ]
            ),
            dmc.Space(h=5),
            html.Div(id=id("content")),
        ],
        color="red",
        value="1",
        id=id("tabs"),
    )
    return tabs


@callback(
    Output(id("content"), "children"),
    Input(id("tabs"), "value"),
    State("user", "data"),
    prevent_initial_call=False,
)
def active_tabs(value, user):
    if value == "1":
        return register_layout(user)
    elif value == "2":
        return course_layout()
    elif value == "3":
        return record_layout()
    else:
        raise PreventUpdate


@callback(
    Output(id("tabs"), "value"),
    Input(id("new_btn"), "n_clicks"),
)
def course_add_new(n_clicks):
    if not n_clicks:
        raise PreventUpdate
    now = datetime.now()
    db.insert(
        "ssp_ext.trainning",
        {
            "teacher": "new",
            "subject": "new",
            "function": "new",
            "owner": "new",
            "start_date": now,
            "end_date": now,
        },
    )
    return "2"


@callback(
    Input(id("course-table"), "cellValueChanged"),
)
def course_update(changed):
    if not changed:
        raise PreventUpdate

    data = changed[0]["data"]
    db.update("ssp_ext.trainning", data)


@callback(
    Output({"type": id("register-btn"), "index": MATCH}, "children"),
    Output({"type": id("register-btn"), "index": MATCH}, "disabled"),
    Input({"type": id("register-btn"), "index": MATCH}, "n_clicks"),
    State("user", "data"),
)
def register_submit(n_clicks, user):
    if not n_clicks:
        raise PreventUpdate
    trainning_id = ctx.triggered_id["index"]
    nt_name = user.get("nt_name")

    sql = (
        "replace into ssp_ext.trainning_record (trainning_id,applicant) values (%s,%s)"
    )
    params = (trainning_id, nt_name)
    db.execute(sql, params)
    return "已经报名", True


@callback(
    Output({"type": id("skype-btn"), "index": MATCH}, "key"),
    Input({"type": id("skype-btn"), "index": MATCH}, "n_clicks"),
    State("user", "data"),
)
def join_skype(n_clicks, user):
    if not n_clicks:
        raise PreventUpdate

    now = datetime.now()
    trainning_id = ctx.triggered_id["index"]
    nt_name = user.get("nt_name")
    res = db.find_one(
        "ssp_ext.trainning_record", {"trainning_id": trainning_id, "applicant": nt_name}
    )
    if res:
        sql = "update ssp_ext.trainning_record set end_date = now() \
            where trainning_id = %s and applicant = %s"
        params = (trainning_id, nt_name)
    else:
        sql = "insert into ssp_ext.trainning_record \
            (trainning_id,applicant,start_date,end_date) \
            values (%s,%s,%s,%s)"
        params = (trainning_id, nt_name, now, now)

    # sql = "update ssp_ext.trainning_record set end_date = now() \
    #     where trainning_id = %s and applicant = %s"
    # params = (trainning_id, nt_name)
    db.execute(sql, params)
    raise PreventUpdate


@callback(
    Output(id("download-excel"), "data"),
    Input(id("download_btn"), "n_clicks"),
    State(id("record-table"), "rowData"),
)
def download_excel(n_clicks, data):
    if not n_clicks:
        raise PreventUpdate
    dff = pd.DataFrame(data)
    dff["end_date"] = pd.to_datetime(dff["end_date"]).dt.date
    dff["start_date"] = pd.to_datetime(dff["start_date"]).dt.date
    dff = dff.rename(
        columns={
            "end_date": "完训日期",
            "start_date": "报名日期",
            "subject": "课程",
            "applicant": "学员",
        }
    )
    return dcc.send_data_frame(
        dff.to_excel, "培训记录.xlsx", sheet_name="Sheet1", index=False
    )
