var dagfuncs = window.dashAgGridFunctions = window.dashAgGridFunctions || {};

const {useRef, createElement, useEffect, useCallback, forwardRef, useMemo} = React

// Use this with DMC components
dagfuncs.AllFunctionalComponentEditors = forwardRef(({ value, ...params }, ref) => {
    const comp = params.colDef.cellEditorParams.component;
    const node = useRef(params.api.getRowNode(params.node.id));
    const newValue = useRef(value);
    const escaped = useRef(null);
    const editorRef = useRef(null);

    const handleStop = ({ event, ...params }) => {
        setTimeout(() => {
            if (!escaped.current) {
                node.current.setDataValue(params.column.colId, newValue.current);
            }
        }, 1);
    };

    if (params.colDef.cellEditorPopup) {
        comp['props']['style'] = { ...comp.props?.style, width: params.column.actualWidth - 2 };
    }

    const setProps = (propsToSet) => {
        if ('value' in propsToSet) {
            newValue.current = propsToSet.value;
        }
    };

    const handleKeyDown = ({ key, ...event }) => {
        if (key == "Escape") {
            escaped.current = true;
        }
    };

    useEffect(() => {
        params.api.addEventListener('cellEditingStopped', handleStop);
        document.addEventListener('keydown', handleKeyDown);
        params.colDef.suppressKeyboardEvent = (params) => params.editing && params.event.key != 'Tab';
        return () => {
            document.removeEventListener('keydown', handleKeyDown);
            delete params.colDef.suppressKeyboardEvent;
            params.api.removeEventListener('cellEditingStopped', handleStop);
        };
    }, []);

    useEffect(() => {
        if (editorRef.current) {
            if (editorRef.current.querySelector('input')) {
                editorRef.current.querySelector('input').focus();
            } else {
                editorRef.current.focus()
            }
        }
    }, [editorRef.current]);

    const el = useMemo(() =>
        createElement(
            'div',
            { ref: editorRef, tabIndex: 0 },
            createElement(window[comp['namespace']][comp['type']], { ...comp.props, setProps, value })
        ),
    []);

    return el;
});

// Use this with most dash-core-components
dagfuncs.AllComponentEditors = class {
    // gets called once before the renderer is used
    init(params) {

        // create the cell
        this.params = params;

        this.eInput = document.createElement('div');

        this.root = ReactDOM.createRoot(this.eInput)

        // sets editor value to the value from the cell
        this.value = params.value;
    }

    // gets called once when grid ready to insert the element
    getGui() {
        return this.eInput;
    }

    // focus and select can be done after the gui is attached
    afterGuiAttached() {
        const comp = this.params.colDef.cellEditorParams.component
        this.params.colDef.suppressKeyboardEvent = (params) => params.editing && params.event.key != 'Tab'
        if (this.params.colDef.cellEditorPopup) {
            comp['props']['style'] = {...comp.props?.style, width: this.params.column.actualWidth-2}
        }

        const setProps = (propsToSet) => {
            if ('value' in propsToSet) {
               this.value = propsToSet.value
            }
            this.root.render(
                createElement(window[comp.namespace][comp.type], {...comp.props, value: this.value, setProps})
            )
        }

        this.root.render(
            createElement(window[comp.namespace][comp.type], {...comp.props, value: this.value, setProps})
        )

    }

    // returns the new value after editing
    getValue() {
        delete this.params.colDef.suppressKeyboardEvent
        return this.value;
    }
};

dagfuncs.rowPinningBottom = function (params) {

  if (params.node.rowPinned) {
    // Text of pinned row is bold for all columns
    let style = { fontWeight: "bold" };

    if (params.colDef.field === "country") {
      // Add text size = 25 for "Average"
      style["fontSize"] = 25
    } else if (["lifeExp", "pop", "gdpPercap"].includes(params.colDef.field)) {
      // Add blue text color for means
      style["color"] = "#2186f4"
    }

    return {
      component: window.dashAgGridComponentFunctions.RowPinningBottomComponent,
      params: { style: style },
    };
  } else {
    // rows that are not pinned don't use any cell renderer
    return undefined;
  }
};
dagfuncs.inventoryDate = (params, date) => {
  const targetDate = new Date(date);
  const itemDate = new Date(params.data.inventory_date);
  return itemDate <= targetDate
};

dagfuncs.EUR = function (number) {
  return Intl.DateTimeFormat().format(number);
}

dagfuncs.dynamicOptions = function (params) {
  const qty = params.data.qty;
  const source = params.data.source;
  const stockno = params.data.stockno;
  if (qty > 0) {
    if (source === 'pur') {
      return {
        options: ['关闭']
      };
    } else {
      return {
        options: ['出库', '关闭'],
      };
    };
  } else {
    if (stockno === null) {
      return {
        options: ['关闭']
      };
    } else {
      return {
        options: ['退库', '关闭']
      }
    };
  }
}

dagfuncs.dynamicOptions2 = function (params) {
  const area = params.data.area;
  const user_area = params.data.user_area;
  if (area === user_area) {
    return {
      options: ['入库']
    };
  } else {
    return {
      options: ['转寄']
    };
  };
}

dagfuncs.NumberInput = class {
  // gets called once before the renderer is used
  init(params) {
    // create the cell
    this.eInput = document.createElement('input');
    this.eInput.value = params.value;
    this.eInput.style.height = '30px';
    this.eInput.style.fontSize = 'calc(var(--ag-font-size) + 1px)';
    this.eInput.style.borderWidth = 0;
    this.eInput.style.width = '95%';
    this.eInput.type = "number";
    this.eInput.min = params.min;
    this.eInput.max = params.max;
    this.eInput.step = params.step || "any";
    this.eInput.required = params.required;
    this.eInput.placeholder = params.placeholder || "";
    this.eInput.name = params.name;
    this.eInput.disabled = params.disabled;
    this.eInput.title = params.title || ""
  }

  // gets called once when grid ready to insert the element
  getGui() {
    return this.eInput;
  }

  // focus and select can be done after the gui is attached
  afterGuiAttached() {
    this.eInput.focus();
    this.eInput.select();
  }

  // returns the new value after editing
  getValue() {
    return this.eInput.value;
  }

  // any cleanup we need to be done here
  destroy() {
    // but this example is simple, no cleanup, we could
    // even leave this method out as it's optional
  }

  // if true, then this editor will appear in a popup
  isPopup() {
    // and we could leave this method out also, false is the default
    return false;
  }
}

dagfuncs.bomDownload = function (params) {
  var dagcomponentfuncs = window.dashAgGridComponentFunctions
  const genderDetails = {
    component: dagcomponentfuncs.DBC_Button,
    // params: { options: ["green", 'red'] },
  };
  if (params.data) {
    if (params.data.bom === '已完成') return genderDetails;
    else return undefined;
  }
  // return undefined;
}

dagfuncs.bomOpenHightLight = function (params) {
  const count =[params.data.b_ee, params.data.b_me, params.data.b_mag].filter(x=>x=='Y').length
  if (count <3 && count>0) {return true};
}

dagfuncs.DMC_Select = class {
  // gets called once before the renderer is used
  init(params) {
    // create the cell
    this.params = params;

    // function for when Dash is trying to send props back to the component / server
    var setProps = (props) => {
      if (typeof props.value != typeof undefined) {
        // updates the value of the editor
        this.value = props.value;

        // re-enables keyboard event
        delete params.colDef.suppressKeyboardEvent;

        // tells the grid to stop editing the cell
        params.api.stopEditing();

        // sets focus back to the grid's previously active cell
        this.prevFocus.focus();
      }
    };
    this.eInput = document.createElement('div');

    // renders component into the editor element
    ReactDOM.render(
      React.createElement(window.dash_mantine_components.Select, {
        data: params.options,
        value: params.value,
        setProps,
        style: { width: params.column.actualWidth },
        className: params.className,
        clearable: params.clearable,
        searchable: params.searchable || true,
        creatable: params.creatable,
        dropdownPosition: params.dropdownPosition,
        debounce: params.debounce,
        disabled: params.disabled,
        filterDataOnExactSearchMatch:
          params.filterDataOnExactSearchMatch,
        limit: params.limit,
        maxDropdownHeight: params.maxDropdownHeight,
        nothingFound: params.nothingFound,
        placeholder: params.placeholder,
        required: params.required,
        searchValue: params.searchValue,
        shadow: params.shadow,
        size: params.size,
        // style: params.style,
        // styles: params.styles,
        style: { width: params.column.actualWidth - 2, ...params.style },
        switchDirectionOnFlip: params.switchDirectionOnFlip,
        variant: params.variant,
      }),
      this.eInput
    );

    // allows focus event
    this.eInput.tabIndex = '0';

    // sets editor value to the value from the cell
    this.value = params.value;
  }

  // gets called once when grid ready to insert the element
  getGui() {
    return this.eInput;
  }

  focusChild() {
    // needed to delay and allow the component to render
    setTimeout(() => {
      var inp = this.eInput.getElementsByClassName(
        'mantine-Select-input'
      )[0];
      inp.tabIndex = '1';

      // disables keyboard event
      this.params.colDef.suppressKeyboardEvent = (params) => {
        const gridShouldDoNothing = params.editing;
        return gridShouldDoNothing;
      };
      // shows dropdown options
      inp.focus();
    }, 100);
  }

  // focus and select can be done after the gui is attached
  afterGuiAttached() {
    // stores the active cell
    this.prevFocus = document.activeElement;

    // adds event listener to trigger event to go into dash component
    this.eInput.addEventListener('focus', this.focusChild());

    // triggers focus event
    this.eInput.focus();
  }

  // returns the new value after editing
  getValue() {
    return this.value;
  }

  // any cleanup we need to be done here
  destroy() {
    // sets focus back to the grid's previously active cell
    this.prevFocus.focus();
  }
};
