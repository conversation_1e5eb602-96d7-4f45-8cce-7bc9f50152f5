from datetime import datetime, timedelta

import dash_mantine_components as dmc
import feffery_antd_components.alias as fac
import pandas as pd
from dash import ctx, no_update
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    ALL,
    Input,
    Output,
    State,
    callback,
    dash,
    dcc,
    html,
)

from common import get_user_info, read_sql
from tasks import bg_mail, task_express_delivery
from utils import db

dash.register_page(__name__, title="样制进度")

steps = {
    "smtstadate": "SMTStart",
    "smtfin_date": "SMTFinish",
    "dipstadate": "DIPStart",
    "fsdate_act": "FSFINISH",
    "findate_act": "Close",
}

styles = {
    "label": {
        "&[data-checked]": {
            "&, &:hover": {
                "backgroundColor": "indigo",
                "color": "white",
            },
        },
    }
}


def layout(prtno=None, **args):
    user = get_user_info()
    nt_name = user.get("nt_name").lower()
    area = user.get("area")
    sql = "select prtno from ssp.prt where smstatus in %s and area=%s"
    params = [["SMTFinish", "DIPStart", "FSFINISH", "sent"], area]
    prtnos = [i["prtno"] for i in db.execute(sql, params)]

    if not prtno:
        return dmc.Paper(
            [
                dmc.Divider(label="选择项目号", labelPosition="center", color="blue"),
                dmc.Group(
                    dmc.ChipGroup(
                        [dmc.Chip(i, value=i, styles=styles) for i in prtnos],
                        value=prtno,
                        id="prtnos",
                        multiple=False,
                    ),
                    justify="center",
                ),
            ],
            shadow="xl",
            p="xl",
        )

    prt = db.find_one("ssp.prt", {"prtno": prtno})
    fsqty = prt.get("fsqty")
    qty = prt.get("qty")
    smd_start = prt.get("smtstadate")
    if smd_start:
        smd_start = f"{smd_start:%y-%m-%d %H:%M}"

    sql = "select * from ssp.sm_work_record where prtno=%s and status!=%s"
    df = read_sql(sql, params=[prtno, "sent"])
    cqty = df["qty"].sum()
    batch = df.loc[df["qty"] > 0].shape[0] + 1

    batch_start_disabled = False
    batch_finish_disabled = False
    s1 = df.loc[df["batch"] == batch, "status"]
    if s1.isin(["BatchStart"]).any():
        batch_start_disabled = True
    if s1.isin(["BatchFinish"]).any():
        batch_finish_disabled = True

    disabled = [
        prt.get("smtstadate"),
        prt.get("smtfin_date"),
        prt.get("dipstadate"),
        prt.get("fsdate_act"),
        prt.get("findate_act"),
    ]
    try:
        idx = disabled.index(None)
    except ValueError:
        idx = None
    disabled = [True for i in disabled]
    if idx is not None:
        disabled[idx] = False

    part1 = dmc.SimpleGrid(
        [
            dmc.Button(
                dmc.Text("贴片开始", style={"white-space": "normal"}),
                id={"type": "status", "index": 0},
                variant="outline",
                disabled=disabled[0],
                # class_name="h-auto",
            ),
            dmc.Button(
                dmc.Text("贴片完成", style={"white-space": "normal"}),
                id={"type": "status", "index": 1},
                variant="outline",
                disabled=disabled[1],
                # class_name="h-auto",
            ),
            dmc.Button(
                dmc.Text("插件开始", style={"white-space": "normal"}),
                id={"type": "status", "index": 2},
                variant="outline",
                disabled=disabled[2],
                # class_name="h-auto",
            ),
            dmc.Button(
                dmc.Text("首样完成", style={"white-space": "normal"}),
                id={"type": "status", "index": 3},
                variant="outline",
                disabled=disabled[3],
                # class_name="h-auto",
            ),
            dmc.Button(
                dmc.Text("样制完成", style={"white-space": "normal"}),
                id={"type": "status", "index": 4},
                variant="outline",
                disabled=disabled[4],
                # class_name="h-auto",
            ),
        ],
        cols=2,
    )

    part2 = html.Div()
    part3 = html.Div()
    part4 = html.Div()

    if (cqty != 0) and (cqty < qty):
        part2 = dmc.Paper(
            [
                dmc.Divider(label="分批次插件", labelPosition="center"),
                dmc.Group(
                    [
                        dmc.Button(
                            f"第{batch}批插件开始",
                            id="batch-start",
                            color="green",
                            disabled=batch_start_disabled,
                        ),
                        dmc.Button(
                            f"第{batch}批插件完成",
                            id="batch-finish",
                            color="red",
                            disabled=batch_finish_disabled,
                        ),
                    ],
                    grow=True,
                ),
            ],
            shadow="xl",
            p="xl",
        )

    if area == "HZ":
        checkin_disabled = False
        checkout_disabled = False
        c1 = df["user"].str.lower() == nt_name
        c2 = df["prtno"] == prtno
        dfx = df.loc[c1 & c2]
        if not dfx.empty:
            s1 = dfx.loc[dfx["id"].idxmax()]
            if s1["status"] == "checkin":
                checkin_disabled = True

        part3 = dmc.Paper(
            [
                dmc.Divider(label="项目签入签出", labelPosition="center"),
                dmc.Group(
                    [
                        dmc.Button(
                            "签入",
                            id="checkin",
                            color="violet",
                            disabled=checkin_disabled,
                        ),
                        dmc.Button(
                            "签出",
                            id="checkout",
                            color="orange",
                            disabled=checkout_disabled,
                        ),
                    ],
                    grow=True,
                ),
            ],
            shadow="xl",
            p="xl",
        )
    if prtno.startswith(("HZSH", "WHSW")):
        part4 = dmc.Paper(
            [
                dmc.Divider(label="快递状态", labelPosition="center"),
                dmc.Button(
                    "快递已寄出", id="express-sent", color="orange", fullWidth=True
                ),
            ],
            shadow="xl",
            p="xl",
        )

    store = dcc.Store(
        id="scan-store",
        data={
            "id": prt.get("id"),
            "prtno": prtno,
            "batch": batch,
            "qty": qty,
            "cqty": cqty,
            "ee": prt.get("ee"),
            "pm": prt.get("pm"),
            "proj": prt.get("proj"),
            "dept": prt.get("dept"),
        },
    )

    layout = dmc.Container(
        dmc.Stack(
            [
                dmc.Center(
                    dmc.Text(prtno, size="xl", c="blue", fw="bold"),
                ),
                fac.Descriptions(
                    [
                        fac.DescriptionItem(smd_start, label="贴片开始日期", span=2),
                        fac.DescriptionItem(qty, label="样制数量", id="qty"),
                        fac.DescriptionItem(fsqty, label="首样数量"),
                        fac.DescriptionItem(cqty, label="已完成数量"),
                    ],
                    labelStyle={"fontWeight": "bold"},
                    size="small",
                    column=3,
                ),
                dmc.Paper(part1, shadow="xl", p="xl"),
                part2,
                part3,
                part4,
                dmc.Paper(
                    [
                        dmc.Divider(label="切换项目号", labelPosition="center"),
                        dmc.Group(
                            dmc.ChipGroup(
                                [dmc.Chip(i, value=i, styles=styles) for i in prtnos],
                                value=prtno,
                                id="prtnos",
                                multiple=False,
                            ),
                            justify="center",
                        ),
                    ],
                    shadow="xl",
                    p="xl",
                ),
                dmc.Modal(
                    [
                        dmc.Space(h=10),
                        dmc.TextInput(
                            label=dmc.Text("等待时间超过3天原因", c="red"),
                            id="start-delay-txt",
                        ),
                        dmc.Space(h=10),
                        dmc.Group(
                            dmc.Button("提交", id="start-delay-modal-submit"),
                            justify="right",
                        ),
                    ],
                    id="start-delay-modal",
                    title="等待时间异常",
                    size="xs",
                    centered=True,
                    closeOnEscape=False,
                    withCloseButton=False,
                    closeOnClickOutside=False,
                ),
                dmc.Modal(
                    [
                        dmc.NumberInput(
                            label="实际首样数量",
                            value=fsqty,
                            id="fsqty",
                            # style={"width": 250},
                        ),
                        dmc.Space(h=10),
                        dmc.TextInput(
                            label=dmc.Text("制作时间超过5天原因", c="red"),
                            id="over5d-txt",
                            display="none",
                        ),
                        dmc.Space(h=10),
                        dmc.Group(
                            dmc.Button("提交", id="scan-modal-submit"), justify="right"
                        ),
                    ],
                    id="fsqty-modal",
                    title="首样完成",
                    size="xs",
                    centered=True,
                    closeOnEscape=False,
                    withCloseButton=False,
                    closeOnClickOutside=False,
                ),
                dmc.Modal(
                    [
                        dmc.NumberInput(
                            label=f"第{batch}批完成数量",
                            id="batch-qty",
                        ),
                        dmc.Space(h=10),
                        dmc.Group(
                            dmc.Button("提交", id="batch-modal-submit"), justify="right"
                        ),
                    ],
                    id="batchy-modal",
                    title="插件完成",
                    size="xs",
                    centered=True,
                    closeOnEscape=False,
                    withCloseButton=False,
                    closeOnClickOutside=False,
                ),
                dmc.Modal(
                    [
                        dmc.NumberInput(
                            label="本次寄出数量",
                            id="express-qty",
                        ),
                        dmc.Space(h=10),
                        dmc.Group(
                            dmc.Button("提交", id="express-modal-submit"),
                            justify="right",
                        ),
                    ],
                    id="express-modal",
                    title="快递寄出",
                    size="xs",
                    centered=True,
                    closeOnEscape=False,
                    withCloseButton=False,
                    closeOnClickOutside=False,
                ),
                store,
            ],
            gap=5,
        )
    )
    return layout


@callback(
    Output({"type": "status", "index": ALL}, "disabled"),
    Input({"type": "status", "index": ALL}, "n_clicks"),
    State("scan-store", "data"),
    State("user", "data"),
)
def update_status(n, store, user):
    tid = ctx.triggered_id
    idx = tid.get("index")
    disable = [False if i == idx + 1 else True for i in range(5)]
    if idx == 3:
        return disable
    nt_name = user.get("nt_name").title()
    field = list(steps)[idx]
    status = steps[field]
    prtno = store.get("prtno")
    now = f"{datetime.now():%Y-%m-%d %H:%M:%S}"

    sql = f"update ssp.prt set smstatus=%s,{field}=%s where prtno=%s"
    params = [status, now, prtno]
    db.execute(sql, params)

    sql = "insert into ssp.sm_work_record(prtno,user,status) values(%s,%s,%s)"
    params = [prtno, nt_name, status]
    db.execute(sql, params)

    status = status.lower()
    if status == "smtstart":
        sql = "update stockout set StockOutDate2=%s,lable=%s \
            where prtno=%s and (StockOutDate2 is null or lable is null)"
        params = [now, now, prtno]
        db.execute(sql, params)

        prt = db.find_one("ssp.prt", {"prtno": prtno})

        if prt.get("dip_area") == "HZ":
            to = "<EMAIL>"
            subject = f"【插件备料通知】{prtno}开始贴片,请准备插件材料"
            bg_mail(to, subject, "")

        elif prt.get("dip_area") == "WH":
            to = "<EMAIL>"
            subject = f"【插件备料通知】{prtno}开始贴片,请准备插件材料"
            bg_mail(to, subject, "")

        to = "<EMAIL>;<EMAIL>"
        subject = f"【贴片开始通知】{prtno}开始贴片,请准备插件"
        dfb = pd.DataFrame([store])
        dfb["smtstart"] = now
        dfb = dfb.reindex(columns=["prtno", "dept", "proj", "smtstart", "qty"]).rename(
            columns={
                "dept": "部门",
                "proj": "机种名",
                "qty": "数量",
                "smtstart": "贴片开始时间",
                "prtno": "项目号",
            }
        )
        body = dfb.to_html(index=False)
        bg_mail(to, subject, body)

    elif status == "smtfinish":
        fsdate_sch = (
            datetime.now()
            + pd.offsets.BDay(3, normalize=True)
            + pd.Timedelta("23:59:59")
        )
        sql = "update ssp.prt set fsdate_sch=%s \
            where prtno=%s and (fsdate_sch<%s or fsdate_sch is null)"
        params = [fsdate_sch, prtno, fsdate_sch]
        db.execute(sql, params)

    return disable


@callback(
    Output("fsqty-modal", "opened"),
    Output("over5d-txt", "display"),
    Input({"type": "status", "index": ALL}, "n_clicks"),
    State("scan-store", "data"),
    State("user", "data"),
)
def open_over5d_modal(n, store, user):
    """首样完成超时触发"""
    idx = ctx.triggered_id.get("index")
    if idx != 3:
        raise PreventUpdate

    area = user.get("area")
    if area == "SH":
        lt = pd.offsets.Day(3)
    else:
        lt = pd.offsets.Day(5)

    prtno = store.get("prtno")

    sql = "select smtstadate from ssp.prt where prtno=%s"
    params = [prtno]
    res = db.execute_fetchone(sql, params)
    smtstadate = res["smtstadate"]
    now = datetime.now()

    if now > (smtstadate + lt):
        return True, "block"
    else:
        return True, "none"


@callback(
    Output("start-delay-modal", "opened"),
    Input({"type": "status", "index": ALL}, "n_clicks"),
    State("scan-store", "data"),
    State("user", "data"),
)
def open_start_delay_modal(n, store, user):
    idx = ctx.triggered_id.get("index")
    # * 贴片开始时触发
    if idx != 0:
        raise PreventUpdate
    prtno = store.get("prtno")
    if not prtno.startswith("HZHH"):
        raise PreventUpdate

    sql = "select pcbstatus,mat_ready_date,dip_mat_ready from ssp.prt where prtno=%s"
    params = [prtno]
    res = db.execute_fetchone(sql, params)
    s1 = pd.Series(res.values())
    s1 = pd.to_datetime(s1, errors="coerce")
    ready_date = s1.max().normalize()
    now = datetime.now().date()
    if now > ready_date + pd.offsets.Day(3):
        return True
    else:
        raise PreventUpdate


@callback(
    Output("start-delay-modal", "opened"),
    Input("start-delay-modal-submit", "n_clicks"),
    State("scan-store", "data"),
    State("start-delay-txt", "value"),
    State("user", "data"),
)
def start_delay_modal_submit(n, store, reason, user):
    """等待时间异常提交"""
    if not (n and reason):
        raise PreventUpdate
    prtno = store.get("prtno")
    nt_name = user.get("nt_name").title()

    sql = "update ssp.prt set findate_sch=CONCAT_WS(',',findate_sch,%s) where prtno=%s"
    params = [f"({nt_name}:等3天:{reason})", prtno]
    db.execute(sql, params)
    return False


@callback(
    Output("fsqty-modal", "opened"),
    Input("scan-modal-submit", "n_clicks"),
    State("over5d-txt", "value"),
    State("scan-store", "data"),
    State("user", "data"),
    State("fsqty", "value"),
    State("over5d-txt", "display"),
    State("qty", "children"),
)
def fsfinish_modal_submit(n, reason, store, user, fsqty, over5d_display, qty):
    if not n:
        raise PreventUpdate
    if over5d_display == "block":
        if not reason:
            raise PreventUpdate

    prtno = store.get("prtno")
    nt_name = user.get("nt_name").title()
    area = user.get("area")
    smstatus = "FSFINISH"

    if fsqty >= int(qty):
        if reason:
            sql = "update ssp.prt set fsqty=%s,completed_qty=%s,smstatus=%s,\
                fsdate_act=now(),findate_act=now(),findate_sch=CONCAT_WS(',',findate_sch,%s) \
                    where prtno=%s"
            params = [fsqty, fsqty, "Close", f"({nt_name}:超5天:{reason})", prtno]
        else:
            sql = "update ssp.prt set fsqty=%s,completed_qty=%s,smstatus=%s,\
                fsdate_act=now(),findate_act=now() where prtno=%s"
            params = [fsqty, fsqty, "Close", prtno]
    else:
        if reason:
            sql = "update ssp.prt set fsqty=%s,completed_qty=%s,smstatus=%s,\
                fsdate_act=now(),findate_sch=CONCAT_WS(',',findate_sch,%s) \
                    where prtno=%s"
            params = [fsqty, fsqty, "FSFINISH", f"({nt_name}:超5天:{reason})", prtno]
        else:
            sql = "update ssp.prt set fsqty=%s,completed_qty=%s,smstatus=%s,\
                fsdate_act=now() where prtno=%s"
            params = [fsqty, fsqty, "FSFINISH", prtno]

    db.execute(sql, params)
    db.insert(
        "ssp.sm_work_record",
        {
            "prtno": prtno,
            "status": smstatus,
            "user": nt_name,
            "qty": fsqty,
            "date": datetime.now(),
        },
    )
    ee = store.get("ee")
    pm = store.get("pm")
    to = [nt_name, ee, pm]
    if area == "HZ":
        to.append("weiming.li")
    to = ";".join(f"{i}@deltaww.com" for i in to)
    proj = store.get("proj")
    subject = (
        f"【样制完成通知】{prtno}:{proj}共{qty}台,完成{fsqty}台,请至样制间取走样品"
    )

    bg_mail(to, subject)
    return False


@callback(
    Output("batch-start", "disabled"),
    Input("batch-start", "n_clicks"),
    State("scan-store", "data"),
    State("user", "data"),
)
def batch_start(n, store, user):
    prtno = store.get("prtno")
    batch = store.get("batch")
    nt_name = user.get("nt_name")
    if not n:
        raise PreventUpdate

    db.insert(
        "ssp.sm_work_record",
        {
            "prtno": prtno,
            "status": "BatchStart",
            "user": nt_name,
            "batch": batch,
            "date": datetime.now(),
        },
    )
    return True


@callback(
    Output("batch-finish", "disabled"),
    Output("batchy-modal", "opened"),
    Input("batch-finish", "n_clicks"),
)
def batch_finish(n):
    if not n:
        raise PreventUpdate
    return True, True


@callback(
    Output("batchy-modal", "opened"),
    Output({"type": "status", "index": 4}, "n_clicks"),
    Input("batch-modal-submit", "n_clicks"),
    State("batch-qty", "value"),
    State("scan-store", "data"),
    State("user", "data"),
)
def batch_modal_submit(n, batch_qty, store, user):
    if not n:
        raise PreventUpdate
    if not batch_qty:
        raise PreventUpdate
    prtno = store.get("prtno")
    batch = store.get("batch")
    nt_name = user.get("nt_name")

    db.insert(
        "ssp.sm_work_record",
        {
            "prtno": prtno,
            "status": "BatchFinish",
            "user": nt_name,
            "batch": batch,
            "qty": batch_qty,
            "date": datetime.now(),
        },
    )
    sql = "update ssp.prt set completed_qty=completed_qty+%s where prtno=%s"
    db.execute(sql, [batch_qty, prtno])
    cqty = store.get("cqty")
    qty = store.get("qty")
    if batch_qty + cqty >= qty:
        return False, 1
    return False, no_update


@callback(
    Output("checkin", "disabled"),
    Input("checkin", "n_clicks"),
    State("scan-store", "data"),
    State("user", "data"),
)
def check_in(n, store, user):
    if not n:
        raise PreventUpdate

    db.insert(
        "ssp.sm_work_record",
        {
            "prtno": store.get("prtno"),
            "status": "checkin",
            "user": user.get("nt_name"),
            "date": datetime.now(),
        },
    )
    return True


@callback(
    Output("checkout", "disabled"),
    Input("checkout", "n_clicks"),
    State("scan-store", "data"),
    State("user", "data"),
)
def check_out(n, store, user):
    if not n:
        raise PreventUpdate

    db.insert(
        "ssp.sm_work_record",
        {
            "status": "checkout",
            "prtno": store.get("prtno"),
            "user": user.get("nt_name"),
            "date": datetime.now(),
        },
    )
    return True


@callback(
    Output("express-modal", "opened"),
    Output("express-sent", "disabled"),
    Output("express-qty", "value"),
    Input("express-sent", "n_clicks"),
    State("scan-store", "data"),
)
def open_express_modal(n, store):
    if not n:
        raise PreventUpdate
    qty = store.get("qty")
    return True, True, qty


@callback(
    Output("express-modal", "opened"),
    Input("express-modal-submit", "n_clicks"),
    State("express-qty", "value"),
    State("scan-store", "data"),
    State("user", "data"),
)
def express_sent_submit(n, qty, store, user):
    if not n:
        raise PreventUpdate
    if not qty:
        raise PreventUpdate
    nt_name = user.get("nt_name")
    prtno = store.get("prtno")
    now = datetime.now()

    db.insert(
        "ssp.sm_work_record",
        {"status": "sent", "prtno": prtno, "user": nt_name, "date": now, "qty": qty},
    )
    sql = "update ssp.prt set express_date=%s,express_qty=express_qty+%s where id=%s"
    db.execute(sql, [now, qty, store.get("id")])
    express_date = now - timedelta(minutes=10)
    task_express_delivery.schedule(args=(nt_name, express_date), delay=10 * 60)
    return False


@callback(
    Output("url", "search"),
    Input("prtnos", "value"),
)
def change_prtno(prtno):
    if not prtno:
        raise PreventUpdate
    return f"?prtno={prtno}"
