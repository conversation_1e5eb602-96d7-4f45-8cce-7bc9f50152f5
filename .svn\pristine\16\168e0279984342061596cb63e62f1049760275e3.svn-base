# -*- coding: utf-8 -*-
import dash_ag_grid as dag
import dash_mantine_components as dmc
import pandas as pd
from dash import dcc, html

from common import id_factory, read_sql, get_db
# from dbtool import db

id = id_factory(__name__)

check_items = [
    "物质成分",
    "操作温度",
    "Lifetime/TCT",
    "封装",
    "Reflow",
    "耐燃等级",
    "产地调查",
    "国产厂商和替代料",
    "材料属性:HF/WS/ESD/MSL/LF",
    "材料属性:ANTI-S",
    "材料属性:双85",
    "工业等级",
    "Rohs",
    "Automotive Parts Survey Form",
    "重量调查",
    "X-Ray Sensitivity Survey",
    "ECCN",
]

# columnDefs = [
#     {
#         "headerName": "BOM信息",
#         "children": [
#             {
#                 "field": "deltapn",
#                 "headerName": "料号",
#             },
#             {
#                 "field": "des",
#                 "headerName": "描述",
#                 "columnGroupShow": "open",
#             },
#             {
#                 "field": "mfgname",
#                 "headerName": "厂商",
#                 "columnGroupShow": "open",
#             },
#             {
#                 "field": "mfgpn",
#                 "headerName": "厂商料号",
#                 "columnGroupShow": "open",
#             },
#         ],
#     },
# ]

# columnDefs1 = [
#     {
#         "headerName": "LifeTime",
#         "field": "life_time",
#         "children": [
#             {
#                 "headerName": "Life",
#                 "field": "life",
#                 "editable": True,
#             },
#             {
#                 "headerName": "Tct",
#                 "field": "tct",
#                 "columnGroupShow": "open",
#                 "editable": True,
#             },
#             {
#                 "headerName": "life_tct",
#                 "field": "life_tct",
#                 "columnGroupShow": "open",
#                 "editable": True,
#             },
#         ],
#     },
#     {
#         "headerName": "操作温度",
#         "field": "operation_temp",
#         "children": [
#             {
#                 "headerName": "ta_min",
#                 "field": "ta_min",
#                 "editable": True,
#             },
#             {
#                 "headerName": "ta_max",
#                 "field": "ta_max",
#                 "columnGroupShow": "open",
#                 "editable": True,
#             },
#         ],
#     },
#     {
#         "headerName": "耐燃等级",
#         "field": "flammability",
#         "children": [
#             {
#                 "headerName": "flammability",
#                 "field": "flammability",
#                 "editable": True,
#             },
#         ],
#     },
#     {
#         "headerName": "AutoMotive",
#         "field": "auto_motive",
#         "children": [
#             {
#                 "headerName": "device_sop_date",
#                 "field": "device_sop_date",
#                 "editable": True,
#                 # "suppressSizeToFit": True,
#             },
#             {
#                 "headerName": "device_total_volume",
#                 "field": "device_total_volume",
#                 "columnGroupShow": "open",
#                 "editable": True,
#                 # "suppressSizeToFit": True,
#             },
#             {
#                 "headerName": "process_sop_date",
#                 "field": "process_sop_date",
#                 "columnGroupShow": "open",
#                 "editable": True,
#                 # "suppressSizeToFit": True,
#             },
#             {
#                 "headerName": "process_total_volume",
#                 "field": "process_total_volume",
#                 "columnGroupShow": "open",
#                 "editable": True,
#                 # "suppressSizeToFit": True,
#             },
#         ],
#     },
# ]

columnDefs = [
    {"field": "deltapn", "headerName": "deltapn", "width": 120},
    {"field": "des", "headerName": "des"},
    {"field": "mfgname", "headerName": "mfgname", "width": 100},
    {"field": "mfgpn", "headerName": "mfgpn"},
]

columnDefs1 = [
    {"headerName": "Life", "field": "life", "editable": True},
    {"headerName": "Tct", "field": "tct", "editable": True},
    {
        "headerName": "Y/N(life >=1000h or TCT>=1000)",
        "field": "y_n",
        "editable": True,
        "cellEditor": {"function": "DMC_Select"},
        "cellEditorParams": {
            "options": ["Y", "N"],
            "clearable": True,
            "shadow": "xl",
        },
        "cellEditorPopup": True,
    },
    {"headerName": "Ta Min", "field": "ta_min", "editable": True},
    {"headerName": "Ta Max", "field": "ta_max", "editable": True},
    {"headerName": "Flammability", "field": "flammability", "editable": True},
    {
        "headerName": "as an identical device-SOP date",
        "field": "device_sop_date",
        "editable": True,
    },
    {
        "headerName": "as an identical device-Total volume",
        "field": "device_total_volume",
        "editable": True,
    },
    {
        "headerName": "as an identical process-SOP date",
        "field": "process_sop_date",
        "editable": True,
    },
    {
        "headerName": "as an identical process-Total volume",
        "field": "process_total_volume",
        "editable": True,
    },
]

rowClassRules = {
    "bg-danger": "params.data.flammability == '' || params.data.life == '' || params.data.tct == '' || params.data.y_n == '' || params.data.ta_min == '' || params.data.ta_max == ''"
}


def generate_table(items, data):
    df = pd.DataFrame(data)
    model = df["model"].iloc[0]
    df = df.groupby("deltapn", as_index=False).agg(
        {"des": "first", "mfgname": "first", "mfgpn": "first"}
    )

    columnDefs_x = []

    for db_name in items:
        sql = f"select * from ce.{db_name} where deltapn in %s"
        params = [df["deltapn"].tolist()]
        dfi = read_sql(sql, params=params)
        cols = dfi.columns.difference(
            [
                "des",
                "mfgname",
                "mfgpn",
                "remark",
                "type",
                "id",
                "create_time",
                "update_time",
                "order",
            ]
        )
        columnDefs_x.extend([i for i in columnDefs1 if i["field"] in cols])
        dfi = dfi.reindex(columns=cols)
        df = df.merge(dfi, on="deltapn", how="left")

    df = df.fillna("")
    dfu = df[df.columns.difference(["deltapn", "des", "mfgname", "mfgpn"])]
    # breakpoint()
    unfinished_count = (dfu.fillna("") == "").any(axis=1).sum()
    # unfinished_count = dfu.isna().any(axis=1).sum()
    unfinished = dmc.Badge(
        f"未完成笔数:{unfinished_count}", color="orange", id=id("unfinished")
    )

    table = dag.AgGrid(
        id=id("table"),
        className="ag-theme-quartz",
        rowData=df.to_dict(orient="records"),
        columnDefs=columnDefs + columnDefs_x,
        # columnSize="sizeToFit",
        defaultColDef={
            "resizable": True,
            # "initialWidth": 160,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
            # "editable": True,
        },
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "suppressScrollOnNewData": True,
        },
        style={"height": 300},
        csvExportParams={
            "fileName": f"{model}.csv",
        },
        rowClassRules=rowClassRules,
        # enableEnterpriseModules=True,
    )
    return html.Div([table, unfinished])


def form_part_1(task, survey, sub_type):
    items = survey[0].get("survey_items").split(",")
    # breakpoint()
    if sub_type == "汇总":
        download_disabled = False
    else:
        download_disabled = True
    # check = [j for i, j in enumerate(check_items) if f"{i}" in items]

    form_part_1 = html.Div(
        [
            dmc.Stack(
                [
                    dmc.Center(dmc.Text("材料调查表", weight=700)),
                    dmc.Divider(),
                    dmc.Paper(
                        [
                            dmc.Group(
                                [
                                    dmc.TextInput(
                                        label="部门",
                                        # withAsterisk=True,
                                        size="xs",
                                        value=task.get("dept"),
                                    ),
                                    dmc.TextInput(
                                        label="机种名",
                                        size="xs",
                                        value=survey[0].get("model"),
                                        id=id("model"),
                                    ),
                                    dmc.TextInput(
                                        label="客户",
                                        size="xs",
                                        value=survey[0].get("customer"),
                                        id=id("customer"),
                                    ),
                                    dmc.TextInput(
                                        label="工程师",
                                        size="xs",
                                        value=task.get("applicant"),
                                    ),
                                ],
                                spacing=0,
                                align="end",
                                grow=True,
                            ),
                            dmc.Group(
                                [
                                    dmc.CheckboxGroup(
                                        [
                                            dmc.Checkbox(
                                                label=i,
                                                value=i,
                                                disabled=True,
                                                checked=True,
                                                color="green",
                                            )
                                            for i in items
                                        ],
                                        # label="调查项:",
                                        value=items,
                                        id=id("survey-items"),
                                    ),
                                ],
                                align="end",
                            ),
                            dmc.Space(h=10),
                            generate_table(items, survey),
                        ],
                        withBorder=True,
                        shadow="xs",
                        p="xs",
                        style={"background-color": "#f1f5f8"},
                    ),
                ]
            ),
            dmc.Space(h=5),
            dmc.Group(
                [
                    # dmc.Button("更新", id=id("update"), color="green"),
                    dmc.Button("更新", id=id("submit")),
                    dmc.Button(
                        "下载",
                        id=id("download-btn"),
                        color="orange",
                        disabled=download_disabled,
                    ),
                    dmc.Button(
                        "结案",
                        id=id("close"),
                        color="green",
                        disabled=download_disabled,
                    ),
                    dcc.Download(id=id("download")),
                ],
                spacing="xl",
                position="apart",
            ),
            # dmc.Space(h=5),
        ]
    )
    return form_part_1


def layout(**query):
    task_id = query.get("task")
    db = get_db()
    task = db.find_one("ce.task", {"id": task_id})
    sub_type = task.get("sub_type")

    if sub_type == "汇总":
        survey = db.execute(
            "select * from ce.survey where model_id=%s", (task["model_id"],)
        )
    else:
        survey = db.execute("select * from ce.survey where task_id=%s", (task_id,))

    layout = dmc.Container(
        dmc.Stack(
            [
                form_part_1(task, survey, sub_type),
                dcc.Store(id=id("model_id"), data=task.get("model_id")),
                dcc.Store(id=id("update_rows"), data=[]),
            ]
        )
    )
    return layout
