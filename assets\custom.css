:root {
    --TURQUOISE: #1abc9c;
    --GREENSEA: #16a085;
    --SUNFLOWER: #f1c40f;
    --ORANGE: #f39c12;
    --EMERALD: #2ecc71;
    --NEPHRITIS: #27ae60;
    --CARROT: #e67e22;
    --PUMPKIN: #d35400;
    --PETERRIVER: #3498db;
    --BELIZEHOLE: #2980b9;
    --ALIZARIN: #e74c3c;
    --POMEGRANATE: #c0392b;
    --AMETHYST: #9b59b6;
    --WISTERIA: #8e44ad;
    --WETASPHALT: #34495e;
    --MIDNIGHTBLUE: #2c3e50;
    --CONCRETE: #95a5a6;
    --ASBESTOS: #7f8c8d;
    --LIGHTGREY: #ecf0f1;
}

.index-func {
    color: white;
    font-size: 8;
    border-radius: 8px;
    height: auto;
    text-align: center;
}

.index-1-func {
    background: var(--TURQUOISE);
}

.index-1-func:hover {
    background: var(--G<PERSON>ENSEA);
}

.index-2-func {
    background: var(--SUNFLOWER);
}

.index-2-func:hover {
    background: var(--ORANGE);
}

.index-3-func {
    background: #9b59b6;
}

.index-3-func:hover {
    background: #8e44ad;
}

.index-4-func {
    background: #2ecc71;
}

.index-4-func:hover {
    background: #27ae60;
}

.index-5-func {
    background: #e67e22;
}

.index-5-func:hover {
    background: #d35400;
}

.index-6-func {
    background: #3498db;
}

.index-6-func:hover {
    background: #2980b9;
}

.index-7-func {
    background: #e74c3c;
}

.index-7-func:hover {
    background: #c0392b;
}

.index-8-func {
    background: #6547ac;
}

.index-8-func:hover {
    background: #6547ac;
}

.index-9-func {
    background: #d35400;
}

.index-9-func:hover {
    background: #d35400;
}

.index-10-func {
    background: #1aa52c;
}

.index-10-func:hover {
    background: #1aa52c;
}

.index-func-font {
    font-size: 30px;
    font-family: Microsoft YaHei;
    font-weight: 600;
    color: white;

}

.index-func-font:hover {
    color: white;
    text-decoration: none;
}

/* .index-func-font-min{
    font-size:15px;
    font-family:Microsoft YaHei;
    color:white;
}
.index-func-font-min:hover{
    color:white;
    text-decoration: none;
} */
/* .index-nav-font{
    font-size:25px;
    font-family:Helvetica;
} */
/* .index-nt-font{
    font-size:20px;
    font-family:Helvetica;
} */
.index-icon {
    height: 100px;
    width: 100px;
}

/* .menuitem-font>a{
    font-size:16px;
    color:#34495e;
}
.menuitem-font:hover a{
    color:white;
    text-decoration: none;
} */
.card-filter {
    -webkit-filter: brightness(1) contrast(1.15);
    filter: brightness(1) contrast(1.15);
}

/* .tools-navbtn{
    color:#1abc9c;
    background:white;
    border: none;
    width:auto;
    display:inline-block;
} */
/* .tools-navbtn:hover{
    color:white;
    background:#16a085;
    border: #16a085;
} */

/* .col-new-5 {
    position: relative;
    min-height: 1px;
    padding-right: 10px;
    padding-left: 10px;
    width: 20%;
    float: left;
} */
/* .tab-label-active{
    color:white;
    background:white;
}
.tab-label-active:active{
    color:red;
}
.tab-label-active:hover{
    color:blue;
} */
.Select-menu-outer {
    display: block !important;
    --accent: black;
}

.card-columns {
    column-count: 2;
    -moz-column-count: 2;
    /* Firefox */
    -webkit-column-count: 2;
    /* Safari and Chrome */
}

.index-nav-link {
    font-weight: bolder;
}

/* ::-webkit-scrollbar
{
    width: 0;
} */
/* my.py左右弹性布局 */
.my-parent {
    display: flex;
}

.my-left {
    flex: 0 0 auto;
    height: 100%;
    /* background-color: #2c3e50; */
    /* padding:2rem,1rem;
    margin-left:8rem; */
    color: white;
}

.my-right {
    display: flex;
    flex: 1;
    height: 100%;
    /* margin-left:10px; */
}

.my-color {
    font-size: 17px;
}

.my-color:hover {
    color: #16a085 !important;
    cursor: pointer;
}

.pie {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: #2ecc71;
    background-image: linear-gradient(to right, transparent 50%, #ecf0f1 0);
}

.pie::before {
    content: '';
    display: block;
    margin-left: 50%;
    height: 100%;
    border-radius: 0 100% 100% 0/50%;
    background-color: inherit;
    transform-origin: left;
    /* transform: rotate(-1turn); */
    transform: rotateZ(270deg);
}

.my-loading {
    margin: 100px auto;
    width: 8rem;
    height: 8rem;
    position: relative;
}

.my-loading .my-progress {
    position: absolute;
    width: 6rem;
    height: 6rem;
    background-color: white;
    border-radius: 50%;
    left: 1rem;
    top: 1rem;
    line-height: 6rem;
    text-align: center;
}

.my-cir-left,
.my-cir-right {
    width: 4rem;
    height: 8rem;
    overflow: hidden;
    position: relative;
    float: left;
    background-color: #ecf0f1;
}

.my-cir-left {
    border-radius: 8rem 0 0 8rem;
}

.my-cir-right {
    border-radius: 0 8rem 8rem 0;
}

.my-cir-left:after,
.my-cir-right:after {
    content: "";
    position: absolute;
    display: block;
    width: 4rem;
    height: 8rem;
    background-color: white;
    border-radius: 8rem 0 0 8rem;
    background-color: #2ecc71;
}

.my-cir-right:after {
    content: "";
    position: absolute;
    display: block;
    border-radius: 0 8rem 8rem 0;
}

.my-cir-left:after {
    transform-origin: right center;
    transform: rotateZ(-180deg);
}

.my-cir-right:after {
    transform-origin: left center;
}

.DateInput_input,
.DateInput_input_1 {
    height: 35px;
    font-size: 15px;
}

#dash-uploader-padding {
    min-height: 34px;
    line-height: 34px;
    height: 34px;
    transform: translateY(-20%);
}

#meeting-uploader-padding {
    min-height: 20px;
    line-height: 20px;
    height: 20px;
    transform: translateY(-20%);
    color: #16a085;
}

.resumable-default,
.resumable-hovered,
.resumable-uploading,
.resumable-complete {
    line-height: 0px !important;
    min-height: 100% !important;
    border-color: #16a085;
    /* width: fit-content !important; */
    /* margin-top: -18px; */
    /* margin-left: -70px; */
}

/* my.py进度条布局 */
.order_status {
    height: 110px;
    padding-top: 60px;
    padding-left: 80px;
    width: 100%;
    /* background: blue; */
}

/* s-step--底色 */
.order_status .s-step {
    float: left;
    width: 150px;
    height: 60px;
    position: relative;
    /* background: red; */
}

.order_status .s-step>b {
    display: block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    border-radius: 40px;
    position: absolute;
    margin-left: -20px;
    top: -20px;
    background: #ccc;
    z-index: 87
}

.order_status .s-step>b>b {
    display: block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    border-radius: 40px;
    background: #ccc;
    margin-top: 0px;
    margin-left: 0px;
    z-index: 88
}

.order_status .s-step>p {
    width: 135px;
    border: 1px dashed #ddd;
    top: 0px;
    position: absolute;
    z-index: 86
}

.order_status .s-step em {
    display: block;
    padding-top: 20px;
    font-style: normal;
    text-align: center;
    color: #b7b7b7;
    font-size: 12px;
    margin-left: -65px;
    width: 130px;
    visibility: hidden;
}

.order_status .s-step>div {
    display: block;
    width: 80px;
    height: 32px;
    line-height: 32px;
    position: absolute;
    margin-left: -40px;
    top: -55px;
    z-index: 87;
    text-align: center;
    font-size: 14px;
    color: #c9c4c4;
}

/* s-step--正在进行，黄色 */
.order_status .s-process {
    float: left;
    width: 150px;
    height: 60px;
    position: relative;
}

.order_status .s-process>b {
    display: block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    border-radius: 40px;
    position: absolute;
    margin-left: -20px;
    top: -20px;
    background: #ccc;
    z-index: 87
}

.order_status .s-process>b>b {
    display: block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    border-radius: 40px;
    margin-top: 0px;
    margin-left: 0px;
    z-index: 88;
    background-color: #f1c40f;
    animation: fade 1s infinite;
}

.order_status .s-process>p {
    width: 135px;
    border: 1px dashed #f1c40f;
    top: 0px;
    position: absolute;
    z-index: 86
}

.order_status .s-process>em {
    display: block;
    padding-top: 20px;
    font-style: normal;
    text-align: center;
    color: #b7b7b7;
    font-size: 12px;
    margin-left: -65px;
    width: 130px;
    visibility: hidden;
}

.order_status .s-process>div {
    display: block;
    width: 80px;
    height: 32px;
    line-height: 32px;
    position: absolute;
    margin-left: -40px;
    top: -55px;
    z-index: 87;
    text-align: center;
    font-size: 14px;
    color: #f1c40f
}

@keyframes fade {
    from {
        opacity: 1.0;
    }

    25% {
        opacity: 0.25;
    }

    50% {
        opacity: 0.5;
    }

    75% {
        opacity: 0.75;
    }

    to {
        opacity: 1.0;
    }
}

/*-------------- s-done--已完成-绿色 --------------*/
.order_status .s-done {
    float: left;
    width: 150px;
    height: 60px;
    position: relative;
}

.order_status .s-done>b {
    display: block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    border-radius: 40px;
    position: absolute;
    margin-left: -20px;
    top: -20px;
    background: #2ecc71;
    z-index: 87
}

.order_status .s-done>b>b {
    background-color: #2ecc71;
    /* animation:fade 1s infinite; */
}

.order_status .s-done>b>b:after,
.order_status .s-done>b>b:before {
    content: '';
    height: 28px;
    width: 6px;
    display: block;
    background: #fff;
    position: absolute;
    top: 6px;
    left: 20px;
    border-radius: 10px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    -ms-transform: rotate(45deg);

}

.order_status .s-done>b>b:before {
    content: '';
    height: 12px;
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    top: 18px;
    left: 8px;
}

.order_status .s-done>p {
    width: 135px;
    border: 1px dashed #2ecc71;
    top: 0px;
    position: absolute;
    z-index: 86
}

.order_status .s-done em {
    display: block;
    padding-top: 20px;
    font-style: normal;
    text-align: center;
    color: #2ecc71;
    font-size: 12px;
    margin-left: -65px;
    width: 130px;
    /* visibility: hidden; */
}

.order_status .s-done>div {
    display: block;
    width: 80px;
    height: 32px;
    line-height: 32px;
    position: absolute;
    margin-left: -40px;
    top: -55px;
    z-index: 87;
    text-align: center;
    font-size: 14px;
    color: #2ecc71
}

/*-------------- s-open--未完成-红色 --------------*/
.order_status .s-open {
    float: left;
    width: 150px;
    height: 60px;
    position: relative;
}

.order_status .s-open>b {
    display: block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    border-radius: 40px;
    position: absolute;
    margin-left: -20px;
    top: -20px;
    background: #e74c3c;
    z-index: 87
}

.order_status .s-open>b>b {
    background-color: #e74c3c;
    /* animation:fade 1s infinite; */
}

.order_status .s-open>b>b:after,
.order_status .s-open>b>b:before {
    content: '';
    height: 6px;
    width: 28px;
    display: block;
    background: #fff;
    border-radius: 10px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    position: absolute;
    top: 17px;
    left: 6px;
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
}

.order_status .s-open>b>b:before {
    content: '';
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
}

.order_status .s-open>p {
    width: 135px;
    border: 1px dashed #e74c3c;
    top: 0px;
    position: absolute;
    z-index: 86
}

.order_status .s-open em {
    display: block;
    padding-top: 20px;
    font-style: normal;
    text-align: center;
    color: #e74c3c;
    font-size: 12px;
    margin-left: -65px;
    width: 130px;
    visibility: hidden;
}

.order_status .s-open>div {
    display: block;
    width: 80px;
    height: 32px;
    line-height: 32px;
    position: absolute;
    margin-left: -40px;
    top: -55px;
    z-index: 87;
    text-align: center;
    font-size: 14px;
    color: #e74c3c
}

.my-width {
    width: 100%;
}

.my-width>div>div>div {
    padding-left: 20px;
    padding-right: 17px;
}

.cell-markdown>p a {
    padding-top: 30px;
}

@font-face {
    font-family: 'iconfont';
    /* Project id 2055539 */
    src: url('//at.alicdn.com/t/font_2055539_13kmg90u24we.woff2?t=1631757020678') format('woff2'),
        url('//at.alicdn.com/t/font_2055539_13kmg90u24we.woff?t=1631757020678') format('woff'),
        url('//at.alicdn.com/t/font_2055539_13kmg90u24we.ttf?t=1631757020678') format('truetype');
}

.iconfont-star {
    font-size: 25px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.cleanfloat::after {
    display: block;
    clear: both;
    content: "";
    visibility: hidden;
    height: 0;
}

/*清浮动*/
.rate-star {
    font-family: "iconfont" !important;
    list-style: none;
    float: left;
    font-size: 30px;
    margin: 4px;
    color: #ccc;
    cursor: pointer;
}

/*五角星样式*/
/* .rate-star:hover{
    color:#f1c40f;
} */
.hs,
.cs {
    color: #f1c40f;
}

/*五角星点击后样式*/
.like-all {
    float: left;
    color: #ccc;
    cursor: pointer;
}

.like-all:hover {
    color: #e74c3c;
}

.like-style {
    font-family: "iconfont" !important;
    list-style: none;
    float: left;
    font-size: 28px;
    transform: rotateY(180deg);
    position: relative;
    bottom: 3px;
}

/*点赞样式*/
.like-font {
    list-style: none;
    font-size: 18px;
    float: left;
    position: relative;
    bottom: -8px;
}

.like-addclass {
    color: #e74c3c;
}

.dislike-all {
    float: left;
    color: #ccc;
    cursor: pointer;
}

.dislike-all:hover {
    color: #34495e;
}

.dislike-style {
    font-family: "iconfont" !important;
    list-style: none;
    font-size: 28px;
    float: left;
    position: relative;
    bottom: -6px;
}

/*点赞样式*/
.dislike-font {
    list-style: none;
    font-size: 18px;
    float: left;
    position: relative;
    bottom: -8px;
    width: 260px;
}

.dislike-addclass {
    color: #34495e;
}

/*清除滚动条抖动*/
html {
    overflow-y: scroll;
}

:root {
    overflow-y: auto;
    overflow-x: hidden;
}

:root body {
    position: absolute;
}

body {
    width: 100vw;
    /* overflow: hidden; */
}

/* 满意度调查collapse文字样式 */
.satisfy-sub-font {
    font-size: 18px;
    align-self: center;
    color: #2c3e50;
    font-weight: bold;
    width: 120px;
}

.satisfy-sub-icon-a {
    font-size: 28px;
    align-self: center;
    font-family: iconfont;
    color: #1abc9c;
}

.satisfy-sub-icon-b {
    font-size: 28px;
    align-self: center;
    font-family: iconfont;
    color: #e74c3c;

}

.satisfy-sub-icon-c {
    font-size: 28px;
    align-self: center;
    font-family: iconfont;
    color: #3498db;
}

.satisfy-sub-icon-d {
    font-size: 28px;
    align-self: center;
    font-family: iconfont;
    color: #e67e22;
}

.satisfy-sub-icon-e {
    font-size: 28px;
    align-self: center;
    font-family: iconfont;
    color: #2ecc71;
}

.bell-bot {
    position: relative;
    padding: 0px;
    margin-top: 6px;
}

.bell-bot-li {
    display: block;
    background: #e74c3c;
    border-radius: 50%;
    width: 7px;
    height: 7px;
    top: 0px;
    right: 0px;
    position: absolute;
}

.dash-table-container .row {
    display: inline-block;
    margin: 0;
}

/*评分进度条样式*/
.progress-con {
    width: 100%;
    /* background-color: #ffe680; */
    /* font-size: 10px; */
}

.progress-all {
    /* text-align: right; */
    /* padding-right: 20px; */
    height: 13px;
    margin-top: 6px;
    /* color: #ffe680; */
    background-color: #f1c40f;
}

.sa-circle {
    width: 140px;
    height: 140px;
    border-radius: 50%;
    color: white;
    /* line-height: 165px; */
    text-align: center;
    background: #34495e;
    display: inline-block;
}

.sa-star {
    font-family: "iconfont" !important;
    list-style: none;
    float: left;
    font-size: 17px;
    margin-right: 4px;
    color: #ccc;
    cursor: pointer;
}

/*五角星样式*/
.wh-star {
    font-family: "iconfont" !important;
    list-style: none;
    float: left;
    font-size: 17px;
    margin-right: 4px;
    color: white;
    cursor: pointer;
}

/*五角星白*/
.personal-center {
    color: white;
}

.personal-center:hover {
    color: #16a085;
    text-decoration: none;
}

.line-limit-length {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.popover-body-style {
    border-style: none;
    overflow-x: hidden;
    overflow-y: auto;
    height: 400px;
}

#issue-type>div {
    width: 110px;
}

/* #issue-date-picker {
    width: 120px;
    border-color: pink;
}
#issue-date-picker > div {
    width:120px;
    border-color: seagreen;
}
#issue-date-picker > div >div {
    width:120px;
    border-color: tomato;
} */
#issue-date-picker>div>div>div {
    width: 120px;
    border-radius: 0px 4px 4px 0px;
}

#issue-date-picker>div>div>div>div {
    width: 115px;
}

#issue-date-picker>div>div>div>div>input {
    width: 115px;
}

.handle-events {
    pointer-events: none;
    cursor: default;
}

/* ---------上传附件样式------------- */
#spe-uploader {
    flex: 1;
    border-radius: 4px !important;
    border-color: #ced4da;
    line-height: 1.8 !important;
    min-height: 100% !important;
    margin-left: 0 !important;
    margin-top: 5px;
}

#spe-uploader-padding {
    margin: 0rem !important;
    padding: 0rem !important;
}

#spe-uploader-padding label {
    margin-bottom: 0;
    color: #ced4da;
}

#spe-form-upload {
    flex: 1;
    border-radius: 4px !important;
    border-color: #ced4da;
    line-height: 1.8 !important;
    min-height: 100% !important;
    margin-left: 0 !important;
    margin-top: 5px;
}

#spe-form-upload-padding {
    margin: 0rem !important;
    padding: 0rem !important;
}

#spe-form-upload-padding label {
    margin-bottom: 0;
    color: #ced4da;
}

#sp-form-upload {
    flex: 1;
    border-radius: 4px !important;
    border-color: #ced4da;
    line-height: 1.8 !important;
    min-height: 100% !important;
    margin-left: 0 !important;
    margin-top: 5px;
}

#sp-form-upload-padding {
    margin: 0rem !important;
    padding: 0rem !important;
}

#sp-form-upload-padding label {
    margin-bottom: 0;
    color: #ced4da;
}

.date-picker>div>div>div {
    width: 321.8px;
    line-height: 1.8 !important;
    border-radius: 4px !important;
}

.date-picker>div>div>div>div {
    width: auto;
    border-radius: 4px !important;
}

.date-picker>div>div>div>div>input {
    width: 260px;
    border-radius: 4px 4px 0px 0px !important;
}

.date-picker-short>div>div>div {
    width: 141.5px;
    line-height: 1.8 !important;
    border-radius: 0px 4px 4px 0px;
}

.date-picker-short>div>div>div>div {
    width: auto;
}

.date-picker-short>div>div>div>div>input {
    width: 130px;
}

/* #query-table .dash-select-cell input {
    background-color: yellowgreen;
    color: tomato;
} */

/* .material-table-mask {
    background-color: tomato;
    width: 100px;
    height: 100px;
    position: relative;
    left: 20px;
    top: -20px;
} */
/* ul li{
    list-style-type: none;
}
.drawer-button {
	float: left;
	min-width: 90px;
	max-width: 150px;
	display: block;
	margin: 0em;
	padding: 0.5em 2em;
	border: none;
	background: none;
	color: inherit;
	vertical-align: middle;
	position: relative;
	z-index: 1;
	-webkit-backface-visibility: hidden;
	-moz-osx-font-smoothing: grayscale;
}
.drawer-button:focus {
	outline: none;
}
.drawer-button > span {
	vertical-align: middle;
}
.button--ujarak {
	-webkit-transition: border-color 0.4s, color 0.4s;
	transition: border-color 0.4s, color 0.4s;
}
.button--ujarak::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background:white;
	z-index: -1;
	opacity: 0;
	-webkit-transform: scale3d(0.7, 1, 1);
	transform: scale3d(0.7, 1, 1);
	-webkit-transition: -webkit-transform 0.4s, opacity 0.4s;
	transition: transform 0.4s, opacity 0.4s;
	-webkit-transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
	transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
}
.button--ujarak.button--round-s::before {
	border-radius: 2px;
}
.button--ujarak.button--inverted::before {
	background: #7986CB;
}
.button--ujarak,
.button--ujarak::before {
	-webkit-transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
	transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
}
.button--ujarak:hover {
	color: rgb(44, 62, 80);
	border-color: #fff;
}
.button--ujarak.button--inverted:hover {
	color: #fff;
	border-color: rgb(44, 62, 80);
}
.button--ujarak:hover::before {
	opacity: 1;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
} */

.spec-smu-dropdown {
    width: 225px !important;
}

.spec-smu-ecn-dropdown {
    width: 225px !important;
}

.spec-modal-dropdown {
    width: 280px !important;
}

.taginput-wrapper {
    border-radius: 5px;
}

.spe-hr::after {
    background: #fff;
    content: '文件详情';
    top: -13px;
    padding: 0 4px;
    position: relative;
    font-weight: bold;
}

.spe-hr-b::after {
    background: #fff;
    content: '操作';
    top: -13px;
    padding: 0 4px;
    position: relative;
    font-weight: bold;
}

.spe-hr-c::after {
    background: #fff;
    content: '查询';
    top: -13px;
    padding: 0 4px;
    position: relative;
    font-weight: bold;
}

.spe-hr-d::after {
    background: #fff;
    content: '查询结果';
    top: -13px;
    padding: 0 4px;
    position: relative;
    font-weight: bold;
}

.spe-table {
    border-collapse: separate;
    border-spacing: 25px 10px;
    /* font-size: 13px; */
}

.required-fields::before {
    content: '* ';
    color: red;
    font-weight: bold;
}

.a-style {
    color: #1abc9c;
}

.a-style:hover {
    color: #16a085;
    cursor: pointer;
    text-decoration: underline;
}

.btn-float-right {
    margin-left: 200px;
}

.spe-query-date-picker-single>div>div>div {
    /* width:146.5px; */
    line-height: 1.8 !important;
    border-style: none;
    border-radius: 4px;
}

.spe-query-date-picker-single>div>div>div>div {
    border-radius: 4px;
}

.spe-query-date-picker-single input {
    border-radius: 4px;
}

.spe-query-date-picker-multi>div>div>div {
    border-style: none;
    border-radius: 4px;
    width: 350px;
}

.spe-query-date-picker-multi>div>div>div>div {
    border-radius: 4px;
}

.spe-query-date-picker-multi input {
    border-radius: 4px;
}

/* tabulator表格样式（全局） */
.tabulator .tabulator-header {
    background-color: #2c3e50 !important;
    border-bottom-color: var(--TURQUOISE) !important;
}

.tabulator .tabulator-header .tabulator-col {
    background-color: #2c3e50 !important;
}

.tabulator-row.tabulator-group span {
    color: var(--TURQUOISE) !important;
}

.tabulator-row.tabulator-group {
    border-bottom-color: var(--TURQUOISE) !important;
}

.tabulator-row.tabulator-group {
    background-color: #2c3e50 !important;
}

.tabulator-row.tabulator-group .tabulator-arrow {
    border-left-color: var(--TURQUOISE);
}

.tabulator-row.tabulator-group.tabulator-group-visible .tabulator-arrow {
    border-top-color: var(--TURQUOISE);
}

.spe-upload-height div {
    color: #7b8a8b;
    border-radius: 4px !important;
    border-color: #ced4da;
    line-height: 1.5 !important;
    min-height: 10px !important;
    height: 36.5px;
    margin-left: 0 !important;
    margin-top: 0 !important;
}

.spe-upload-height div>div {
    padding: 7px !important;
}

/* ------------Timeline--------------------- */
.timeline {
    width: 100%;
    max-width: 800px;
    background: #fff;
    padding: 30px 50px;
    position: relative;
    /* box-shadow: 0.5rem 0.5rem 2rem 0 rgba(0, 0, 0, 0.2); */
}

.timeline:before {
    content: "";
    position: absolute;
    top: 0px;
    left: calc(20% + 28px);
    bottom: 0px;
    width: 4px;
    background: var(--LIGHTGREY);
}

.timeline:after {
    content: "";
    display: table;
    clear: both;
}

.timeline-entry {
    clear: both;
    text-align: left;
    position: relative;
}

.timeline-title {
    margin-bottom: 0.5em;
    float: left;
    width: 20%;
    padding-right: 30px;
    text-align: right;
    position: relative;
}

.timeline-title:before {
    content: "";
    position: absolute;
    width: 24px;
    height: 24px;
    border: 4px solid var(--LIGHTGREY);
    background-color: #fff;
    border-radius: 50%;
    top: 15%;
    right: -11.7px;
    z-index: 99;
}

.timeline-title:after {
    content: "";
    position: absolute;
    top: 20%;
    right: -6.3px;
    font-family: 'iconfont';
    font-size: 13px;
    font-weight: bold;
    color: var(--LIGHTGREY);
    z-index: 99;
}

.timeline-title.timeline-red:before {
    border-color: var(--ALIZARIN);
}

.timeline-title.timeline-red:after {
    content: "\ea71";
    color: var(--ALIZARIN);
}

.timeline-title.timeline-yellow:before {
    border-color: var(--SUNFLOWER);
}

.timeline-title.timeline-yellow:after {
    content: "\ea76";
    color: var(--SUNFLOWER);
}

.timeline-title.timeline-green:before {
    border-color: var(--TURQUOISE);
}

.timeline-title.timeline-green:after {
    content: "\ea72";
    color: var(--TURQUOISE);
}

.timeline-title h3 {
    margin: 0;
    font-size: 95%;
    font-weight: bold;
}

.timeline-title p {
    margin: 0;
    font-size: 80%;
}

.timeline-body {
    margin: 0 0 3em;
    float: right;
    width: 79%;
    padding-left: 30px;
}

.timeline-body p {
    line-height: 1.4em;
}

.timeline-body p:first-child {
    margin-top: 0;
    font-weight: 400;
}

.timeline-body ul {
    color: var(--WETASPHALT);
    padding-left: 25px;
    margin-top: 5px;
    margin-bottom: 5px;
    font-size: 14px;
    list-style-type: circle;
}

.timeline-body ul li {
    margin-left: 15px;
}

.timeline-body ul li:before {
    content: "";
    margin-right: 0.5em;
}

.tabulator-header-filter {
    color: black;
}

/* 隐藏栏位 */
.tabulator-menu {
    left: 880px;
    top: 0;
}

.pur-tab3-right1 {
    position: absolute;
    left: 126px;
    top: -45px;
    width: 76px;
    height: 37px;
    background-color: #d9534f;
    border-color: #d9534f;
    color: #fff;

}

.pur-tab3-right2 {
    position: absolute;
    left: 246px;
    top: -45px;
    width: 78px;
    height: 38px;
    color: #fff;
    background-color: #29abe0;
    border-color: #29abe0;
}

.pur-tab1-right1 {
    position: absolute;
    /* left: 323px; */
    right: 28px;
    top: 120px;
    width: 70px;
    height: 36px;
    background-color: dimgrey;
    border-color: dimgrey;
    color: #fff;
}


.dash-delete-cell {
    text-align: center !important;
    font-size: 20px !important;
    color: #d35400 !important;
}

.column-header-name {
    margin-left: 0 !important;
}

.span.column-header--sort {
    margin: 0 5px 0 0;
}


.show-hide {
    left: 1174px;
    position: absolute;
    top: -44px;
    width: 78px;
    height: 39px;
    color: white;
    font-family: sans-serif;
    background-color: rgb(199, 197, 186);
    border: white;
    border-radius: 7px;
    font-size: 13px;
}

.dash-spreadsheet-menu .dash-spreadsheet-menu-item .show-hide-menu {
    left: 1120px !important;
}

.navbar .nav-link {
    font-size: 16px;
}


.ant-btn-primary {
    background: rgb(44, 62, 80);
    border-color: rgb(44, 62, 80);
}

.ant-btn-primary:focus {
    background: rgb(44, 62, 80);
    border-color: rgb(44, 62, 80);
}

/* 首页样式 */

#home-card>div.ant-card-head>div>div.ant-card-head-title {
    font: 35px/28px Arial, Microsoft JhengHei, Microsoft YaHei;
    color: rgb(0, 159, 232);
    text-shadow: 2px 2px 4px #c0d6f7;
    font-weight: 900;
}

/* @-webkit-keyframes bounce{
  0%,100%,20%,50%,80%{
    -webkit-transform:translateY(0);
  }40%{
    -webkit-transform:translateY(-30px);
  }60%{
    -webkit-transform:translateY(-15px);
  }
} */

.ant-card-body {
    padding: 8px;
}

/* ce模块的样式 */
/* rd 上传main附件 */
#apply_main_update button {
    float: right;
    top: 0;
    border: none;
    width: 177px;
    height: 95px;
    background-color: #ddd;
}

/* ce 上传main附件 */
#apply_main_update2 button {
    width: 80px;
    height: 36px;
    background-color: #ddd;
    border-radius: 10%;
    border-color: red;
    border: 6px;
}

#apply_next_btn {
    height: 95px;
}


/* 折叠栏 */

.mantine-1avyp1d {
    background-color: #fff;
}

/* 弹框标题样式 */
#modal-title {
    margin-left: 40%;
    font-size: 24px;
}

#mail_modal-title {
    margin-left: 33%;
    font-size: 24px;
}



/* rd处理中样式 */

/* 显示附件的区域 */
#process_file {
    float: right;
    top: 0;
    border: none;
    width: 318px;
    height: 95px;
    background-color: #ddd;
}

#apply_next_btn2 {
    height: 85px;
}

/* 右侧提示框 */
#rightPop {
    position: fixed;
    right: 0px;
    top: 100px;
    width: 330px;
    border-left: 4px solid rgb(112, 184, 240);
    box-shadow: 0 4px 8px 0 rgb(0 0 0 / 10%);
    padding: 16px 40px 16px 16px;
    font-size: 12px;
    z-index: 100;
    background-color: rgb(235, 245, 253);
}

/* 提示框关闭按钮*/
#rightPop_close {
    position: absolute;
    right: 20px;
    color: #70B8F0;
    cursor: pointer;
}

/* 设置cellEditorPopup的z-index属性 */
.ag-popup-editor {
    z-index: 1000000 !important;
}

.ag-select-cell-editor {
    z-index: 1000000 !important;
}

.tabulator-header .tabulator-col .tabulator-col-content .tabulator-col-title {
    white-space: normal !important;
    overflow-wrap: break-word !important;
}

#_pages_content>div>section>aside:nth-child(1) {
    min-width: 30px !important;
    max-width: 120px !important;
}

#_pages_content>div>section>aside:nth-child(1)>div.ant-layout-sider-trigger {
    min-width: 30px !important;
    max-width: 120px !important;
}

.ant-statistic-title {
    margin-bottom: -12px;
}

.ag-theme-quartz .ag-layout-auto-height .ag-center-cols-viewport {
    min-height: fit-content !important;
}

.ant-collapse-header.ant-collapse-header-collapsible-only {
    padding: 5px 5px !important
}

.ag-checked {
    background-color: red !important;
}

#pages-stock-layout-alert {
    padding: 5px;
}

#pages-info-sup-layout-kpi-card>div.ant-card-head {
    /* font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji"; */
    font-size: 14px;
    font: inherit;
    /* background-color: rgb(248, 248, 248); */
    /* all: inherit; */
    /* line-height: 1.5; */
    /* font-weight: 600; */
    /* color: inherit; */
    /* text-shadow: 2px 2px 4px #c0d6f7; */
    /* font-weight: 900; */
    min-height: 20px;
    border: none;
    padding-left: 30px;
    padding-top: 5px;
    /* padding-left: 10px; */

}

#pages-sm-layout-dashboard-tabs-panel-2 > div > div:nth-child(3) > div.react-grid-layout {
    position: relative;
    background: repeating-linear-gradient(
      to right,
      transparent,
      transparent calc(100% / 12 - 1px),
      #ccc calc(100% / 12 - 1px),
      #ccc calc(100% / 12)
    );
  }
  
  #pages-sm-layout-dashboard-tabs-panel-2 > div > div:nth-child(3) > div.react-grid-layout>div.react-grid-item {
    background-color: #fff;
    border: 1px solid #ddd;
    box-sizing: border-box;
  }

  .material-toast {
    height: auto !important;
    min-height: 40px !important;
    padding: 0 16px !important;
    line-height: 1.2 !important;
    width: auto !important;
    min-width: fit-content !important;
    max-width: 90vw !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .material-accordion-item {
    background-color:red;
    padding: auto !important;
  }
  #modal-body > div > div > div.align-self-center.col-9 > div > div > div > div
      --toastify-color-light: #fff;
    --toastify-color-dark: #121212;
    --toastify-color-info: #3498db;
    --toastify-color-success: #07bc0c;
    --toastify-color-warning: #f1c40f;
    --toastify-color-error: #e74c3c;
    --toastify-color-transparent: rgba(255, 255, 255, 0.7);
    --toastify-icon-color-info: var(--toastify-color-info);
    --toastify-icon-color-success: var(--toastify-color-success);
    --toastify-icon-color-warning: var(--toastify-color-warning);
    --toastify-icon-color-error: var(--toastify-color-error);
    --toastify-toast-width: 320px;
    --toastify-toast-background: #fff;
    --toastify-toast-min-height: 64px;
    --toastify-toast-max-height: 800px;
    --toastify-font-family: sans-serif;
    --toastify-z-index: 9999;
    --toastify-text-color-light: #757575;
    --toastify-text-color-dark: #fff;
    --toastify-text-color-info: #fff;
    --toastify-text-color-success: #fff;
    --toastify-text-color-warning: #fff;
    --toastify-text-color-error: #fff;
    --toastify-spinner-color: #616161;
    --toastify-spinner-color-empty-area: #e0e0e0;
    --toastify-color-progress-light: linear-gradient(to right, #4cd964, #5ac8fa, #007aff, #34aadc, #5856d6, #ff2d55);
    --toastify-color-progress-dark: #bb86fc;
    --toastify-color-progress-info: var(--toastify-color-info);
    --toastify-color-progress-success: var(--toastify-color-success);
    --toastify-color-progress-warning: var(--toastify-color-warning);
    --toastify-color-progress-error: var(--toastify-color-error);
    --sl-border-radius-circle: 50%;
    --sl-transition-fast: 150ms;
    --bs-blue: #008cba;
    --bs-indigo: #6610f2;
    --bs-purple: #6f42c1;
    --bs-pink: #e83e8c;
    --bs-red: #f04124;
    --bs-orange: #fd7e14;
    --bs-yellow: #e99002;
    --bs-green: #43ac6a;
    --bs-teal: #20c997;
    --bs-cyan: #5bc0de;
    --bs-black: #000;
    --bs-white: #fff;
    --bs-gray: #888;
    --bs-gray-dark: #333;
    --bs-gray-100: #f8f9fa;
    --bs-gray-200: #eee;
    --bs-gray-300: #dee2e6;
    --bs-gray-400: #ccc;
    --bs-gray-500: #adb5bd;
    --bs-gray-600: #888;
    --bs-gray-700: #495057;
    --bs-gray-800: #333;
    --bs-gray-900: #222;
    --bs-primary: #008cba;
    --bs-secondary: #eee;
    --bs-success: #43ac6a;
    --bs-info: #5bc0de;
    --bs-warning: #e99002;
    --bs-danger: #f04124;
    --bs-light: #eee;
    --bs-dark: #333;
    --bs-primary-rgb: 0, 140, 186;
    --bs-secondary-rgb: 238, 238, 238;
    --bs-success-rgb: 67, 172, 106;
    --bs-info-rgb: 91, 192, 222;
    --bs-warning-rgb: 233, 144, 2;
    --bs-danger-rgb: 240, 65, 36;
    --bs-light-rgb: 238, 238, 238;
    --bs-dark-rgb: 51, 51, 51;
    --bs-primary-text-emphasis: #00384a;
    --bs-secondary-text-emphasis: #5f5f5f;
    --bs-success-text-emphasis: #1b452a;
    --bs-info-text-emphasis: #244d59;
    --bs-warning-text-emphasis: #5d3a01;
    --bs-danger-text-emphasis: #601a0e;
    --bs-light-text-emphasis: #495057;
    --bs-dark-text-emphasis: #495057;
    --bs-primary-bg-subtle: #cce8f1;
    --bs-secondary-bg-subtle: #fcfcfc;
    --bs-success-bg-subtle: #d9eee1;
    --bs-info-bg-subtle: #def2f8;
    --bs-warning-bg-subtle: #fbe9cc;
    --bs-danger-bg-subtle: #fcd9d3;
    --bs-light-bg-subtle: #fcfcfd;
    --bs-dark-bg-subtle: #ccc;
    --bs-primary-border-subtle: #99d1e3;
    --bs-secondary-border-subtle: #f8f8f8;
    --bs-success-border-subtle: #b4dec3;
    --bs-info-border-subtle: #bde6f2;
    --bs-warning-border-subtle: #f6d39a;
    --bs-danger-border-subtle: #f9b3a7;
    --bs-light-border-subtle: #eee;
    --bs-dark-border-subtle: #adb5bd;
    --bs-white-rgb: 255, 255, 255;
    --bs-black-rgb: 0, 0, 0;
    --bs-font-sans-serif: "Open Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
    --bs-body-font-family: var(--bs-font-sans-serif);
    --bs-body-font-size: 1rem;
    --bs-body-font-weight: 400;
    --bs-body-line-height: 1.5;
    --bs-body-color: #222;
    --bs-body-color-rgb: 34, 34, 34;
    --bs-body-bg: #fff;
    --bs-body-bg-rgb: 255, 255, 255;
    --bs-emphasis-color: #000;
    --bs-emphasis-color-rgb: 0, 0, 0;
    --bs-secondary-color: rgba(34, 34, 34, 0.75);
    --bs-secondary-color-rgb: 34, 34, 34;
    --bs-secondary-bg: #eee;
    --bs-secondary-bg-rgb: 238, 238, 238;
    --bs-tertiary-color: rgba(34, 34, 34, 0.5);
    --bs-tertiary-color-rgb: 34, 34, 34;
    --bs-tertiary-bg: #f8f9fa;
    --bs-tertiary-bg-rgb: 248, 249, 250;
    --bs-heading-color: inherit;
    --bs-link-color: #008cba;
    --bs-link-color-rgb: 0, 140, 186;
    --bs-link-decoration: underline;
    --bs-link-hover-color: #007095;
    --bs-link-hover-color-rgb: 0, 112, 149;
    --bs-code-color: #e83e8c;
    --bs-highlight-bg: #fbe9cc;
    --bs-border-width: 1px;
    --bs-border-style: solid;
    --bs-border-color: #dee2e6;
    --bs-border-color-translucent: rgba(0, 0, 0, 0.175);
    --bs-border-radius: 0;
    --bs-border-radius-sm: 0;
    --bs-border-radius-lg: 0;
    --bs-border-radius-xl: 1rem;
    --bs-border-radius-xxl: 2rem;
    --bs-border-radius-2xl: var(--bs-border-radius-xxl);
    --bs-border-radius-pill: 50rem;
    --bs-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --bs-box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --bs-box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --bs-box-shadow-inset: inset 0 1px 2px rgba(0, 0, 0, 0.075);
    --bs-focus-ring-width: 0.25rem;
    --bs-focus-ring-opacity: 0.25;
    --bs-focus-ring-color: rgba(0, 140, 186, 0.25);
    --bs-form-valid-color: #43ac6a;
    --bs-form-valid-border-color: #43ac6a;
    --bs-form-invalid-color: #f04124;
    --bs-form-invalid-border-color: #f04124;
    --bs-breakpoint-xs: 0;
    --bs-breakpoint-sm: 576px;
    --bs-breakpoint-md: 768px;
    --bs-breakpoint-lg: 992px;
    --bs-breakpoint-xl: 1200px;
    --bs-breakpoint-xxl: 1400px;
    --TURQUOISE: #1abc9c;
    --GREENSEA: #16a085;
    --SUNFLOWER: #f1c40f;
    --ORANGE: #f39c12;
    --EMERALD: #2ecc71;
    --NEPHRITIS: #27ae60;
    --CARROT: #e67e22;
    --PUMPKIN: #d35400;
    --PETERRIVER: #3498db;
    --BELIZEHOLE: #2980b9;
    --ALIZARIN: #e74c3c;
    --POMEGRANATE: #c0392b;
    --AMETHYST: #9b59b6;
    --WISTERIA: #8e44ad;
    --WETASPHALT: #34495e;
    --MIDNIGHTBLUE: #2c3e50;
    --CONCRETE: #95a5a6;
    --ASBESTOS: #7f8c8d;
    --LIGHTGREY: #ecf0f1;
    --nprogress-color: #51CF66;
    font-family: var(--bs-body-font-family);
    font-size: var(--bs-body-font-size);
    font-weight: var(--bs-body-font-weight);
    line-height: var(--bs-body-line-height);
    text-align: var(--bs-body-text-align);
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
    --bs-modal-zindex: 1055;
    --bs-modal-width: 500px;
    --bs-modal-padding: 1rem;
    --bs-modal-color: ;
    --bs-modal-bg: var(--bs-body-bg);
    --bs-modal-border-color: var(--bs-border-color-translucent);
    --bs-modal-border-width: var(--bs-border-width);
    --bs-modal-border-radius: var(--bs-border-radius-lg);
    --bs-modal-inner-border-radius: calc(var(--bs-border-radius-lg) - (var(--bs-border-width)));
    --bs-modal-header-padding-x: 1rem;
    --bs-modal-header-padding-y: 1rem;
    --bs-modal-header-padding: 1rem 1rem;
    --bs-modal-header-border-color: var(--bs-border-color);
    --bs-modal-header-border-width: var(--bs-border-width);
    --bs-modal-title-line-height: 1.5;
    --bs-modal-footer-gap: 0.5rem;
    --bs-modal-footer-bg: ;
    --bs-modal-footer-border-color: var(--bs-border-color);
    --bs-modal-footer-border-width: var(--bs-border-width);
    --bs-modal-margin: 1.75rem;
    --bs-modal-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    pointer-events: auto;
    --bs-gutter-x: 1.5rem;
    --bs-gutter-y: 0;
    --bs-accordion-color: var(--bs-body-color);
    --bs-accordion-bg: var(--bs-body-bg);
    --bs-accordion-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease;
    --bs-accordion-border-color: var(--bs-border-color);
    --bs-accordion-border-width: var(--bs-border-width);
    --bs-accordion-border-radius: var(--bs-border-radius);
    --bs-accordion-inner-border-radius: calc(var(--bs-border-radius) - (var(--bs-border-width)));
    --bs-accordion-btn-padding-x: 1.25rem;
    --bs-accordion-btn-padding-y: 1rem;
    --bs-accordion-btn-color: var(--bs-body-color);
    --bs-accordion-btn-bg: var(--bs-accordion-bg);
    --bs-accordion-btn-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23222'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
    --bs-accordion-btn-icon-width: 1.25rem;
    --bs-accordion-btn-icon-transform: rotate(-180deg);
    --bs-accordion-btn-icon-transition: transform 0.2s ease-in-out;
    --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%2300384a'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
    --bs-accordion-btn-focus-border-color: #80c6dd;
    --bs-accordion-btn-focus-box-shadow: 0 0 0 0.25rem rgba(0, 140, 186, 0.25);
    --bs-accordion-body-padding-x: 1.25rem;
    --bs-accordion-body-padding-y: 1rem;
    --bs-accordion-active-color: var(--bs-primary-text-emphasis);
    --bs-accordion-active-bg: var(--bs-primary-bg-subtle);
    color: var(--bs-accordion-color);
    box-sizing: border-box;
    padding: var(--bs-accordion-body-padding-y) var(--bs-accordion-body-padding-x);