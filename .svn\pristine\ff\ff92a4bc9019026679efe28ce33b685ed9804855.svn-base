# -*- coding: utf-8 -*-
import dash_mantine_components as dmc
from dash_iconify import DashIconify


def create_sidebar(page, menu_items):
    link = []
    for item in menu_items:
        if item.get("page") == page:
            active = True
        else:
            active = False
        navlink_children = []
        child = item.get("children")
        if child:
            for c in child:
                if c.get("page") == page:
                    active = True
                else:
                    active = False
                x = dmc.NavLink(
                    label=c.get("label"),
                    href=c.get("href"),
                    leftSection=DashIconify(icon=c.get("icon"), width=20),
                    target=c.get("target", "_self"),
                    color="red",
                    active=active,
                    refresh=False,
                    style={
                        "font-weight": c.get("font-weight"),
                        "color": c.get("color"),
                    },
                )
                navlink_children.append(x)
        label = item.get("label")
        if label in ("待确认", "未处理", "在途"):
            count = int(item.get("count", 0))
            if len(label) == 2:
                offset = -18
            else:
                offset = -13
            if count > 0:
                label = dmc.Indicator(
                    label,
                    position="middle-end",
                    offset=offset,
                    size=13,
                    label=count,
                    inline=True,
                    color="red",
                    processing=True,
                )
        navlink = dmc.NavLink(
            label=label,
            href=item.get("href"),
            leftSection=DashIconify(icon=item.get("icon"), height=16),
            target=item.get("target", "_self"),
            color="red",
            active=active,
            refresh=False,
            style={
                "font-weight": item.get("font-weight"),
                "color": item.get("color"),
            },
            children=navlink_children,
        )
        link.append(navlink)
    link.insert(1, dmc.Divider())
    sidebar = dmc.Stack(link, gap=5)
    return sidebar
