import os

import dash_bootstrap_components as dbc
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import ALL, Input, Output, callback
from flask import request
from pony.orm import db_session
from db.ssp import User


navbar = dbc.NavbarSimple(
    children=[
        dbc.NavLink("Home", href="/"),
        dbc.NavLink(
            "样制",
            href="/sm",
            id={"type": "nav-role", "index": 0},
            style={"display": "none"},
        ),
        dbc.NavLink(
            "物料",
            href="/pur",
            id={"type": "nav-role", "index": 1},
            style={"display": "none"},
        ),
        dbc.NavLink(
            "规格",
            href="/spec",
            id={"type": "nav-role", "index": 2},
            style={"display": "none"},
        ),
        dbc.NavLink(
            "材料",
            href="/ce",
            id={"type": "nav-role", "index": 3},
            style={"display": "none"},
        ),
        dbc.NavLink(
            "仓库",
            href="/stock",
            id={"type": "nav-role", "index": 4},
            style={"display": "none"},
        ),
        dbc.NavLink(
            "资产",
            href="/asset",
            id={"type": "nav-role", "index": 5},
            style={"display": "none"},
        ),
        dbc.NavLink(
            "BOM",
            href="/bom",
            id={"type": "nav-role", "index": 6},
            style={"display": "none"},
        ),
        dbc.NavLink("User Center", id="center-href"),
        dbc.NavLink("Feedback", href="/message", id="nav-contact"),
        # dbc.DropdownMenu(
        #     label="更多",
        #     children=[
        #         dbc.DropdownMenuItem("联系我们", href="/message"),
        #         dbc.DropdownMenuItem("满意度评价", href="/satisfaction"),
        #     ],
        #     nav=True,
        #     in_navbar=True,
        #     align_end=True,
        # ),
        dbc.DropdownMenu(
            dbc.DropdownMenuItem("更新资料", href="/login"),
            nav=True,
            in_navbar=True,
            id="nt-name",
            align_end=True,
            disabled=True,
        ),
    ],
    brand="Delta SUP" if os.getenv("MYSQL_TCP_PORT") == "3306" else "Delta SUP测试环境",
    brand_href="/",
    sticky="top",
    color="#2c3e50",
    dark=True,
    id="page-header",
    fluid=True,
    style={"height": "100%"},
)


# ================回调函数====================
@callback(
    Output("user", "data"),
    Output("nt-name", "label"),
    Output("nt-name", "disabled"),
    Input("nt-name", "id"),
    prevent_initial_call=False,
)
@db_session
def welcome(id):
    user = request.environ.get("HTTP_X_REMOTE_USER", f"Delta\\{os.getenv('username')}")
    nt_name = user.split("\\")[1]
    u = User.get(nt_name=nt_name)
    if u:
        user_data = u.to_dict()
    else:
        user_data = {}
    admin = [
        "weiming.li",
        "zhen.liu",
        "kuangyi.gu",
        "danfeng.chen",
        "ying.gao",
        "mona.zhang",
        "bo.sm.wang",
        "yingzhi.zhang",
        "mmeng.chen",
    ]
    if nt_name.lower() in admin:
        disabled = False
    else:
        disabled = True
    return user_data, f"{nt_name.title()}", disabled


@callback(
    Output("url", "pathname"),
    Input("user", "data"),
    Input("url", "pathname"),
)
def url_redirect(user, pathname):
    if not user:
        return "/login"
    else:
        raise PreventUpdate


@callback(
    Output({"type": "nav-role", "index": ALL}, "style"),
    Output("center-href", "href"),
    Input("user", "data"),
)
def nav_role(user):
    if user.get("dept_id") == 10:
        return [{"display": "block"}] * 7, "/info/sup"
    else:
        return [{"display": "none"}] * 7, "/info/rd"


@callback(
    Output("nav-contact", "style"),
    Input("user", "data"),
)
def nav_contact(user):
    if user.get("dept_id") == 10:
        return {"display": "none"}
    else:
        raise PreventUpdate
