# -*- coding: utf-8 -*-
import numpy as np
import pandas as pd
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import Input, Output, State, callback, dash

from common import (
    add_mat_category,
    df_add_ce_owner,
    material_information_complete,
    df_to_html,
    get_db,
)
from components.notice import notice

# from dbtool import db
from datetime import datetime
from tasks import bg_mail

from . import layout

id = layout.id
layout = layout.layout
dash.register_page(__name__, path="/ce/pn/update/rd", title="料号升级申请-RD")


@callback(
    Output(id("table"), "data"),
    Input(id("add-row"), "n_clicks"),
    State(id("table"), "data"),
)
def table_add_row(n_clicks, data):
    if n_clicks:
        data.append({})
        return data
    else:
        raise PreventUpdate


@callback(
    Output(id("table"), "data"),
    Input(id("table"), "data_timestamp"),
    State(id("table"), "active_cell"),
    State(id("table"), "data"),
    State(id("table"), "columns"),
)
def update_table_when_pasting_data(data_timestamp, active_cell, data, columns):
    if not data_timestamp:
        raise PreventUpdate

    column_id = active_cell.get("column_id") if active_cell else None
    if column_id not in ("deltapn", "mfgpn", "des"):
        raise PreventUpdate
    column_id = "mfgpn" if column_id == "des" else column_id

    df = pd.DataFrame(data)
    cols = [i.get("id") for i in columns]
    df = df.reindex(columns=cols)

    df = material_information_complete(df, column_id)
    df["deltapn"] = np.where(df["deltapn"] == "", "NEWPART", df["deltapn"])
    df["mfgpn"] = np.where(df["mfgpn"] == "", "NA", df["mfgpn"])
    df = add_mat_category(df)
    return df.to_dict(orient="records")


@callback(
    Output("global-notice", "children"),
    Output(id("rd-submit"), "disabled"),
    Input(id("rd-submit"), "n_clicks"),
    State(id("table"), "data"),
    State(id("attachment"), "lastUploadTaskRecord"),
    State(id("applicant"), "value"),
    State(id("title"), "children"),
)
def rd_submit(n_clicks, data, attachment, applicant, title):
    if not n_clicks:
        raise PreventUpdate

    df = pd.DataFrame(data)
    df = df.replace({"": None})
    df = df.dropna(how="all")

    if df.empty:
        return notice("请填写数据", "error"), False

    if not attachment:
        return notice("请上传附件", "warning"), False

    db = get_db()
    user = db.find_one("ssp.user", {"nt_name": applicant})
    dept = user.get("dept")
    dept_id = user.get("dept_id")

    attach = attachment.get("taskId")
    sub_type = title[:-3]
    cc = ["Ru.Xue", "Ying.Gao"]
    if dept_id in (4, 5, 22):
        cc += ["Yuhan.Wang"]

    df = df_add_ce_owner(df)

    for item in df.itertuples():
        ce = item.owner_ce
        task = {
            "type": "料号申请",
            "sub_type": sub_type,
            "applicant": applicant,
            "ce": ce,
            "status": "open",
            "urgent": "一般",
            "dept": dept,
            "cat1": item.cat1,
            "cat2": item.cat2,
            "cat3": item.cat3,
            "deltapn": item.deltapn,
            "des": item.des,
            "mfgpn": item.mfgpn,
            "mfgname": item.mfgname,
            "dept_id": dept_id,
            "cc": ",".join(cc),
            "start_date": datetime.now(),
        }
        task_id = db.insert("ce.task", task)
        x = {
            "task_id": task_id,
            "deltapn": item.deltapn,
            "des": item.des,
            "mfgname": item.mfgname,
            "mfgpn": item.mfgpn,
            "cat1": item.cat1,
            "cat2": item.cat2,
            "cat3": item.cat3,
            "change_content": item.change_content,
            "old_invalid": item.old_invalid,
            "attachment": attach,
        }
        db.insert("ce.pn_update", x)

        # *---------邮件通知开始-----------*
        to = [applicant, ce] + cc
        to = ";".join(f"{i}@deltaww.com" for i in to)
        subject = f"【料号申请】{sub_type}"
        df = pd.DataFrame([task])
        columns = [
            "type",
            "dept",
            "applicant",
            "deltapn",
            "mfgname",
            "mfgpn",
            "start_date",
            "ce",
        ]
        df = df.reindex(columns=columns)
        df["start_date"] = df["start_date"].dt.date
        content = df_to_html(
            df,
            f"您的{sub_type}已提交,{ce}将会处理,请知悉！",
            href="http://sup.deltaww.com/info/rd?page=ongoing",
            link_text="工作进展可至个人中心查询",
        )
        bg_mail(to, subject, content)
        # *---------邮件通知结束-----------*

    return notice("提交成功"), True
