import dash
from . import layout

layout = layout.layout
dash.register_page(__name__, path="/nre", title="NRE")

'''
doc = (
    SSP_DIR
    / "3. Document backup"
    / "ssp_doc"
    / "DES NRE数据收集与分析网站 填写说明 V1.0 20220125.pptx"
)

bgc = assign(
    """
    function(cell){
        cell.getElement().style.backgroundColor = "#84e6ac";
        }
        """
)

func1 = assign(
    """
    function (cell){
        return {star:8};
        }
    """
)

cc = assign(
    """
    function(){
        window.alert(123);
        }
    """
)

formatter = assign(
    """
    function(cell, formatterParams, onRendered){
        let v=cell.getValue();
        v=v*100;
        return v.toFixed(0)+'%'
        }
        """
)

dept_dict = {
    21: "CD",
    22: "APE",
    28: "IM",
    "CD": 21,
    "APE": 22,
    "IM": 28,
    "GENERAL": 21,
}

@callback(
    Output("nre-tab-2", "disabled"),
    Output("nre-tab-3", "disabled"),
    Input("user", "data"),
    prevent_initial_call=False,
)
def disabled_tabs(user):
    nt_name = user.get("nt_name").lower()
    if nt_name in cfg.nre_admin:
        return False, False
    else:
        raise PreventUpdate


@callback(
    Output("month-slider", "value"),
    Input("tabs", "active_tab"),
    prevent_initial_call=False,
)
def current_month(active_tab):
    if active_tab != "tab-1":
        raise PreventUpdate
    now = datetime.now().strftime("%Y-%m")
    return now


@callback(
    Output("nre-user", "options"),
    Output("nre-user", "value"),
    Output("nre-user", "disabled"),
    Input("user", "data"),
    prevent_initial_call=False,
)
@db_session
def nre_user(user):
    nt_name = user.get("nt_name").lower()
    des_users = select(
        i.nt_name for i in User if i.dept_id in (21, 22, 28) and not i.termdate
    )[:]
    options = [{"label": i.title(), "value": i.title()} for i in des_users]
    if nt_name in cfg.nre_admin:
        disabled = False
    else:
        disabled = True
    return options, nt_name.title(), disabled


@callback(
    Output("nre-data-table", "columns"),
    Output("nre-data-table", "data"),
    Input("month-slider", "value"),
    Input("nre-user", "value"),
)
@db_session
def nre_data(value, user):
    # breakpoint()
    # now = datetime.now()
    # nt_name = user.get("nt_name")
    # start_time = pd.Period(f"{now.year}-{month}", freq="M").start_time
    # end_time = pd.Period(f"{now.year}-{month}", freq="M").end_time
    if not value:
        raise PreventUpdate

    start_time = pd.Period(value, freq="M").start_time
    end_time = pd.Period(value, freq="M").end_time
    year, month = value.split("-")

    # breakpoint()
    dr = pd.date_range(start_time, end_time)
    nre_data = select(
        i for i in Nre_data if i.owner == user and i.year == year and i.month == month
    )[:]
    # breakpoint()
    nre_project = select(
        i
        for i in Nre_project
        if i.status == "open"
        or (
            i.status == "closed"
            and i.close_time.year == year
            and i.close_time.month == month
        )
    )[:]

    df = pd.DataFrame(
        {
            "id": i.id,
            "dept": i.dept,
            "project": i.project,
            "model": i.model,
            "a_code": i.a_code,
            "total": None,
        }
        for i in nre_project
    )

    col1 = [{"title": i, "field": i, "frozen": True} for i in df.columns]

    col2 = []
    for i in dr:
        col = {
            "title": f"{i.day}",
            "field": f"{i.date()}",
            "headerSort": False,
            "validator": "max:12",
            "week": i.week,
            "editor": "input",
        }
        if is_holiday(i):
            col["formatterParams"] = bgc
            col["is_holiday"] = True
            col["validator"] = "max:8"

        # if month == now.month or month == now.month - 1:
        #     col["editor"] = "input"

        col2.append(col)

    columns = col1 + col2
    if nre_data:
        df1 = pd.DataFrame({**i.data, **{"id": i.project.id}} for i in nre_data)
        df = df.merge(df1, on="id", how="left")
        total = df1.drop("id", axis=1).fillna(0).replace("", 0).astype(int).sum()
        s1 = pd.Series({"dept": "统计", "project": "总计:"})
        df = df.append(total.append(s1), ignore_index=True)

        dfh = pd.DataFrame(col2)[["field", "is_holiday"]]
        dft = total.reset_index().merge(
            dfh, how="left", left_on="index", right_on="field"
        )

        dft[1] = dft[0]
        c1 = dft["is_holiday"] is not True
        c2 = dft[1] < 8
        dft[1] = np.where(c1 & c2, 8 - dft[1], None)

        leave = dft.set_index("index")[1]
        s = pd.Series({"dept": "统计", "project": "请假:"})
        leave = leave.append(s)

        c2 = dft[0] > 8
        c3 = dft[0] <= 8
        dft[0] = np.where(c1 & c2, dft[0] - 8, dft[0])
        dft[0] = np.where(c1 & c3, None, dft[0])

        ot = dft.set_index("index")[0]
        s = pd.Series({"dept": "统计", "project": "加班:"})
        ot = ot.append(s)

        df = df.append(ot, ignore_index=True).append(leave, ignore_index=True)
        df["total"] = (
            df.loc[:, df.columns.isin(dr.astype(str))]
            .fillna(0)
            .replace("", 0)
            .astype(int)
            .sum(1)
        )

    data = df.reindex(columns=[i["field"] for i in columns]).to_dict(orient="records")
    return columns, data


@callback(
    Output("nre-data-table", "data"),
    Input("first-fill-month", "n_clicks_timestamp"),
    Input("first-fill-week", "n_clicks_timestamp"),
    State("nre-data-table", "data"),
    State("nre-data-table", "columns"),
)
def fill_data(n1, n2, data, columns):
    if not (n1 or n2):
        raise PreventUpdate

    which_btn = (n1, n2).index(max(n1, n2))
    df = pd.DataFrame(data)
    day_cols = {
        i["field"]: i["week"]
        for i in columns
        if i["title"].isdigit() and not i.get("is_holiday", False)
    }
    df1 = df.reindex(columns=day_cols)
    df2 = df.loc[:, df.columns.difference(df1.columns)]

    df1 = df1.replace({np.nan: None})

    if which_btn == 1:
        weeks = {i.get("week") for i in columns if i["field"] in day_cols}
        dfx = []
        for j in weeks:
            col_x = [i for i in day_cols if day_cols[i] == j]
            dfi = df1.reindex(columns=col_x)
            for i, j in dfi.iterrows():
                if j.iloc[0] and not j.iloc[1]:
                    dfi.iloc[i] = j.fillna(method="ffill")
            dfx.append(dfi)
        df1 = pd.concat(dfx, axis=1)
    else:
        for i, j in df1.iterrows():
            if j.iloc[0] and not j.iloc[1]:
                df1.iloc[i] = j.fillna(method="ffill")

    df = pd.concat([df1, df2], axis=1)
    return df.to_dict(orient="records")


@callback(
    Output("global-notice", "children"),
    Input("nre-save", "n_clicks"),
    State("nre-data-table", "data"),
    State("nre-data-table", "columns"),
    State("month-slider", "value"),
    State("nre-user", "value"),
)
@db_session
def save_data(n, data, columns, value, nt_name):
    if not n:
        raise PreventUpdate
    year, month = value.split("-")

    df = pd.DataFrame(data).set_index("id")
    df = df.loc[df["dept"] != "统计"]
    df1 = df.loc[:, df.columns.str.match("\d{4}-\d{2}-\d{2}")]
    # breakpoint()
    # df1 = df1.fillna("").applymap(lambda x: int(x) if x else None)
    df1 = df1.replace({np.nan: None, "": None})

    dfc = pd.DataFrame(columns)
    holiday = dfc.loc[dfc["is_holiday"] == True]["field"]
    df_holiday = df1.loc[:, df1.columns.isin(holiday)]
    df_workday = df1.loc[:, ~df1.columns.isin(holiday)]

    if (df_holiday.fillna(0).astype(int).sum() > 8).any():
        return notice("假日不能超过8小时,请修正", "error")

    if (df_workday.fillna(0).astype(int).sum() > 12).any():
        return notice("平日不能超过12小时,请修正", "error")

    role = User.get(nt_name=nt_name).role_group
    # year = datetime.now().year

    for i in df1.itertuples():
        data = df1.loc[i.Index].dropna().to_dict()
        if data:
            nre = select(
                j
                for j in Nre_data
                if j.project.id == i.Index and j.month == month and j.owner == nt_name
            )[:]
            if nre:
                nre[0].set(
                    project=int(i.Index),
                    year=year,
                    month=month,
                    data=data,
                    owner=nt_name,
                    role=role,
                )
            else:
                Nre_data(
                    project=int(i.Index),
                    year=year,
                    month=month,
                    data=data,
                    owner=nt_name,
                    role=role,
                )
    return notice()  # "保存成功", "success", True


@callback(Output("nre-doc-download", "data"), Input("nre-doc-btn", "n_clicks"))
def download_document(n_clicks):
    if not n_clicks:
        raise PreventUpdate
    return dcc.send_file(doc)


# clientside_callback(
#     ClientsideFunction(namespace="clientside", function_name="nreDataValid"),
#     Output("nre-data-table", "columns"),
#     Input("nre-data-table", "cellEdited"),
#     State("nre-data-table", "columns"),
#     State("nre-data-table", "data"),
# )


# @callback(
#     Output("nre-data-table", "columns"),
#     Input("nre-data-table", "cellEdited"),
#     State("nre-data-table", "columns"),
#     State("nre-data-table", "data"),
# )
# def nre_data_valid(e, col, data):
#     if not e:
#         raise PreventUpdate
#     for i in col:
#         is_holiday = i.get("is_holiday")
#         if is_holiday:
#             i["formatterParams"] = bgc

#         if i["field"] == e["column"]:
#             valid = i.get("validator")
#             if valid:
#                 sum_data = sum(
#                     int(i[e["column"]]) for i in data if i[e["column"]] and i["id"]
#                 )

#                 if is_holiday:
#                     i["validator"] = f"max:{8-sum_data}"
#                 else:
#                     i["validator"] = f"max:{12-sum_data}"
#     return col


# -----------tab-2------------------
@callback(
    # Output("nre-project-table", "columns"),
    Output("nre-project-table", "data"),
    Input("tabs", "active_tab"),
)
@db_session
def nre_project(active_tab):
    if active_tab != "tab-2":
        raise PreventUpdate
    project = select(i for i in Nre_project)[:]
    data = [
        {
            "id": i.id,
            "dept": i.dept,
            "project": i.project,
            "model": i.model,
            "a_code": i.a_code,
            "status": i.status,
        }
        for i in project
    ]
    return data


@callback(
    Output("nre-project-table", "data"),
    Input("nre-project-add-row", "n_clicks"),
    State("nre-project-table", "data"),
)
def nre_project_table_add_row(n_clicks, data):
    """nre-project-table插入行"""
    if n_clicks:
        data.append({"action": "add", "status": "open"})
        return data
    else:
        raise PreventUpdate


@callback(
    Output("nre-project-table", "data"),
    Input("nre-project-table", "cellEdited"),
    State("nre-project-table", "data"),
)
def nre_project_changed(e, data):
    if not e:
        raise PreventUpdate
    row_id = e.get("row").get("id")
    if row_id:
        for i in data:
            if i["id"] == row_id:
                i["action"] = "update"
        return data
    else:
        raise PreventUpdate


@callback(
    Output("nre-project-alert", "children"),
    Output("nre-project-alert", "color"),
    Output("nre-project-alert", "is_open"),
    Input("nre-project-submit", "n_clicks"),
    State("nre-project-table", "data"),
    State("user", "data"),
)
@db_session
def nre_project_submit(n_clicks, data, user):
    """nre-project-table提交"""
    if not n_clicks:
        raise PreventUpdate
    nt_name = user.get("nt_name")
    now = datetime.now()
    for i in data:
        action = i.get("action")
        if action == "update":
            id = i.get("id")
            proj = Nre_project.get(id=id)
            proj.set(
                status=i["status"],
                project=i["project"],
                model=i["model"],
                a_code=i["a_code"],
                close_time=now if i["status"] == "closed" else proj.close_time,
            )
        elif action == "add":
            Nre_project(
                dept=i["dept"],
                project=i["project"],
                model=i["model"],
                a_code=i["a_code"],
                owner=nt_name,
            )

    return "提交成功", "success", True


@callback(
    Output("summary-year", "value"),
    Input("tabs", "active_tab"),
)
def nre_summary_year(active_tab):
    if active_tab != "tab-3":
        raise PreventUpdate
    year = datetime.now().year
    return f"{year}"


@callback(
    Output("nre-summary-table", "data"),
    Output("nre-summary-table", "columns"),
    Output("nre-summary-model", "children"),
    Input("nre-summary-type", "value"),
    Input("summary-year", "value"),
    Input("nre-dept", "value"),
)
@db_session
def nre_summary(value, year, dept_id):
    """nre_summary"""
    # year = datetime.now().year
    if not all([value, year, dept_id]):
        raise PreventUpdate
    col_month = list(range(1, 13))
    model_summary = no_update

    if value == "submit":
        des_users = select(
            i.nt_name for i in User if i.dept_id == dept_id and not i.termdate
        )[:]
        des_users = [i.title() for i in des_users]

        data = select((i.month, i.owner) for i in Nre_data if i.year == year)[:]
        dfd = pd.DataFrame(data, columns=["month", "name"])
        dfd["name"] = dfd["name"].str.title()
        dfd["submit"] = 1
        dfd = pd.pivot_table(
            dfd, index="name", values="submit", columns="month", aggfunc=np.sum
        )
        dfd = dfd.reindex(columns=col_month)
        dfd = dfd.reindex(des_users).reset_index()
        data = dfd.to_dict(orient="records")
        columns = [
            {
                "field": f"{i}",
                "title": f"{i}",
                "headerFilter": "select" if i in col_month else "input",
                "headerFilterParams": {"values": True},
                "formatter": "tickCross" if i in col_month else None,
                "formatterParams": {"allowEmpty": True},
            }
            for i in dfd.columns
        ]
        return data, columns, model_summary

    data = select(i for i in Nre_data if i.year == year)[:]
    df = pd.DataFrame(
        {
            "dept": i.project.dept,
            "project": i.project.project,
            "model": i.project.model,
            "a_code": i.project.a_code,
            "data": sum(int(i) for i in i.data.values()),
            "month": i.month,
            "owner": i.owner,
            "role": i.role.upper(),
        }
        for i in data
    )
    dept = dept_dict.get(dept_id)
    df = df.loc[df["dept"] == dept]

    if value in ("model", "a_code"):
        index = ["project", value]
    elif value in ("EE", "ME", "LAYOUT", "SOFTWARE", "TE", "PM"):
        df = df.loc[df["role"] == value]
        index = ["role", "owner"]
    else:
        index = value

    dfp = pd.pivot_table(
        df, values="data", index=index, columns="month", aggfunc=np.sum
    )

    if value == "model":
        model_summary = []
        for i in df["model"].unique():
            dfi = df.loc[df["model"] == i]
            dfi_pt = pd.pivot_table(
                dfi,
                values="data",
                index=["model", "role"],
                columns="month",
                aggfunc=np.sum,
            )
            dfi_pt = dfi_pt.reindex(
                [
                    (i, "EE"),
                    (i, "ME"),
                    (i, "LAYOUT"),
                    (i, "SOFTWARE"),
                    (i, "TE"),
                    (i, "PM"),
                ]
            )
            dfi_pt = dfi_pt.reindex(columns=col_month).reset_index().fillna(0)
            dfi_pt["total"] = dfi_pt[col_month].sum(1)
            tablei = DashTabulator(
                data=dfi_pt.to_dict(orient="records"),
                columns=[
                    {"field": f"{i}", "title": f"{i}", "topCalc": "sum"}
                    for i in dfi_pt.columns
                ],
                theme="tabulator_site",
                options={
                    "selectable": 1,
                    # "height": "380px",
                    "columnCalcs": "table",
                },
                downloadButtonType={
                    "css": "btn btn-sm btn-outline-primary",
                    "text": "Export",
                    "type": "xlsx",
                },
            )
            model_summary.extend([tablei, html.Br()])

    dfp = dfp.reindex(columns=col_month).reset_index().fillna(0)

    dfp["total"] = dfp[col_month].sum(1)
    dfp["%"] = dfp["total"] / dfp["total"].sum()
    columns = []
    for i in dfp.columns:
        coli = {"field": f"{i}", "title": f"{i}"}
        if i in col_month + ["total", "%"]:
            coli.update({"topCalc": "sum"})
        if i in col_month:
            coli.update({"title": f"{coli['title']}月"})
        if i == "%":
            coli.update({"formatter": formatter})
        columns.append(coli)

    data = dfp.to_dict(orient="records")
    return data, columns, model_summary
'''
