from datetime import datetime

import dash_bootstrap_components as dbc
import dash_uploader as du
import numpy as np
import pandas as pd
from config import SSP_DIR, UPLOAD_FOLDER_ROOT, pool
from dash_extensions.enrich import (
    dash_table,
    dcc,
    html,
    no_update,
    Input,
    Output,
    State,
    callback,
    dash,
)
from dash.exceptions import PreventUpdate
from tasks import bg_mail
import dash_mantine_components as dmc
from common import read_sql

dash.register_page(__name__, title="重工")

uploader = du.Upload(
    id="dash-uploader",
    text="点击上传重工文件",
    filetypes=["xls", "xlsx"],
    default_style={"border-color": "rgba(26, 188, 156)", "min-height": "100%"},
)

style_header = {
    "backgroundColor": "rgba(52, 73, 94)",
    "color": "white",
    "fontWeight": "bold",
    "fontSize": "12px",
    # 'border': '1px solid',
    "textTransform": "uppercase",
}

style_cell = {
    "whiteSpace": "normal",
    "height": "auto",
    "textAlign": "left",
    "font-family": "Helvetica",
    "font-size": "10px",
}

tab1_content = html.Div(
    [
        html.Br(),
        dbc.Row(
            [
                dbc.Col(
                    dcc.Dropdown(
                        id="auto-nt",
                        placeholder="输入工程师",
                        style={"border-color": "#1abc9c"},
                    ),
                    width=2,
                ),
                dbc.Col(
                    dbc.InputGroup(
                        [
                            dbc.InputGroupText("机种名"),
                            dbc.Input(id="rwk-proj", style={"border-color": "#1abc9c"}),
                        ]
                    ),
                ),
                dbc.Col(
                    dbc.InputGroup(
                        [
                            dbc.InputGroupText("重工数量"),
                            dbc.Input(
                                id="rwk-qty",
                                type="number",
                                style={"border-color": "#1abc9c"},
                            ),
                        ]
                    ),
                ),
                dbc.Col(
                    dbc.InputGroup(
                        [
                            dbc.InputGroupText("需求日期"),
                            dcc.DatePickerSingle(
                                id="rwk-date",
                                display_format="YYYY-M-D",
                                with_portal=True,
                            ),
                        ]
                    ),
                ),
                dbc.Col(
                    dbc.Button("下载模板", id="rwk-download-btn"),
                ),
            ],
            # no_gutters=True,
        ),
        html.Pre(),
        uploader,
        html.Pre(),
        html.Div(id="callback-output"),
        dcc.Download(id="rwk-download"),
    ]
)

layout = dbc.Container(
    [
        dbc.Tabs(
            [
                dbc.Tab(tab1_content, label="重工", tab_id="tab-1"),
            ],
            id="tab-num",
            active_tab="tab-1",
            style={"color": "#16a085"},
        )
    ],
    fluid=True,
    className="ml-3 pr-5",
)


# ---------回调函数----------------
@callback(
    [Output("auto-nt", "options")], [Input("auto-nt", "id")], prevent_initial_call=False
)
def auto_nt_data_source(id):
    sql = "select distinct nt_name from ssp.user where termdate is null"
    user = read_sql(sql)

    user["nt_name"] = user["nt_name"].str.title()
    options = [{"label": i, "value": i} for i in user["nt_name"]]
    return options


@callback(
    [Output("rwk-download", "data")],
    [Input("rwk-download-btn", "n_clicks")],
)
def rework_download(n_clicks):
    return dcc.send_file(SSP_DIR / "program" / "TEMPLATE" / "changelist.xlsx")


@callback(
    Output("callback-output", "children"),
    Output("rwk-qty", "disabled"),
    Input("dash-uploader", "isCompleted"),
    State("dash-uploader", "fileNames"),
    State("dash-uploader", "upload_id"),
    State("rwk-qty", "value"),
    State("auto-nt", "value"),
)
def callback_on_completion(iscompleted, filenames, upload_id, rwk_qty, user):
    if (rwk_qty is None) or (user is None):
        return html.Div("请先输入工程师,重工台数"), no_update
    if not iscompleted:
        raise PreventUpdate
    if filenames is None:
        raise PreventUpdate

    file = UPLOAD_FOLDER_ROOT / upload_id / filenames[0]

    df = pd.read_excel(
        file, header=1, dtype=str, keep_default_na=False, engine="openpyxl"
    )
    if not df.columns.difference(
        ["变更类别", "位置号", "料号", "描述", "厂商", "厂商料号", "备注"]
    ).empty:
        return html.Div("重工模板不正确,请下载正确模板后重试"), no_update

    df = df.loc[(df != "").any(1)]
    if ~df["变更类别"].isin(["ADD", "DEL", "CHG"]).all():
        return html.Div("变更类别限CHG,ADD,DEL"), no_update

    if (df["位置号"] == "").any():
        return html.Div("位置号不能为空"), no_update

    df["料号"] = np.where(df["变更类别"] == "DEL", "", df["料号"])
    df["描述"] = np.where(df["变更类别"] == "DEL", "", df["描述"])
    df["厂商"] = np.where(df["变更类别"] == "DEL", "", df["厂商"])
    df["厂商料号"] = np.where(df["变更类别"] == "DEL", "", df["厂商料号"])

    params = df["料号"].unique().tolist()
    ph = ",".join(["%s"] * len(params))
    sql = f"select deltapn,des,mfgname,mfgpn,checkcode \
    from ssp_csg.mat_info where deltapn in ({ph})"
    df1 = read_sql(sql, params=params)
    df1 = df1.drop_duplicates(["deltapn"])
    df = df.merge(df1, left_on=["料号"], right_on=["deltapn"], how="left")
    df["checkcode"] = np.where(df["checkcode"].isnull(), df["料号"], df["checkcode"])
    df["描述"] = np.where(df["描述"] != df["des"], df["des"], df["描述"])
    df["厂商"] = np.where(df["厂商"] != df["mfgname"], df["mfgname"], df["厂商"])
    df["厂商料号"] = np.where(
        df["厂商料号"] != df["mfgpn"], df["mfgpn"], df["厂商料号"]
    )
    params = df["checkcode"].unique().tolist()
    ph = ",".join(["%s"] * len(params))

    sql = "select * from ssp.user where nt_name=%s"
    df_user = read_sql(sql, params=[user])
    df_user.columns = df_user.columns.str.lower()
    area = df_user["area"].iloc[0]
    dept = df_user["dept"].iloc[0]
    dept_group = dept.split("_")[0]

    sql = f"select id as stock_id,stockno,area,checkcode,qty from ssp.stock \
        where checkcode in ({ph}) and (limituse=%s or limituse like %s)"

    stock = read_sql(sql, params=params + ["all", f"%{dept_group}%"])
    sh_stock = (
        stock.query('area=="SH"')
        .reindex(columns=["checkcode", "qty", "stock_id"])
        .rename(columns={"qty": "上海库存", "stock_id": "sh_stock_id"})
    ).drop_duplicates(["checkcode"])

    hz_stock = (
        stock.query('area=="HZ"')
        .reindex(columns=["checkcode", "qty", "stock_id"])
        .rename(columns={"qty": "杭州库存", "stock_id": "hz_stock_id"})
    ).drop_duplicates(["checkcode"])

    wh_stock = (
        stock.query('area=="WH"')
        .reindex(columns=["checkcode", "qty", "stock_id"])
        .rename(columns={"qty": "武汉库存", "stock_id": "wh_stock_id"})
    ).drop_duplicates(["checkcode"])

    grp = df.groupby("料号", as_index=False).agg(
        QPA=pd.NamedAgg(column="料号", aggfunc="count")
    )

    df["位置号"] = df["位置号"] + "-" + df["变更类别"]
    df = df.groupby(["料号", "checkcode"], as_index=False).agg(
        {
            "位置号": lambda x: ",".join(x),
            "备注": lambda x: ",".join(x),
            "描述": "first",
            "厂商": "first",
            "厂商料号": "first",
            "checkcode": "first",
        }
    )

    df = (
        df.merge(sh_stock, on="checkcode", how="left")
        .merge(hz_stock, on="checkcode", how="left")
        .merge(wh_stock, on="checkcode", how="left")
        .merge(grp, on="料号", how="left")
    )
    df["需求数量"] = df["QPA"] * rwk_qty + 2
    df["需求数量"] = np.where(df["料号"] == "", 0, df["需求数量"])
    df["上海库存"] = df["上海库存"].fillna(0).astype(int)
    df["杭州库存"] = df["杭州库存"].fillna(0).astype(int)
    df["武汉库存"] = df["武汉库存"].fillna(0).astype(int)
    df["sh_stock_id"] = df["sh_stock_id"].fillna(0).astype(int)
    df["hz_stock_id"] = df["hz_stock_id"].fillna(0).astype(int)
    df["wh_stock_id"] = df["wh_stock_id"].fillna(0).astype(int)

    if area == "HZ":
        df["需求数量"] = df["需求数量"] + 3
        df["stock_id"] = np.where(
            df["杭州库存"] < df["需求数量"], df["sh_stock_id"], df["hz_stock_id"]
        )
    elif area == "WH":
        df["需求数量"] = df["需求数量"] + 3
        df["stock_id"] = np.where(
            df["武汉库存"] < df["需求数量"], df["sh_stock_id"], df["wh_stock_id"]
        )
    else:
        df["杭州库存"] = 0
        df["stock_id"] = df["sh_stock_id"]

    df = df.merge(
        stock[["stock_id", "qty", "area", "stockno"]], on="stock_id", how="left"
    )
    df["qty"] = df["qty"].fillna(0)
    df["area"] = df["area"].fillna(area)
    df["库存状态"] = np.where(df["qty"] < df["需求数量"], "库存不足", "库存充足")

    df["备注"] = np.where(df["备注"].str.match("^,+$"), "", df["备注"])

    table_columns = (
        "位置号",
        "料号",
        "描述",
        "厂商",
        "厂商料号",
        "备注",
        "QPA",
        "需求数量",
        "上海库存",
        "杭州库存",
        "武汉库存",
        "库存状态",
        "RD反馈",
        "checkcode",
        "stock_id",
        "area",
        "stockno",
    )

    output = dmc.Stack(
        [
            dash_table.DataTable(
                data=df.to_dict(orient="records"),
                columns=[
                    {"name": i, "id": i, "presentation": "dropdown"}
                    for i in table_columns
                ],
                hidden_columns=["checkcode", "stock_id", "area", "stockno"],
                editable=True,
                # row_deletable=True,
                id="rwk-table",
                style_header=style_header,
                style_cell=style_cell,
                dropdown={
                    "RD反馈": {
                        "options": [
                            {"label": i, "value": i} for i in ["自行提供"]
                        ]  # ,'申请购买'
                    },
                },
                style_data_conditional=[
                    {
                        "if": {
                            "filter_query": "{库存状态} contains 库存充足",
                            "column_id": "库存状态",
                        },
                        "backgroundColor": "#2ecc71",
                    },
                    {
                        "if": {
                            "filter_query": "{库存状态} contains 库存不足",
                            "column_id": "库存状态",
                        },
                        "backgroundColor": "#e74c3c",
                    },
                ],
                style_cell_conditional=[
                    {"if": {"column_id": "变更类别"}, "width": "60px"},
                    {"if": {"column_id": "位置号"}, "width": "50px"},
                    {"if": {"column_id": "料号"}, "width": "80px"},
                    {"if": {"column_id": "描述"}, "width": "250px"},
                    {"if": {"column_id": "厂商"}, "width": "50px"},
                    {"if": {"column_id": "厂商料号"}, "width": "150px"},
                    {"if": {"column_id": "QPA"}, "width": "50px"},
                    {"if": {"column_id": "需求数量"}, "width": "60px"},
                    {"if": {"column_id": "上海库存"}, "width": "60px"},
                    {"if": {"column_id": "杭州库存"}, "width": "60px"},
                    {"if": {"column_id": "武汉库存"}, "width": "60px"},
                    {"if": {"column_id": "库存状态"}, "width": "200px"},
                    {"if": {"column_id": "RD反馈"}, "width": "100px"},
                ],
                css=[
                    {"selector": ".dash-spreadsheet-menu-item", "rule": "display:none"}
                ],
            ),
            dmc.Group([
                dbc.Alert(id="rwk-alert", is_open=False, fade=True, duration=10000),
                dbc.Button("Submit", color="primary", id="rwk-submit"),
            ],justify="right"),
        ]
    )
    return output, True


@callback(
    Output("rwk-alert", "children"),
    Output("rwk-alert", "is_open"),
    Output("rwk-alert", "color"),
    Output("rwk-submit", "disabled"),
    Input("rwk-submit", "n_clicks"),
    State("auto-nt", "value"),
    State("rwk-table", "data"),
    State("rwk-table", "columns"),
    State("rwk-proj", "value"),
    State("rwk-qty", "value"),
    State("user", "data"),
)
def rework_submit(n_clicks, rd, data, columns, proj, qty, user):
    if proj is None:
        return "请输入机种名称和需求日期", True, "danger", False

    columns = [i["name"] for i in columns]
    df = pd.DataFrame(data, columns=columns)
    df = df.fillna("")
    df["RD反馈"] = df["RD反馈"].fillna("").astype(str)
    c1 = df["库存状态"] == "库存不足"
    c2 = df["RD反馈"] == ""
    if (c1 & c2).any():
        return "库存不足,无法提交", True, "danger", False

    df["需求数量"] = np.where(df["RD反馈"] == "自行提供", 0, df["需求数量"])

    nt_name = user.get("nt_name")

    sql = "select area,dept,dept_id from ssp.user where nt_name=%s"
    df_user = read_sql(sql, params=[rd])
    area = df_user["area"].iloc[0]
    dept = df_user["dept"].iloc[0]
    dept_id = df_user["dept_id"].iloc[0]
    sql = "SELECT RIGHT(PrtNo,7) as maxid FROM ssp.prt \
    where PrtNo like %s ORDER BY id DESC limit 1"
    params = [f"{area}%"]
    conn = pool.connection()
    cu = conn.cursor()
    cu.execute(sql, params)
    res = cu.fetchone()
    smd_area = area[0]
    dip_area = area[0]
    prtno = f"{area}{smd_area}{dip_area}{int(res['maxid'])+1}"
    now = datetime.now()
    if dip_area == "SH":
        smstatus = "Planning"
    else:
        smstatus = "Sent"

    sql = "insert into ssp.prt(project_id,stage,board,prtno,proj,pcbpn,qty,SMStatus,\
        pcbstatus,Dept,EE,AppDate,FSQty,FSDate_Req,Area,dept_id,b_ee,smd_area,dip_area) \
        value(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)"
    params = [
        10000,
        0,
        proj,
        prtno,
        proj,
        "重工",
        qty,
        smstatus,
        now,
        dept,
        rd,
        now,
        qty,
        now,
        area,
        dept_id,
        "X",
        area,
        area,
    ]
    cu.execute(sql, params)

    df["备注"] = df["备注"].str.cat(df["RD反馈"], sep=",")
    df["备注"] = df["备注"].str.replace("^,|,$", "")
    smbom = df[["位置号", "料号", "描述", "厂商", "厂商料号", "备注", "checkcode"]]
    smbom["位置号"] = smbom["位置号"].str.split(",")
    smbom = smbom.explode("位置号")
    smbom.columns = [
        "designno",
        "deltapn",
        "des",
        "mfgname",
        "mfgpn",
        "pcb_remark",
        "checkcode",
    ]
    smbom["prtno"] = prtno
    smbom["packaging"] = "DIP"
    smbom["up_date"] = now
    smbom["des"] = np.where(
        smbom["pcb_remark"] != "", smbom["pcb_remark"], smbom["des"]
    )

    fields = ",".join(smbom.columns)
    ph = ",".join(["%s"] * smbom.columns.size)

    sql = f"insert into ssp.smbom({fields}) value({ph})"
    params = smbom.values.tolist()
    cu.executemany(sql, params)

    stockout = df[["料号", "需求数量", "checkcode", "area", "stock_id", "stockno"]]
    stockout = stockout.drop_duplicates("checkcode")
    stockout = stockout.loc[stockout["需求数量"] > 0]
    stockout = stockout.rename(columns={"料号": "deltapn", "需求数量": "qty"})
    stockout["stockoutdate"] = now
    stockout["stockoutdate2"] = now
    stockout["lable"] = now
    stockout["prtno"] = prtno
    stockout["dept"] = dept
    stockout["type"] = "SM"
    stockout["owner1"] = rd
    stockout["source"] = "bom"
    stockout["dept_id"] = dept_id

    fields = ",".join(stockout.columns)
    ph = ",".join(["%s"] * stockout.columns.size)

    sql = f"insert into ssp.stockout({fields}) value({ph})"
    params = stockout.values.tolist()
    cu.executemany(sql, params)

    sql = "update ssp.stock set qty=qty-%s where id=%s"
    params = df.loc[df["需求数量"] > 0, ["需求数量", "stock_id"]].values.tolist()
    cu.executemany(sql, params)

    conn.commit()
    cu.close()
    conn.close()

    # =========邮件通知===========
    if area == "SH":
        to = [rd, nt_name, "yingzhi.zhang"]
    elif area == "WH":
        to = [rd, nt_name, "hq.cai", "TONGCHUN.SONG", "FANWEI.XIE"]
    else:
        to = [rd, nt_name, "qin.hong", "cc.zhu", "lili.fang", "weiming.li"]

    to = ";".join(f"{i}@deltaww.com" for i in to)
    subject = f"【重工需求】项目号:{prtno},数量:{qty}"
    body = f"Dear {rd}:<br>重工需求已生成，请提供Rework Instruction"
    bg_mail(to, subject, body)

    return "重工需求提交成功", True, "success", True
